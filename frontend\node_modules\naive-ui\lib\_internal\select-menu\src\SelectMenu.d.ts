import type { SelectGroupOption, SelectIgnoredOption, SelectOption, SelectTreeMate, Value } from '../../../select/src/interface';
import type { ScrollbarInst } from '../../scrollbar';
import type { NodeProps, RenderLabel, RenderOption, Size } from './interface';
import { type TreeNode } from 'treemate';
import { type PropType } from 'vue';
import { type VirtualListInst } from 'vueuc';
declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    clsPrefix: {
        type: StringConstructor;
        required: true;
    };
    scrollable: {
        type: BooleanConstructor;
        default: boolean;
    };
    treeMate: {
        type: PropType<SelectTreeMate>;
        required: true;
    };
    multiple: BooleanConstructor;
    size: {
        type: PropType<Size>;
        default: string;
    };
    value: {
        type: PropType<Value | null>;
        default: null;
    };
    autoPending: BooleanConstructor;
    virtualScroll: {
        type: BooleanConstructor;
        default: boolean;
    };
    show: {
        type: BooleanConstructor;
        default: boolean;
    };
    labelField: {
        type: StringConstructor;
        default: string;
    };
    valueField: {
        type: StringConstructor;
        default: string;
    };
    loading: BooleanConstructor;
    focusable: BooleanConstructor;
    renderLabel: PropType<RenderLabel>;
    renderOption: PropType<RenderOption>;
    nodeProps: PropType<NodeProps>;
    showCheckmark: {
        type: BooleanConstructor;
        default: boolean;
    };
    onMousedown: PropType<(e: MouseEvent) => void>;
    onScroll: PropType<(e: Event) => void>;
    onFocus: PropType<(e: FocusEvent) => void>;
    onBlur: PropType<(e: FocusEvent) => void>;
    onKeyup: PropType<(e: KeyboardEvent) => void>;
    onKeydown: PropType<(e: KeyboardEvent) => void>;
    onTabOut: PropType<() => void>;
    onMouseenter: PropType<(e: MouseEvent) => void>;
    onMouseleave: PropType<(e: MouseEvent) => void>;
    onResize: PropType<() => void>;
    resetMenuOnOptionsChange: {
        type: BooleanConstructor;
        default: boolean;
    };
    inlineThemeDisabled: BooleanConstructor;
    onToggle: PropType<(tmNode: TreeNode<SelectOption>) => void>;
    theme: PropType<import("../../../_mixins").Theme<"InternalSelectMenu", {
        optionFontSizeTiny: string;
        optionFontSizeSmall: string;
        optionFontSizeMedium: string;
        optionFontSizeLarge: string;
        optionFontSizeHuge: string;
        optionHeightTiny: string;
        optionHeightSmall: string;
        optionHeightMedium: string;
        optionHeightLarge: string;
        optionHeightHuge: string;
        borderRadius: string;
        color: string;
        groupHeaderTextColor: string;
        actionDividerColor: string;
        optionTextColor: string;
        optionTextColorPressed: string;
        optionTextColorDisabled: string;
        optionTextColorActive: string;
        optionOpacityDisabled: string;
        optionCheckColor: string;
        optionColorPending: string;
        optionColorActive: string;
        optionColorActivePending: string;
        actionTextColor: string;
        loadingColor: string;
        height: string;
        paddingTiny: string;
        paddingSmall: string;
        paddingMedium: string;
        paddingLarge: string;
        paddingHuge: string;
        optionPaddingTiny: string;
        optionPaddingSmall: string;
        optionPaddingMedium: string;
        optionPaddingLarge: string;
        optionPaddingHuge: string;
        loadingSize: string;
    }, {
        Scrollbar: import("../../../_mixins").Theme<"Scrollbar", {
            height: string;
            width: string;
            borderRadius: string;
            color: string;
            colorHover: string;
            railInsetHorizontalBottom: string;
            railInsetHorizontalTop: string;
            railInsetVerticalRight: string;
            railInsetVerticalLeft: string;
            railColor: string;
        }, any>;
        Empty: import("../../../_mixins").Theme<"Empty", {
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            fontSizeHuge: string;
            textColor: string;
            iconColor: string;
            extraTextColor: string;
            iconSizeTiny: string;
            iconSizeSmall: string;
            iconSizeMedium: string;
            iconSizeLarge: string;
            iconSizeHuge: string;
        }, any>;
    }>>;
    themeOverrides: PropType<import("../../../_mixins/use-theme").ExtractThemeOverrides<import("../../../_mixins").Theme<"InternalSelectMenu", {
        optionFontSizeTiny: string;
        optionFontSizeSmall: string;
        optionFontSizeMedium: string;
        optionFontSizeLarge: string;
        optionFontSizeHuge: string;
        optionHeightTiny: string;
        optionHeightSmall: string;
        optionHeightMedium: string;
        optionHeightLarge: string;
        optionHeightHuge: string;
        borderRadius: string;
        color: string;
        groupHeaderTextColor: string;
        actionDividerColor: string;
        optionTextColor: string;
        optionTextColorPressed: string;
        optionTextColorDisabled: string;
        optionTextColorActive: string;
        optionOpacityDisabled: string;
        optionCheckColor: string;
        optionColorPending: string;
        optionColorActive: string;
        optionColorActivePending: string;
        actionTextColor: string;
        loadingColor: string;
        height: string;
        paddingTiny: string;
        paddingSmall: string;
        paddingMedium: string;
        paddingLarge: string;
        paddingHuge: string;
        optionPaddingTiny: string;
        optionPaddingSmall: string;
        optionPaddingMedium: string;
        optionPaddingLarge: string;
        optionPaddingHuge: string;
        loadingSize: string;
    }, {
        Scrollbar: import("../../../_mixins").Theme<"Scrollbar", {
            height: string;
            width: string;
            borderRadius: string;
            color: string;
            colorHover: string;
            railInsetHorizontalBottom: string;
            railInsetHorizontalTop: string;
            railInsetVerticalRight: string;
            railInsetVerticalLeft: string;
            railColor: string;
        }, any>;
        Empty: import("../../../_mixins").Theme<"Empty", {
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            fontSizeHuge: string;
            textColor: string;
            iconColor: string;
            extraTextColor: string;
            iconSizeTiny: string;
            iconSizeSmall: string;
            iconSizeMedium: string;
            iconSizeLarge: string;
            iconSizeHuge: string;
        }, any>;
    }>>>;
    builtinThemeOverrides: PropType<import("../../../_mixins/use-theme").ExtractThemeOverrides<import("../../../_mixins").Theme<"InternalSelectMenu", {
        optionFontSizeTiny: string;
        optionFontSizeSmall: string;
        optionFontSizeMedium: string;
        optionFontSizeLarge: string;
        optionFontSizeHuge: string;
        optionHeightTiny: string;
        optionHeightSmall: string;
        optionHeightMedium: string;
        optionHeightLarge: string;
        optionHeightHuge: string;
        borderRadius: string;
        color: string;
        groupHeaderTextColor: string;
        actionDividerColor: string;
        optionTextColor: string;
        optionTextColorPressed: string;
        optionTextColorDisabled: string;
        optionTextColorActive: string;
        optionOpacityDisabled: string;
        optionCheckColor: string;
        optionColorPending: string;
        optionColorActive: string;
        optionColorActivePending: string;
        actionTextColor: string;
        loadingColor: string;
        height: string;
        paddingTiny: string;
        paddingSmall: string;
        paddingMedium: string;
        paddingLarge: string;
        paddingHuge: string;
        optionPaddingTiny: string;
        optionPaddingSmall: string;
        optionPaddingMedium: string;
        optionPaddingLarge: string;
        optionPaddingHuge: string;
        loadingSize: string;
    }, {
        Scrollbar: import("../../../_mixins").Theme<"Scrollbar", {
            height: string;
            width: string;
            borderRadius: string;
            color: string;
            colorHover: string;
            railInsetHorizontalBottom: string;
            railInsetHorizontalTop: string;
            railInsetVerticalRight: string;
            railInsetVerticalLeft: string;
            railColor: string;
        }, any>;
        Empty: import("../../../_mixins").Theme<"Empty", {
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            fontSizeHuge: string;
            textColor: string;
            iconColor: string;
            extraTextColor: string;
            iconSizeTiny: string;
            iconSizeSmall: string;
            iconSizeMedium: string;
            iconSizeLarge: string;
            iconSizeHuge: string;
        }, any>;
    }>>>;
}>, {
    selfRef: import("vue").Ref<HTMLElement | null>;
    getPendingTmNode: () => TreeNode<import("../../../select/src/interface").SelectBaseOption> | null;
    prev: () => void;
    next: () => void;
    mergedTheme: import("vue").ComputedRef<{
        common: import("../../..").ThemeCommonVars;
        self: {
            optionFontSizeTiny: string;
            optionFontSizeSmall: string;
            optionFontSizeMedium: string;
            optionFontSizeLarge: string;
            optionFontSizeHuge: string;
            optionHeightTiny: string;
            optionHeightSmall: string;
            optionHeightMedium: string;
            optionHeightLarge: string;
            optionHeightHuge: string;
            borderRadius: string;
            color: string;
            groupHeaderTextColor: string;
            actionDividerColor: string;
            optionTextColor: string;
            optionTextColorPressed: string;
            optionTextColorDisabled: string;
            optionTextColorActive: string;
            optionOpacityDisabled: string;
            optionCheckColor: string;
            optionColorPending: string;
            optionColorActive: string;
            optionColorActivePending: string;
            actionTextColor: string;
            loadingColor: string;
            height: string;
            paddingTiny: string;
            paddingSmall: string;
            paddingMedium: string;
            paddingLarge: string;
            paddingHuge: string;
            optionPaddingTiny: string;
            optionPaddingSmall: string;
            optionPaddingMedium: string;
            optionPaddingLarge: string;
            optionPaddingHuge: string;
            loadingSize: string;
        };
        peers: {
            Scrollbar: import("../../../_mixins").Theme<"Scrollbar", {
                height: string;
                width: string;
                borderRadius: string;
                color: string;
                colorHover: string;
                railInsetHorizontalBottom: string;
                railInsetHorizontalTop: string;
                railInsetVerticalRight: string;
                railInsetVerticalLeft: string;
                railColor: string;
            }, any>;
            Empty: import("../../../_mixins").Theme<"Empty", {
                fontSizeTiny: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                fontSizeHuge: string;
                textColor: string;
                iconColor: string;
                extraTextColor: string;
                iconSizeTiny: string;
                iconSizeSmall: string;
                iconSizeMedium: string;
                iconSizeLarge: string;
                iconSizeHuge: string;
            }, any>;
        };
        peerOverrides: {
            Scrollbar?: {
                peers?: {
                    [x: string]: any;
                } | undefined;
            } | undefined;
            Empty?: {
                peers?: {
                    [x: string]: any;
                } | undefined;
            } | undefined;
        };
    }>;
    mergedClsPrefix: import("vue").Ref<string, string>;
    rtlEnabled: import("vue").Ref<import("../../../config-provider/src/internal-interface").RtlItem | undefined, import("../../../config-provider/src/internal-interface").RtlItem | undefined> | undefined;
    virtualListRef: import("vue").Ref<{
        listElRef: HTMLElement;
        itemsElRef: HTMLElement | null;
        scrollTo: import("vueuc").VVirtualListScrollTo;
    } | null, VirtualListInst | {
        listElRef: HTMLElement;
        itemsElRef: HTMLElement | null;
        scrollTo: import("vueuc").VVirtualListScrollTo;
    } | null>;
    scrollbarRef: import("vue").Ref<{
        $el: HTMLElement;
        containerRef: HTMLElement | null;
        contentRef: HTMLElement | null;
        containerScrollTop: number;
        syncUnifiedContainer: () => void;
        scrollTo: import("../../scrollbar/src/Scrollbar").ScrollTo;
        scrollBy: import("../../scrollbar/src/Scrollbar").ScrollBy;
        sync: () => void;
        handleMouseEnterWrapper: () => void;
        handleMouseLeaveWrapper: () => void;
    } | null, ScrollbarInst | {
        $el: HTMLElement;
        containerRef: HTMLElement | null;
        contentRef: HTMLElement | null;
        containerScrollTop: number;
        syncUnifiedContainer: () => void;
        scrollTo: import("../../scrollbar/src/Scrollbar").ScrollTo;
        scrollBy: import("../../scrollbar/src/Scrollbar").ScrollBy;
        sync: () => void;
        handleMouseEnterWrapper: () => void;
        handleMouseLeaveWrapper: () => void;
    } | null>;
    itemSize: import("vue").ComputedRef<number>;
    padding: import("vue").ComputedRef<import("seemly").Margin>;
    flattenedNodes: import("vue").ComputedRef<TreeNode<import("../../../select/src/interface").SelectBaseOption<string | number, string | ((option: import("../../../select/src/interface").SelectBaseOption<string | number, string | any>, selected: boolean) => import("vue").VNodeChild)>, SelectGroupOption, SelectIgnoredOption>[]>;
    empty: import("vue").ComputedRef<boolean>;
    virtualListContainer(): HTMLElement | undefined;
    virtualListContent(): HTMLElement | null | undefined;
    doScroll: (e: Event) => void;
    handleFocusin: (e: FocusEvent) => void;
    handleFocusout: (e: FocusEvent) => void;
    handleKeyUp: (e: KeyboardEvent) => void;
    handleKeyDown: (e: KeyboardEvent) => void;
    handleMouseDown: (e: MouseEvent) => void;
    handleVirtualListResize: () => void;
    handleVirtualListScroll: (e: Event) => void;
    cssVars: import("vue").ComputedRef<{
        '--n-height': string;
        '--n-action-divider-color': string;
        '--n-action-text-color': string;
        '--n-bezier': string;
        '--n-border-radius': string;
        '--n-color': string;
        '--n-option-font-size': string;
        '--n-group-header-text-color': string;
        '--n-option-check-color': string;
        '--n-option-color-pending': string;
        '--n-option-color-active': string;
        '--n-option-color-active-pending': string;
        '--n-option-height': string;
        '--n-option-opacity-disabled': string;
        '--n-option-text-color': string;
        '--n-option-text-color-active': string;
        '--n-option-text-color-disabled': string;
        '--n-option-text-color-pressed': string;
        '--n-option-padding': string;
        '--n-option-padding-left': string;
        '--n-option-padding-right': string;
        '--n-loading-color': string;
        '--n-loading-size': string;
    }> | undefined;
    themeClass: import("vue").Ref<string, string> | undefined;
    onRender: (() => void) | undefined;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    clsPrefix: {
        type: StringConstructor;
        required: true;
    };
    scrollable: {
        type: BooleanConstructor;
        default: boolean;
    };
    treeMate: {
        type: PropType<SelectTreeMate>;
        required: true;
    };
    multiple: BooleanConstructor;
    size: {
        type: PropType<Size>;
        default: string;
    };
    value: {
        type: PropType<Value | null>;
        default: null;
    };
    autoPending: BooleanConstructor;
    virtualScroll: {
        type: BooleanConstructor;
        default: boolean;
    };
    show: {
        type: BooleanConstructor;
        default: boolean;
    };
    labelField: {
        type: StringConstructor;
        default: string;
    };
    valueField: {
        type: StringConstructor;
        default: string;
    };
    loading: BooleanConstructor;
    focusable: BooleanConstructor;
    renderLabel: PropType<RenderLabel>;
    renderOption: PropType<RenderOption>;
    nodeProps: PropType<NodeProps>;
    showCheckmark: {
        type: BooleanConstructor;
        default: boolean;
    };
    onMousedown: PropType<(e: MouseEvent) => void>;
    onScroll: PropType<(e: Event) => void>;
    onFocus: PropType<(e: FocusEvent) => void>;
    onBlur: PropType<(e: FocusEvent) => void>;
    onKeyup: PropType<(e: KeyboardEvent) => void>;
    onKeydown: PropType<(e: KeyboardEvent) => void>;
    onTabOut: PropType<() => void>;
    onMouseenter: PropType<(e: MouseEvent) => void>;
    onMouseleave: PropType<(e: MouseEvent) => void>;
    onResize: PropType<() => void>;
    resetMenuOnOptionsChange: {
        type: BooleanConstructor;
        default: boolean;
    };
    inlineThemeDisabled: BooleanConstructor;
    onToggle: PropType<(tmNode: TreeNode<SelectOption>) => void>;
    theme: PropType<import("../../../_mixins").Theme<"InternalSelectMenu", {
        optionFontSizeTiny: string;
        optionFontSizeSmall: string;
        optionFontSizeMedium: string;
        optionFontSizeLarge: string;
        optionFontSizeHuge: string;
        optionHeightTiny: string;
        optionHeightSmall: string;
        optionHeightMedium: string;
        optionHeightLarge: string;
        optionHeightHuge: string;
        borderRadius: string;
        color: string;
        groupHeaderTextColor: string;
        actionDividerColor: string;
        optionTextColor: string;
        optionTextColorPressed: string;
        optionTextColorDisabled: string;
        optionTextColorActive: string;
        optionOpacityDisabled: string;
        optionCheckColor: string;
        optionColorPending: string;
        optionColorActive: string;
        optionColorActivePending: string;
        actionTextColor: string;
        loadingColor: string;
        height: string;
        paddingTiny: string;
        paddingSmall: string;
        paddingMedium: string;
        paddingLarge: string;
        paddingHuge: string;
        optionPaddingTiny: string;
        optionPaddingSmall: string;
        optionPaddingMedium: string;
        optionPaddingLarge: string;
        optionPaddingHuge: string;
        loadingSize: string;
    }, {
        Scrollbar: import("../../../_mixins").Theme<"Scrollbar", {
            height: string;
            width: string;
            borderRadius: string;
            color: string;
            colorHover: string;
            railInsetHorizontalBottom: string;
            railInsetHorizontalTop: string;
            railInsetVerticalRight: string;
            railInsetVerticalLeft: string;
            railColor: string;
        }, any>;
        Empty: import("../../../_mixins").Theme<"Empty", {
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            fontSizeHuge: string;
            textColor: string;
            iconColor: string;
            extraTextColor: string;
            iconSizeTiny: string;
            iconSizeSmall: string;
            iconSizeMedium: string;
            iconSizeLarge: string;
            iconSizeHuge: string;
        }, any>;
    }>>;
    themeOverrides: PropType<import("../../../_mixins/use-theme").ExtractThemeOverrides<import("../../../_mixins").Theme<"InternalSelectMenu", {
        optionFontSizeTiny: string;
        optionFontSizeSmall: string;
        optionFontSizeMedium: string;
        optionFontSizeLarge: string;
        optionFontSizeHuge: string;
        optionHeightTiny: string;
        optionHeightSmall: string;
        optionHeightMedium: string;
        optionHeightLarge: string;
        optionHeightHuge: string;
        borderRadius: string;
        color: string;
        groupHeaderTextColor: string;
        actionDividerColor: string;
        optionTextColor: string;
        optionTextColorPressed: string;
        optionTextColorDisabled: string;
        optionTextColorActive: string;
        optionOpacityDisabled: string;
        optionCheckColor: string;
        optionColorPending: string;
        optionColorActive: string;
        optionColorActivePending: string;
        actionTextColor: string;
        loadingColor: string;
        height: string;
        paddingTiny: string;
        paddingSmall: string;
        paddingMedium: string;
        paddingLarge: string;
        paddingHuge: string;
        optionPaddingTiny: string;
        optionPaddingSmall: string;
        optionPaddingMedium: string;
        optionPaddingLarge: string;
        optionPaddingHuge: string;
        loadingSize: string;
    }, {
        Scrollbar: import("../../../_mixins").Theme<"Scrollbar", {
            height: string;
            width: string;
            borderRadius: string;
            color: string;
            colorHover: string;
            railInsetHorizontalBottom: string;
            railInsetHorizontalTop: string;
            railInsetVerticalRight: string;
            railInsetVerticalLeft: string;
            railColor: string;
        }, any>;
        Empty: import("../../../_mixins").Theme<"Empty", {
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            fontSizeHuge: string;
            textColor: string;
            iconColor: string;
            extraTextColor: string;
            iconSizeTiny: string;
            iconSizeSmall: string;
            iconSizeMedium: string;
            iconSizeLarge: string;
            iconSizeHuge: string;
        }, any>;
    }>>>;
    builtinThemeOverrides: PropType<import("../../../_mixins/use-theme").ExtractThemeOverrides<import("../../../_mixins").Theme<"InternalSelectMenu", {
        optionFontSizeTiny: string;
        optionFontSizeSmall: string;
        optionFontSizeMedium: string;
        optionFontSizeLarge: string;
        optionFontSizeHuge: string;
        optionHeightTiny: string;
        optionHeightSmall: string;
        optionHeightMedium: string;
        optionHeightLarge: string;
        optionHeightHuge: string;
        borderRadius: string;
        color: string;
        groupHeaderTextColor: string;
        actionDividerColor: string;
        optionTextColor: string;
        optionTextColorPressed: string;
        optionTextColorDisabled: string;
        optionTextColorActive: string;
        optionOpacityDisabled: string;
        optionCheckColor: string;
        optionColorPending: string;
        optionColorActive: string;
        optionColorActivePending: string;
        actionTextColor: string;
        loadingColor: string;
        height: string;
        paddingTiny: string;
        paddingSmall: string;
        paddingMedium: string;
        paddingLarge: string;
        paddingHuge: string;
        optionPaddingTiny: string;
        optionPaddingSmall: string;
        optionPaddingMedium: string;
        optionPaddingLarge: string;
        optionPaddingHuge: string;
        loadingSize: string;
    }, {
        Scrollbar: import("../../../_mixins").Theme<"Scrollbar", {
            height: string;
            width: string;
            borderRadius: string;
            color: string;
            colorHover: string;
            railInsetHorizontalBottom: string;
            railInsetHorizontalTop: string;
            railInsetVerticalRight: string;
            railInsetVerticalLeft: string;
            railColor: string;
        }, any>;
        Empty: import("../../../_mixins").Theme<"Empty", {
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            fontSizeHuge: string;
            textColor: string;
            iconColor: string;
            extraTextColor: string;
            iconSizeTiny: string;
            iconSizeSmall: string;
            iconSizeMedium: string;
            iconSizeLarge: string;
            iconSizeHuge: string;
        }, any>;
    }>>>;
}>> & Readonly<{}>, {
    value: Value | null;
    size: Size;
    show: boolean;
    multiple: boolean;
    loading: boolean;
    focusable: boolean;
    inlineThemeDisabled: boolean;
    scrollable: boolean;
    labelField: string;
    showCheckmark: boolean;
    autoPending: boolean;
    virtualScroll: boolean;
    valueField: string;
    resetMenuOnOptionsChange: boolean;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;

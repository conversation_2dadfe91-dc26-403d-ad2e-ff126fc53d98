<template>
  <div class="media-library">
    <div class="header">
      <h3>素材库</h3>
    </div>
    
    <!-- 上传区域 -->
    <div 
      class="upload-area"
      :class="{ 'drag-over': isDragOver }"
      @drop="handleDrop"
      @dragover.prevent="isDragOver = true"
      @dragleave="isDragOver = false"
      @click="triggerFileInput"
    >
      <input
        ref="fileInput"
        type="file"
        accept="video/*"
        multiple
        style="display: none"
        @change="handleFileSelect"
      />
      
      <div class="upload-content">
        <div class="upload-icon">📁</div>
        <div class="upload-text">
          <div>拖拽视频文件到这里</div>
          <div class="upload-subtext">或点击选择文件</div>
        </div>
      </div>
      
      <!-- 上传进度 -->
      <div v-if="uploadProgress > 0 && uploadProgress < 100" class="upload-progress">
        <div class="progress-bar">
          <div class="progress-fill" :style="{ width: uploadProgress + '%' }"></div>
        </div>
        <div class="progress-text">{{ Math.round(uploadProgress) }}%</div>
      </div>
    </div>
    
    <!-- 媒体列表 -->
    <div class="media-list">
      <div
        v-for="media in mediaLibrary"
        :key="media.id"
        class="media-item"
        draggable="true"
        @dragstart="handleMediaDragStart($event, media)"
        @dragend="handleMediaDragEnd"
      >
        <div class="media-thumbnail">
          <img 
            v-if="media.thumbnail_url" 
            :src="media.thumbnail_url" 
            :alt="media.name"
            @error="handleImageError"
          />
          <div v-else class="thumbnail-placeholder">🎬</div>
        </div>
        
        <div class="media-info">
          <div class="media-name" :title="media.name">{{ media.name }}</div>
          <div class="media-duration">{{ formatDuration(media.duration) }}</div>
          <div class="media-size">{{ formatFileSize(media.file_size) }}</div>
        </div>
        
        <div class="media-actions">
          <button 
            class="delete-btn"
            @click="handleDeleteMedia(media.id)"
            title="删除"
          >
            🗑️
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useEditorStore } from '@/stores/editor'
import { storeToRefs } from 'pinia'
import apiService from '@/services/api'
import type { MediaItem } from '@/types'

const editorStore = useEditorStore()
const { mediaLibrary } = storeToRefs(editorStore)

const fileInput = ref<HTMLInputElement>()
const isDragOver = ref(false)
const uploadProgress = ref(0)

// 格式化时长
const formatDuration = (seconds: number): string => {
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  const sizes = ['B', 'KB', 'MB', 'GB']
  if (bytes === 0) return '0 B'
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
}

// 处理文件拖拽放置
const handleDrop = (event: DragEvent) => {
  event.preventDefault()
  isDragOver.value = false
  
  const files = event.dataTransfer?.files
  if (files) {
    handleFiles(Array.from(files))
  }
}

// 触发文件选择
const triggerFileInput = () => {
  fileInput.value?.click()
}

// 处理文件选择
const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  const files = target.files
  if (files) {
    handleFiles(Array.from(files))
  }
}

// 处理文件上传
const handleFiles = async (files: File[]) => {
  for (const file of files) {
    if (file.type.startsWith('video/')) {
      await uploadFile(file)
    }
  }
}

// 上传单个文件
const uploadFile = async (file: File) => {
  uploadProgress.value = 0
  
  const response = await apiService.uploadVideo(file, (progress) => {
    uploadProgress.value = progress
  })
  
  if (response.data) {
    editorStore.addMediaItem(response.data)
    uploadProgress.value = 0
  } else {
    console.error('Upload failed:', response.error)
    uploadProgress.value = 0
  }
}

// 处理媒体拖拽开始
const handleMediaDragStart = (event: DragEvent, media: MediaItem) => {
  if (event.dataTransfer) {
    event.dataTransfer.setData('application/json', JSON.stringify({
      type: 'media',
      mediaId: media.id,
      data: media
    }))
    event.dataTransfer.effectAllowed = 'copy'
  }
  editorStore.setDragging(true)
}

// 处理媒体拖拽结束
const handleMediaDragEnd = () => {
  editorStore.setDragging(false)
}

// 删除媒体
const handleDeleteMedia = async (mediaId: string) => {
  const response = await apiService.deleteMedia(mediaId)
  if (response.data) {
    editorStore.removeMediaItem(mediaId)
  } else {
    console.error('Delete failed:', response.error)
  }
}

// 处理图片加载错误
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.style.display = 'none'
}

// 加载已有媒体
const loadExistingMedia = async () => {
  const response = await apiService.getMediaList()
  if (response.data) {
    response.data.forEach(media => {
      editorStore.addMediaItem(media)
    })
  }
}

onMounted(() => {
  loadExistingMedia()
})
</script>

<style scoped>
.media-library {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
}

.header {
  margin-bottom: 16px;
}

.header h3 {
  margin: 0;
  color: #ffffff;
  font-size: 16px;
}

.upload-area {
  border: 2px dashed #555;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 16px;
}

.upload-area:hover,
.upload-area.drag-over {
  border-color: #007acc;
  background-color: rgba(0, 122, 204, 0.1);
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.upload-icon {
  font-size: 32px;
}

.upload-text {
  color: #ccc;
}

.upload-subtext {
  font-size: 12px;
  color: #888;
}

.upload-progress {
  margin-top: 12px;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background-color: #333;
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #007acc;
  transition: width 0.3s ease;
}

.progress-text {
  margin-top: 4px;
  font-size: 12px;
  color: #ccc;
}

.media-list {
  flex: 1;
  overflow-y: auto;
}

.media-item {
  display: flex;
  align-items: center;
  padding: 8px;
  margin-bottom: 8px;
  background-color: #333;
  border-radius: 4px;
  cursor: grab;
  transition: background-color 0.2s ease;
}

.media-item:hover {
  background-color: #404040;
}

.media-item:active {
  cursor: grabbing;
}

.media-thumbnail {
  width: 48px;
  height: 32px;
  margin-right: 8px;
  border-radius: 4px;
  overflow: hidden;
  background-color: #222;
  display: flex;
  align-items: center;
  justify-content: center;
}

.media-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.thumbnail-placeholder {
  font-size: 16px;
}

.media-info {
  flex: 1;
  min-width: 0;
}

.media-name {
  font-size: 12px;
  color: #fff;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 2px;
}

.media-duration,
.media-size {
  font-size: 10px;
  color: #888;
}

.media-actions {
  margin-left: 8px;
}

.delete-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 2px;
  transition: background-color 0.2s ease;
}

.delete-btn:hover {
  background-color: rgba(255, 0, 0, 0.2);
}
</style>

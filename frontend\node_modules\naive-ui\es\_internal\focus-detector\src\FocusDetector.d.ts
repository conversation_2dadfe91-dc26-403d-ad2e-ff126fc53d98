import { type PropType } from 'vue';
declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    onFocus: PropType<(e: FocusEvent) => void>;
    onBlur: PropType<(e: FocusEvent) => void>;
}>, () => JSX.Element, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    onFocus: PropType<(e: FocusEvent) => void>;
    onBlur: PropType<(e: FocusEvent) => void>;
}>> & Readonly<{}>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;

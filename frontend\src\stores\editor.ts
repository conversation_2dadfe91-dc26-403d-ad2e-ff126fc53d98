import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import type { MediaItem, TimelineClip, TimelineTrack, DragData } from '@/types'

export const useEditorStore = defineStore('editor', () => {
  // 素材库
  const mediaLibrary = ref<MediaItem[]>([])
  
  // 时间轴轨道
  const tracks = ref<TimelineTrack[]>([
    {
      id: 'video-track-1',
      type: 'video',
      clips: []
    }
  ])
  
  // 播放状态
  const isPlaying = ref(false)
  const currentTime = ref(0)
  const zoom = ref(1) // 时间轴缩放级别
  
  // UI状态
  const selectedClipId = ref<string>()
  const isDragging = ref(false)
  
  // 计算属性
  const timelineDuration = computed(() => {
    let maxDuration = 0
    tracks.value.forEach(track => {
      track.clips.forEach(clip => {
        const clipEnd = clip.startTime + clip.duration
        if (clipEnd > maxDuration) {
          maxDuration = clipEnd
        }
      })
    })
    return Math.max(maxDuration, 60) // 最少60秒
  })
  
  const videoTrack = computed(() => {
    return tracks.value.find(track => track.type === 'video')
  })
  
  const currentClip = computed(() => {
    if (!videoTrack.value) return null

    return videoTrack.value.clips.find(clip => {
      const clipStart = clip.startTime
      const clipEnd = clip.startTime + clip.duration
      // 使用 <= 来包含结束时间，避免边界问题
      return currentTime.value >= clipStart && currentTime.value <= clipEnd
    })
  })
  
  // Actions
  const addMediaItem = (media: MediaItem) => {
    mediaLibrary.value.push(media)
  }
  
  const removeMediaItem = (mediaId: string) => {
    const index = mediaLibrary.value.findIndex(item => item.id === mediaId)
    if (index > -1) {
      mediaLibrary.value.splice(index, 1)
    }
    
    // 同时移除时间轴上相关的片段
    tracks.value.forEach(track => {
      track.clips = track.clips.filter(clip => clip.mediaId !== mediaId)
    })
  }
  
  const addClipToTrack = (trackId: string, clip: TimelineClip) => {
    const track = tracks.value.find(t => t.id === trackId)
    if (track) {
      track.clips.push(clip)
      // 按开始时间排序
      track.clips.sort((a, b) => a.startTime - b.startTime)
    }
  }
  
  const removeClip = (clipId: string) => {
    tracks.value.forEach(track => {
      const index = track.clips.findIndex(clip => clip.id === clipId)
      if (index > -1) {
        track.clips.splice(index, 1)
      }
    })
  }
  
  const updateClip = (clipId: string, updates: Partial<TimelineClip>) => {
    tracks.value.forEach(track => {
      const clip = track.clips.find(c => c.id === clipId)
      if (clip) {
        Object.assign(clip, updates)
      }
    })
  }
  
  const setCurrentTime = (time: number) => {
    currentTime.value = Math.max(0, Math.min(time, timelineDuration.value))
  }
  
  const setPlaying = (playing: boolean) => {
    isPlaying.value = playing
  }
  
  const setSelectedClip = (clipId?: string) => {
    selectedClipId.value = clipId
  }
  
  const setDragging = (dragging: boolean) => {
    isDragging.value = dragging
  }
  
  const setZoom = (zoomLevel: number) => {
    zoom.value = Math.max(0.1, Math.min(zoomLevel, 5))
  }
  
  // 创建新片段的辅助函数
  const createClipFromMedia = (mediaId: string, startTime: number): TimelineClip => {
    const media = mediaLibrary.value.find(m => m.id === mediaId)
    if (!media) {
      throw new Error('Media not found')
    }
    
    return {
      id: `clip-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      mediaId,
      startTime,
      duration: media.duration,
      trimStart: 0,
      trimEnd: media.duration
    }
  }
  
  // 查找时间轴上指定时间点的可放置位置
  const findDropPosition = (trackId: string, time: number): number => {
    const track = tracks.value.find(t => t.id === trackId)
    if (!track) return time
    
    // 简单实现：直接返回指定时间
    // 后续可以添加碰撞检测和自动对齐逻辑
    return time
  }
  
  return {
    // State
    mediaLibrary,
    tracks,
    isPlaying,
    currentTime,
    zoom,
    selectedClipId,
    isDragging,
    
    // Computed
    timelineDuration,
    videoTrack,
    currentClip,
    
    // Actions
    addMediaItem,
    removeMediaItem,
    addClipToTrack,
    removeClip,
    updateClip,
    setCurrentTime,
    setPlaying,
    setSelectedClip,
    setDragging,
    setZoom,
    createClipFromMedia,
    findDropPosition
  }
})

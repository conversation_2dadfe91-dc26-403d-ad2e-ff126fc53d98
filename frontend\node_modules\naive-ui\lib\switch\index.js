"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.switchProps = exports.NSwitch = void 0;
var Switch_1 = require("./src/Switch");
Object.defineProperty(exports, "NSwitch", { enumerable: true, get: function () { return __importDefault(Switch_1).default; } });
Object.defineProperty(exports, "switchProps", { enumerable: true, get: function () { return Switch_1.switchProps; } });

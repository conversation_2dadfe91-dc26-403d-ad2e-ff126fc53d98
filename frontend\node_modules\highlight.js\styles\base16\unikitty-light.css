pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Unikitty Light
  Author: <PERSON> (@joshwlewis)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme unikitty-light
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #ffffff  Default Background
base01  #e1e1e2  Lighter Background (Used for status bars, line number and folding marks)
base02  #c4c3c5  Selection Background
base03  #a7a5a8  Comments, Invisibles, Line Highlighting
base04  #89878b  Dark Foreground (Used for status bars)
base05  #6c696e  Default Foreground, Caret, Delimiters, Operators
base06  #4f4b51  Light Foreground (Not often used)
base07  #322d34  Light Background (Not often used)
base08  #d8137f  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #d65407  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #dc8a0e  Classes, Markup Bold, Search Text Background
base0B  #17ad98  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #149bda  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #775dff  Functions, Methods, Attribute IDs, Headings
base0E  #aa17e6  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #e013d0  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #6c696e;
  background: #ffffff
}
.hljs::selection,
.hljs ::selection {
  background-color: #c4c3c5;
  color: #6c696e
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #a7a5a8 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #a7a5a8
}
/* base04 - #89878b -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #89878b
}
/* base05 - #6c696e -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #6c696e
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #d8137f
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #d65407
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #dc8a0e
}
.hljs-strong {
  font-weight: bold;
  color: #dc8a0e
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #17ad98
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #149bda
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #775dff
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #aa17e6
}
.hljs-emphasis {
  color: #aa17e6;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #e013d0
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}
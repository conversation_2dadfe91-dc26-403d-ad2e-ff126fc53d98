"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const _common_1 = __importDefault(require("./_common"));
function self() {
    return _common_1.default;
}
const spaceLight = {
    name: 'Space',
    self
};
exports.default = spaceLight;

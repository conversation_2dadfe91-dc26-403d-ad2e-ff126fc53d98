import type { PropType } from 'vue';
declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    onResizeStart: FunctionConstructor;
    onResize: PropType<(displacementX: number) => void>;
    onResizeEnd: FunctionConstructor;
}>, {
    mergedClsPrefix: import("vue").Ref<string, string>;
    active: import("vue").Ref<boolean, boolean>;
    handleMousedown: (e: MouseEvent) => void;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    onResizeStart: FunctionConstructor;
    onResize: PropType<(displacementX: number) => void>;
    onResizeEnd: FunctionConstructor;
}>> & Readonly<{}>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;

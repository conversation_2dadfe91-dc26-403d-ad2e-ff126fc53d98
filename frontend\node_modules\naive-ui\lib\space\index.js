"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.spaceProps = exports.NSpace = void 0;
var Space_1 = require("./src/Space");
Object.defineProperty(exports, "NSpace", { enumerable: true, get: function () { return __importDefault(Space_1).default; } });
Object.defineProperty(exports, "spaceProps", { enumerable: true, get: function () { return Space_1.spaceProps; } });

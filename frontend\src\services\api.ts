import type { MediaItem, ApiResponse } from '@/types'

const API_BASE_URL = 'http://localhost:8001'

class ApiService {
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    try {
      const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        ...options,
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      return { data }
    } catch (error) {
      console.error('API request failed:', error)
      return { error: error instanceof Error ? error.message : 'Unknown error' }
    }
  }

  async uploadVideo(file: File, onProgress?: (progress: number) => void): Promise<ApiResponse<MediaItem>> {
    try {
      const formData = new FormData()
      formData.append('file', file)

      const xhr = new XMLHttpRequest()
      
      return new Promise((resolve) => {
        xhr.upload.addEventListener('progress', (event) => {
          if (event.lengthComputable && onProgress) {
            const progress = (event.loaded / event.total) * 100
            onProgress(progress)
          }
        })

        xhr.addEventListener('load', () => {
          if (xhr.status === 200) {
            try {
              const data = JSON.parse(xhr.responseText)
              resolve({ data })
            } catch (error) {
              resolve({ error: 'Failed to parse response' })
            }
          } else {
            resolve({ error: `Upload failed with status: ${xhr.status}` })
          }
        })

        xhr.addEventListener('error', () => {
          resolve({ error: 'Upload failed' })
        })

        xhr.open('POST', `${API_BASE_URL}/api/upload`)
        xhr.send(formData)
      })
    } catch (error) {
      return { error: error instanceof Error ? error.message : 'Upload failed' }
    }
  }

  async getMediaList(): Promise<ApiResponse<MediaItem[]>> {
    return this.request<MediaItem[]>('/api/media')
  }

  async getMediaInfo(mediaId: string): Promise<ApiResponse<MediaItem>> {
    return this.request<MediaItem>(`/api/media/${mediaId}`)
  }

  async deleteMedia(mediaId: string): Promise<ApiResponse<{ message: string }>> {
    return this.request<{ message: string }>(`/api/media/${mediaId}`, {
      method: 'DELETE',
    })
  }

  getProxyUrl(mediaId: string): string {
    return `${API_BASE_URL}/storage/proxies/${mediaId}_proxy.mp4`
  }

  getThumbnailUrl(mediaId: string): string {
    return `${API_BASE_URL}/storage/thumbnails/${mediaId}_thumb.png`
  }
}

export const apiService = new ApiService()
export default apiService

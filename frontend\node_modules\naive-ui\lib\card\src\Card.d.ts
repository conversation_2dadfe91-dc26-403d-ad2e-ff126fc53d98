import type { ExtractPublicPropTypes, MaybeArray } from '../../_utils';
import { type CSSProperties, type PropType, type SlotsType, type VNode, type VNodeChild } from 'vue';
export interface CardSegmented {
    content?: boolean | 'soft';
    footer?: boolean | 'soft';
    action?: boolean | 'soft';
}
export declare const cardBaseProps: {
    readonly title: PropType<string | (() => VNodeChild)>;
    readonly contentClass: StringConstructor;
    readonly contentStyle: PropType<CSSProperties | string>;
    readonly headerClass: StringConstructor;
    readonly headerStyle: PropType<CSSProperties | string>;
    readonly headerExtraClass: StringConstructor;
    readonly headerExtraStyle: PropType<CSSProperties | string>;
    readonly footerClass: StringConstructor;
    readonly footerStyle: PropType<CSSProperties | string>;
    readonly embedded: BooleanConstructor;
    readonly segmented: {
        readonly type: PropType<boolean | CardSegmented>;
        readonly default: false;
    };
    readonly size: {
        readonly type: PropType<"small" | "medium" | "large" | "huge">;
        readonly default: "medium";
    };
    readonly bordered: {
        readonly type: BooleanConstructor;
        readonly default: true;
    };
    readonly closable: BooleanConstructor;
    readonly hoverable: BooleanConstructor;
    readonly role: StringConstructor;
    readonly onClose: PropType<MaybeArray<() => void>>;
    readonly tag: {
        readonly type: PropType<keyof HTMLElementTagNameMap>;
        readonly default: "div";
    };
    readonly cover: PropType<() => VNodeChild>;
    readonly content: PropType<string | (() => VNodeChild)>;
    readonly footer: PropType<() => VNodeChild>;
    readonly action: PropType<() => VNodeChild>;
    readonly headerExtra: PropType<() => VNodeChild>;
};
export declare const cardBasePropKeys: ("tag" | "content" | "size" | "footer" | "title" | "cover" | "role" | "onClose" | "action" | "bordered" | "contentClass" | "contentStyle" | "closable" | "headerClass" | "headerStyle" | "footerClass" | "footerStyle" | "hoverable" | "embedded" | "headerExtraClass" | "headerExtraStyle" | "segmented" | "headerExtra")[];
export declare const cardProps: {
    title: PropType<string | (() => VNodeChild)>;
    contentClass: StringConstructor;
    contentStyle: PropType<CSSProperties | string>;
    headerClass: StringConstructor;
    headerStyle: PropType<CSSProperties | string>;
    headerExtraClass: StringConstructor;
    headerExtraStyle: PropType<CSSProperties | string>;
    footerClass: StringConstructor;
    footerStyle: PropType<CSSProperties | string>;
    embedded: BooleanConstructor;
    segmented: {
        readonly type: PropType<boolean | CardSegmented>;
        readonly default: false;
    };
    size: {
        readonly type: PropType<"small" | "medium" | "large" | "huge">;
        readonly default: "medium";
    };
    bordered: {
        readonly type: BooleanConstructor;
        readonly default: true;
    };
    closable: BooleanConstructor;
    hoverable: BooleanConstructor;
    role: StringConstructor;
    onClose: PropType<MaybeArray<() => void>>;
    tag: {
        readonly type: PropType<keyof HTMLElementTagNameMap>;
        readonly default: "div";
    };
    cover: PropType<() => VNodeChild>;
    content: PropType<string | (() => VNodeChild)>;
    footer: PropType<() => VNodeChild>;
    action: PropType<() => VNodeChild>;
    headerExtra: PropType<() => VNodeChild>;
    theme: PropType<import("../../_mixins").Theme<"Card", {
        lineHeight: string;
        color: string;
        colorModal: string;
        colorPopover: string;
        colorTarget: string;
        colorEmbedded: string;
        colorEmbeddedModal: string;
        colorEmbeddedPopover: string;
        textColor: string;
        titleTextColor: string;
        borderColor: string;
        actionColor: string;
        titleFontWeight: string;
        closeColorHover: string;
        closeColorPressed: string;
        closeBorderRadius: string;
        closeIconColor: string;
        closeIconColorHover: string;
        closeIconColorPressed: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        fontSizeHuge: string;
        boxShadow: string;
        borderRadius: string;
        paddingSmall: string;
        paddingMedium: string;
        paddingLarge: string;
        paddingHuge: string;
        titleFontSizeSmall: string;
        titleFontSizeMedium: string;
        titleFontSizeLarge: string;
        titleFontSizeHuge: string;
        closeIconSize: string;
        closeSize: string;
    }, any>>;
    themeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Card", {
        lineHeight: string;
        color: string;
        colorModal: string;
        colorPopover: string;
        colorTarget: string;
        colorEmbedded: string;
        colorEmbeddedModal: string;
        colorEmbeddedPopover: string;
        textColor: string;
        titleTextColor: string;
        borderColor: string;
        actionColor: string;
        titleFontWeight: string;
        closeColorHover: string;
        closeColorPressed: string;
        closeBorderRadius: string;
        closeIconColor: string;
        closeIconColorHover: string;
        closeIconColorPressed: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        fontSizeHuge: string;
        boxShadow: string;
        borderRadius: string;
        paddingSmall: string;
        paddingMedium: string;
        paddingLarge: string;
        paddingHuge: string;
        titleFontSizeSmall: string;
        titleFontSizeMedium: string;
        titleFontSizeLarge: string;
        titleFontSizeHuge: string;
        closeIconSize: string;
        closeSize: string;
    }, any>>>;
    builtinThemeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Card", {
        lineHeight: string;
        color: string;
        colorModal: string;
        colorPopover: string;
        colorTarget: string;
        colorEmbedded: string;
        colorEmbeddedModal: string;
        colorEmbeddedPopover: string;
        textColor: string;
        titleTextColor: string;
        borderColor: string;
        actionColor: string;
        titleFontWeight: string;
        closeColorHover: string;
        closeColorPressed: string;
        closeBorderRadius: string;
        closeIconColor: string;
        closeIconColorHover: string;
        closeIconColorPressed: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        fontSizeHuge: string;
        boxShadow: string;
        borderRadius: string;
        paddingSmall: string;
        paddingMedium: string;
        paddingLarge: string;
        paddingHuge: string;
        titleFontSizeSmall: string;
        titleFontSizeMedium: string;
        titleFontSizeLarge: string;
        titleFontSizeHuge: string;
        closeIconSize: string;
        closeSize: string;
    }, any>>>;
};
export type CardProps = ExtractPublicPropTypes<typeof cardProps>;
export interface CardSlots {
    default?: () => VNode[];
    cover?: () => VNode[];
    header?: () => VNode[];
    'header-extra'?: () => VNode[];
    footer?: () => VNode[];
    action?: () => VNode[];
}
declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    title: PropType<string | (() => VNodeChild)>;
    contentClass: StringConstructor;
    contentStyle: PropType<CSSProperties | string>;
    headerClass: StringConstructor;
    headerStyle: PropType<CSSProperties | string>;
    headerExtraClass: StringConstructor;
    headerExtraStyle: PropType<CSSProperties | string>;
    footerClass: StringConstructor;
    footerStyle: PropType<CSSProperties | string>;
    embedded: BooleanConstructor;
    segmented: {
        readonly type: PropType<boolean | CardSegmented>;
        readonly default: false;
    };
    size: {
        readonly type: PropType<"small" | "medium" | "large" | "huge">;
        readonly default: "medium";
    };
    bordered: {
        readonly type: BooleanConstructor;
        readonly default: true;
    };
    closable: BooleanConstructor;
    hoverable: BooleanConstructor;
    role: StringConstructor;
    onClose: PropType<MaybeArray<() => void>>;
    tag: {
        readonly type: PropType<keyof HTMLElementTagNameMap>;
        readonly default: "div";
    };
    cover: PropType<() => VNodeChild>;
    content: PropType<string | (() => VNodeChild)>;
    footer: PropType<() => VNodeChild>;
    action: PropType<() => VNodeChild>;
    headerExtra: PropType<() => VNodeChild>;
    theme: PropType<import("../../_mixins").Theme<"Card", {
        lineHeight: string;
        color: string;
        colorModal: string;
        colorPopover: string;
        colorTarget: string;
        colorEmbedded: string;
        colorEmbeddedModal: string;
        colorEmbeddedPopover: string;
        textColor: string;
        titleTextColor: string;
        borderColor: string;
        actionColor: string;
        titleFontWeight: string;
        closeColorHover: string;
        closeColorPressed: string;
        closeBorderRadius: string;
        closeIconColor: string;
        closeIconColorHover: string;
        closeIconColorPressed: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        fontSizeHuge: string;
        boxShadow: string;
        borderRadius: string;
        paddingSmall: string;
        paddingMedium: string;
        paddingLarge: string;
        paddingHuge: string;
        titleFontSizeSmall: string;
        titleFontSizeMedium: string;
        titleFontSizeLarge: string;
        titleFontSizeHuge: string;
        closeIconSize: string;
        closeSize: string;
    }, any>>;
    themeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Card", {
        lineHeight: string;
        color: string;
        colorModal: string;
        colorPopover: string;
        colorTarget: string;
        colorEmbedded: string;
        colorEmbeddedModal: string;
        colorEmbeddedPopover: string;
        textColor: string;
        titleTextColor: string;
        borderColor: string;
        actionColor: string;
        titleFontWeight: string;
        closeColorHover: string;
        closeColorPressed: string;
        closeBorderRadius: string;
        closeIconColor: string;
        closeIconColorHover: string;
        closeIconColorPressed: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        fontSizeHuge: string;
        boxShadow: string;
        borderRadius: string;
        paddingSmall: string;
        paddingMedium: string;
        paddingLarge: string;
        paddingHuge: string;
        titleFontSizeSmall: string;
        titleFontSizeMedium: string;
        titleFontSizeLarge: string;
        titleFontSizeHuge: string;
        closeIconSize: string;
        closeSize: string;
    }, any>>>;
    builtinThemeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Card", {
        lineHeight: string;
        color: string;
        colorModal: string;
        colorPopover: string;
        colorTarget: string;
        colorEmbedded: string;
        colorEmbeddedModal: string;
        colorEmbeddedPopover: string;
        textColor: string;
        titleTextColor: string;
        borderColor: string;
        actionColor: string;
        titleFontWeight: string;
        closeColorHover: string;
        closeColorPressed: string;
        closeBorderRadius: string;
        closeIconColor: string;
        closeIconColorHover: string;
        closeIconColorPressed: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        fontSizeHuge: string;
        boxShadow: string;
        borderRadius: string;
        paddingSmall: string;
        paddingMedium: string;
        paddingLarge: string;
        paddingHuge: string;
        titleFontSizeSmall: string;
        titleFontSizeMedium: string;
        titleFontSizeLarge: string;
        titleFontSizeHuge: string;
        closeIconSize: string;
        closeSize: string;
    }, any>>>;
}>, {
    rtlEnabled: import("vue").Ref<import("../../config-provider/src/internal-interface").RtlItem | undefined, import("../../config-provider/src/internal-interface").RtlItem | undefined> | undefined;
    mergedClsPrefix: import("vue").Ref<string, string>;
    mergedTheme: import("vue").ComputedRef<{
        common: import("../..").ThemeCommonVars;
        self: {
            lineHeight: string;
            color: string;
            colorModal: string;
            colorPopover: string;
            colorTarget: string;
            colorEmbedded: string;
            colorEmbeddedModal: string;
            colorEmbeddedPopover: string;
            textColor: string;
            titleTextColor: string;
            borderColor: string;
            actionColor: string;
            titleFontWeight: string;
            closeColorHover: string;
            closeColorPressed: string;
            closeBorderRadius: string;
            closeIconColor: string;
            closeIconColorHover: string;
            closeIconColorPressed: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            fontSizeHuge: string;
            boxShadow: string;
            borderRadius: string;
            paddingSmall: string;
            paddingMedium: string;
            paddingLarge: string;
            paddingHuge: string;
            titleFontSizeSmall: string;
            titleFontSizeMedium: string;
            titleFontSizeLarge: string;
            titleFontSizeHuge: string;
            closeIconSize: string;
            closeSize: string;
        };
        peers: any;
        peerOverrides: {
            [x: string]: any;
        };
    }>;
    handleCloseClick: () => void;
    cssVars: import("vue").ComputedRef<{
        '--n-bezier': string;
        '--n-border-radius': string;
        '--n-color': string;
        '--n-color-modal': string;
        '--n-color-popover': string;
        '--n-color-embedded': string;
        '--n-color-embedded-modal': string;
        '--n-color-embedded-popover': string;
        '--n-color-target': string;
        '--n-text-color': string;
        '--n-line-height': string;
        '--n-action-color': string;
        '--n-title-text-color': string;
        '--n-title-font-weight': string;
        '--n-close-icon-color': string;
        '--n-close-icon-color-hover': string;
        '--n-close-icon-color-pressed': string;
        '--n-close-color-hover': string;
        '--n-close-color-pressed': string;
        '--n-border-color': string;
        '--n-box-shadow': string;
        '--n-padding-top': string;
        '--n-padding-bottom': string;
        '--n-padding-left': string;
        '--n-font-size': string;
        '--n-title-font-size': string;
        '--n-close-size': string;
        '--n-close-icon-size': string;
        '--n-close-border-radius': string;
    }> | undefined;
    themeClass: import("vue").Ref<string, string> | undefined;
    onRender: (() => void) | undefined;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    title: PropType<string | (() => VNodeChild)>;
    contentClass: StringConstructor;
    contentStyle: PropType<CSSProperties | string>;
    headerClass: StringConstructor;
    headerStyle: PropType<CSSProperties | string>;
    headerExtraClass: StringConstructor;
    headerExtraStyle: PropType<CSSProperties | string>;
    footerClass: StringConstructor;
    footerStyle: PropType<CSSProperties | string>;
    embedded: BooleanConstructor;
    segmented: {
        readonly type: PropType<boolean | CardSegmented>;
        readonly default: false;
    };
    size: {
        readonly type: PropType<"small" | "medium" | "large" | "huge">;
        readonly default: "medium";
    };
    bordered: {
        readonly type: BooleanConstructor;
        readonly default: true;
    };
    closable: BooleanConstructor;
    hoverable: BooleanConstructor;
    role: StringConstructor;
    onClose: PropType<MaybeArray<() => void>>;
    tag: {
        readonly type: PropType<keyof HTMLElementTagNameMap>;
        readonly default: "div";
    };
    cover: PropType<() => VNodeChild>;
    content: PropType<string | (() => VNodeChild)>;
    footer: PropType<() => VNodeChild>;
    action: PropType<() => VNodeChild>;
    headerExtra: PropType<() => VNodeChild>;
    theme: PropType<import("../../_mixins").Theme<"Card", {
        lineHeight: string;
        color: string;
        colorModal: string;
        colorPopover: string;
        colorTarget: string;
        colorEmbedded: string;
        colorEmbeddedModal: string;
        colorEmbeddedPopover: string;
        textColor: string;
        titleTextColor: string;
        borderColor: string;
        actionColor: string;
        titleFontWeight: string;
        closeColorHover: string;
        closeColorPressed: string;
        closeBorderRadius: string;
        closeIconColor: string;
        closeIconColorHover: string;
        closeIconColorPressed: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        fontSizeHuge: string;
        boxShadow: string;
        borderRadius: string;
        paddingSmall: string;
        paddingMedium: string;
        paddingLarge: string;
        paddingHuge: string;
        titleFontSizeSmall: string;
        titleFontSizeMedium: string;
        titleFontSizeLarge: string;
        titleFontSizeHuge: string;
        closeIconSize: string;
        closeSize: string;
    }, any>>;
    themeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Card", {
        lineHeight: string;
        color: string;
        colorModal: string;
        colorPopover: string;
        colorTarget: string;
        colorEmbedded: string;
        colorEmbeddedModal: string;
        colorEmbeddedPopover: string;
        textColor: string;
        titleTextColor: string;
        borderColor: string;
        actionColor: string;
        titleFontWeight: string;
        closeColorHover: string;
        closeColorPressed: string;
        closeBorderRadius: string;
        closeIconColor: string;
        closeIconColorHover: string;
        closeIconColorPressed: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        fontSizeHuge: string;
        boxShadow: string;
        borderRadius: string;
        paddingSmall: string;
        paddingMedium: string;
        paddingLarge: string;
        paddingHuge: string;
        titleFontSizeSmall: string;
        titleFontSizeMedium: string;
        titleFontSizeLarge: string;
        titleFontSizeHuge: string;
        closeIconSize: string;
        closeSize: string;
    }, any>>>;
    builtinThemeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Card", {
        lineHeight: string;
        color: string;
        colorModal: string;
        colorPopover: string;
        colorTarget: string;
        colorEmbedded: string;
        colorEmbeddedModal: string;
        colorEmbeddedPopover: string;
        textColor: string;
        titleTextColor: string;
        borderColor: string;
        actionColor: string;
        titleFontWeight: string;
        closeColorHover: string;
        closeColorPressed: string;
        closeBorderRadius: string;
        closeIconColor: string;
        closeIconColorHover: string;
        closeIconColorPressed: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        fontSizeHuge: string;
        boxShadow: string;
        borderRadius: string;
        paddingSmall: string;
        paddingMedium: string;
        paddingLarge: string;
        paddingHuge: string;
        titleFontSizeSmall: string;
        titleFontSizeMedium: string;
        titleFontSizeLarge: string;
        titleFontSizeHuge: string;
        closeIconSize: string;
        closeSize: string;
    }, any>>>;
}>> & Readonly<{}>, {
    tag: keyof HTMLElementTagNameMap;
    size: "small" | "medium" | "large" | "huge";
    bordered: boolean;
    closable: boolean;
    hoverable: boolean;
    embedded: boolean;
    segmented: boolean | CardSegmented;
}, SlotsType<CardSlots>, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;

import type { VNode } from 'vue';
import type { GlobalIconConfig } from '../../config-provider/src/internal-interface';
export declare function replaceable(name: keyof GlobalIconConfig, icon: () => VNode): import("vue").DefineComponent<{}, () => import("vue").VNodeChild, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<{}> & Readonly<{}>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;

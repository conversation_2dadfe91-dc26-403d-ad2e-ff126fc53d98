declare const __VERSION__: string;

// Dynamic Modules
declare const __USE_ALT_AUDIO__: boolean;
declare const __USE_EME_DRM__: boolean;
declare const __USE_SUBTITLES__: boolean;
declare const __USE_CMCD__: boolean;
declare const __USE_CONTENT_STEERING__: boolean;
declare const __USE_VARIABLE_SUBSTITUTION__: boolean;
declare const __USE_M2TS_ADVANCED_CODECS__: boolean;
declare const __USE_MEDIA_CAPABILITIES__: boolean;
declare const __USE_INTERSTITIALS__: boolean;

// __IN_WORKER__ is provided from a closure call around the final UMD bundle.
declare const __IN_WORKER__: boolean;
// __HLS_WORKER_BUNDLE__ is the name of the closure around the final UMD bundle.
declare const __HLS_WORKER_BUNDLE__: Function;

import type { <PERSON><PERSON><PERSON>abe<PERSON>, RenderOption } from '../../_internal/select-menu/src/interface';
import type { FormValidationStatus } from '../../form/src/interface';
import type { SelectBaseOption, SelectGroupOption, SelectIgnoredOption } from '../../select/src/interface';
import type { AutoCompleteDefaultSlotProps, AutoCompleteOptions, OnSelect, OnUpdateValue } from './interface';
import { type TreeNode } from 'treemate';
import { type HTMLAttributes, type InputHTMLAttributes, type PropType, type SlotsType, type VNode } from 'vue';
import { type FollowerPlacement } from 'vueuc';
import { type ExtractPublicPropTypes, type MaybeArray } from '../../_utils';
export declare const autoCompleteProps: {
    readonly to: {
        type: PropType<HTMLElement | string | boolean>;
        default: undefined;
    };
    readonly menuProps: PropType<HTMLAttributes>;
    readonly append: BooleanConstructor;
    readonly bordered: {
        readonly type: PropType<boolean | undefined>;
        readonly default: undefined;
    };
    readonly clearable: {
        readonly type: BooleanConstructor;
        readonly default: undefined;
    };
    readonly defaultValue: {
        readonly type: PropType<string | null>;
        readonly default: null;
    };
    readonly loading: {
        readonly type: BooleanConstructor;
        readonly default: undefined;
    };
    readonly disabled: {
        readonly type: PropType<boolean | undefined>;
        readonly default: undefined;
    };
    readonly placeholder: StringConstructor;
    readonly placement: {
        readonly type: PropType<FollowerPlacement>;
        readonly default: "bottom-start";
    };
    readonly value: StringConstructor;
    readonly blurAfterSelect: BooleanConstructor;
    readonly clearAfterSelect: BooleanConstructor;
    readonly getShow: PropType<(inputValue: string) => boolean>;
    readonly showEmpty: BooleanConstructor;
    readonly inputProps: PropType<InputHTMLAttributes>;
    readonly renderOption: PropType<RenderOption>;
    readonly renderLabel: PropType<RenderLabel>;
    readonly size: PropType<"small" | "medium" | "large">;
    readonly options: {
        readonly type: PropType<AutoCompleteOptions>;
        readonly default: () => never[];
    };
    readonly zIndex: NumberConstructor;
    readonly status: PropType<FormValidationStatus>;
    readonly 'onUpdate:value': PropType<MaybeArray<OnUpdateValue>>;
    readonly onUpdateValue: PropType<MaybeArray<OnUpdateValue>>;
    readonly onSelect: PropType<MaybeArray<OnSelect>>;
    readonly onBlur: PropType<MaybeArray<(e: FocusEvent) => void>>;
    readonly onFocus: PropType<MaybeArray<(e: FocusEvent) => void>>;
    readonly onInput: PropType<MaybeArray<OnUpdateValue> | undefined>;
    readonly theme: PropType<import("../../_mixins").Theme<"AutoComplete", {
        menuBoxShadow: string;
    }, {
        InternalSelectMenu: import("../../_mixins").Theme<"InternalSelectMenu", {
            optionFontSizeTiny: string;
            optionFontSizeSmall: string;
            optionFontSizeMedium: string;
            optionFontSizeLarge: string;
            optionFontSizeHuge: string;
            optionHeightTiny: string;
            optionHeightSmall: string;
            optionHeightMedium: string;
            optionHeightLarge: string;
            optionHeightHuge: string;
            borderRadius: string;
            color: string;
            groupHeaderTextColor: string;
            actionDividerColor: string;
            optionTextColor: string;
            optionTextColorPressed: string;
            optionTextColorDisabled: string;
            optionTextColorActive: string;
            optionOpacityDisabled: string;
            optionCheckColor: string;
            optionColorPending: string;
            optionColorActive: string;
            optionColorActivePending: string;
            actionTextColor: string;
            loadingColor: string;
            height: string;
            paddingTiny: string;
            paddingSmall: string;
            paddingMedium: string;
            paddingLarge: string;
            paddingHuge: string;
            optionPaddingTiny: string;
            optionPaddingSmall: string;
            optionPaddingMedium: string;
            optionPaddingLarge: string;
            optionPaddingHuge: string;
            loadingSize: string;
        }, {
            Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                height: string;
                width: string;
                borderRadius: string;
                color: string;
                colorHover: string;
                railInsetHorizontalBottom: string;
                railInsetHorizontalTop: string;
                railInsetVerticalRight: string;
                railInsetVerticalLeft: string;
                railColor: string;
            }, any>;
            Empty: import("../../_mixins").Theme<"Empty", {
                fontSizeTiny: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                fontSizeHuge: string;
                textColor: string;
                iconColor: string;
                extraTextColor: string;
                iconSizeTiny: string;
                iconSizeSmall: string;
                iconSizeMedium: string;
                iconSizeLarge: string;
                iconSizeHuge: string;
            }, any>;
        }>;
        Input: import("../../_mixins").Theme<"Input", {
            fontWeight: string;
            countTextColorDisabled: string;
            countTextColor: string;
            heightTiny: string;
            heightSmall: string;
            heightMedium: string;
            heightLarge: string;
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            lineHeight: string;
            lineHeightTextarea: string;
            borderRadius: string;
            iconSize: string;
            groupLabelColor: string;
            groupLabelTextColor: string;
            textColor: string;
            textColorDisabled: string;
            textDecorationColor: string;
            caretColor: string;
            placeholderColor: string;
            placeholderColorDisabled: string;
            color: string;
            colorDisabled: string;
            colorFocus: string;
            groupLabelBorder: string;
            border: string;
            borderHover: string;
            borderDisabled: string;
            borderFocus: string;
            boxShadowFocus: string;
            loadingColor: string;
            loadingColorWarning: string;
            borderWarning: string;
            borderHoverWarning: string;
            colorFocusWarning: string;
            borderFocusWarning: string;
            boxShadowFocusWarning: string;
            caretColorWarning: string;
            loadingColorError: string;
            borderError: string;
            borderHoverError: string;
            colorFocusError: string;
            borderFocusError: string;
            boxShadowFocusError: string;
            caretColorError: string;
            clearColor: string;
            clearColorHover: string;
            clearColorPressed: string;
            iconColor: string;
            iconColorDisabled: string;
            iconColorHover: string;
            iconColorPressed: string;
            suffixTextColor: string;
            paddingTiny: string;
            paddingSmall: string;
            paddingMedium: string;
            paddingLarge: string;
            clearSize: string;
        }, any>;
    }>>;
    readonly themeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"AutoComplete", {
        menuBoxShadow: string;
    }, {
        InternalSelectMenu: import("../../_mixins").Theme<"InternalSelectMenu", {
            optionFontSizeTiny: string;
            optionFontSizeSmall: string;
            optionFontSizeMedium: string;
            optionFontSizeLarge: string;
            optionFontSizeHuge: string;
            optionHeightTiny: string;
            optionHeightSmall: string;
            optionHeightMedium: string;
            optionHeightLarge: string;
            optionHeightHuge: string;
            borderRadius: string;
            color: string;
            groupHeaderTextColor: string;
            actionDividerColor: string;
            optionTextColor: string;
            optionTextColorPressed: string;
            optionTextColorDisabled: string;
            optionTextColorActive: string;
            optionOpacityDisabled: string;
            optionCheckColor: string;
            optionColorPending: string;
            optionColorActive: string;
            optionColorActivePending: string;
            actionTextColor: string;
            loadingColor: string;
            height: string;
            paddingTiny: string;
            paddingSmall: string;
            paddingMedium: string;
            paddingLarge: string;
            paddingHuge: string;
            optionPaddingTiny: string;
            optionPaddingSmall: string;
            optionPaddingMedium: string;
            optionPaddingLarge: string;
            optionPaddingHuge: string;
            loadingSize: string;
        }, {
            Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                height: string;
                width: string;
                borderRadius: string;
                color: string;
                colorHover: string;
                railInsetHorizontalBottom: string;
                railInsetHorizontalTop: string;
                railInsetVerticalRight: string;
                railInsetVerticalLeft: string;
                railColor: string;
            }, any>;
            Empty: import("../../_mixins").Theme<"Empty", {
                fontSizeTiny: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                fontSizeHuge: string;
                textColor: string;
                iconColor: string;
                extraTextColor: string;
                iconSizeTiny: string;
                iconSizeSmall: string;
                iconSizeMedium: string;
                iconSizeLarge: string;
                iconSizeHuge: string;
            }, any>;
        }>;
        Input: import("../../_mixins").Theme<"Input", {
            fontWeight: string;
            countTextColorDisabled: string;
            countTextColor: string;
            heightTiny: string;
            heightSmall: string;
            heightMedium: string;
            heightLarge: string;
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            lineHeight: string;
            lineHeightTextarea: string;
            borderRadius: string;
            iconSize: string;
            groupLabelColor: string;
            groupLabelTextColor: string;
            textColor: string;
            textColorDisabled: string;
            textDecorationColor: string;
            caretColor: string;
            placeholderColor: string;
            placeholderColorDisabled: string;
            color: string;
            colorDisabled: string;
            colorFocus: string;
            groupLabelBorder: string;
            border: string;
            borderHover: string;
            borderDisabled: string;
            borderFocus: string;
            boxShadowFocus: string;
            loadingColor: string;
            loadingColorWarning: string;
            borderWarning: string;
            borderHoverWarning: string;
            colorFocusWarning: string;
            borderFocusWarning: string;
            boxShadowFocusWarning: string;
            caretColorWarning: string;
            loadingColorError: string;
            borderError: string;
            borderHoverError: string;
            colorFocusError: string;
            borderFocusError: string;
            boxShadowFocusError: string;
            caretColorError: string;
            clearColor: string;
            clearColorHover: string;
            clearColorPressed: string;
            iconColor: string;
            iconColorDisabled: string;
            iconColorHover: string;
            iconColorPressed: string;
            suffixTextColor: string;
            paddingTiny: string;
            paddingSmall: string;
            paddingMedium: string;
            paddingLarge: string;
            clearSize: string;
        }, any>;
    }>>>;
    readonly builtinThemeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"AutoComplete", {
        menuBoxShadow: string;
    }, {
        InternalSelectMenu: import("../../_mixins").Theme<"InternalSelectMenu", {
            optionFontSizeTiny: string;
            optionFontSizeSmall: string;
            optionFontSizeMedium: string;
            optionFontSizeLarge: string;
            optionFontSizeHuge: string;
            optionHeightTiny: string;
            optionHeightSmall: string;
            optionHeightMedium: string;
            optionHeightLarge: string;
            optionHeightHuge: string;
            borderRadius: string;
            color: string;
            groupHeaderTextColor: string;
            actionDividerColor: string;
            optionTextColor: string;
            optionTextColorPressed: string;
            optionTextColorDisabled: string;
            optionTextColorActive: string;
            optionOpacityDisabled: string;
            optionCheckColor: string;
            optionColorPending: string;
            optionColorActive: string;
            optionColorActivePending: string;
            actionTextColor: string;
            loadingColor: string;
            height: string;
            paddingTiny: string;
            paddingSmall: string;
            paddingMedium: string;
            paddingLarge: string;
            paddingHuge: string;
            optionPaddingTiny: string;
            optionPaddingSmall: string;
            optionPaddingMedium: string;
            optionPaddingLarge: string;
            optionPaddingHuge: string;
            loadingSize: string;
        }, {
            Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                height: string;
                width: string;
                borderRadius: string;
                color: string;
                colorHover: string;
                railInsetHorizontalBottom: string;
                railInsetHorizontalTop: string;
                railInsetVerticalRight: string;
                railInsetVerticalLeft: string;
                railColor: string;
            }, any>;
            Empty: import("../../_mixins").Theme<"Empty", {
                fontSizeTiny: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                fontSizeHuge: string;
                textColor: string;
                iconColor: string;
                extraTextColor: string;
                iconSizeTiny: string;
                iconSizeSmall: string;
                iconSizeMedium: string;
                iconSizeLarge: string;
                iconSizeHuge: string;
            }, any>;
        }>;
        Input: import("../../_mixins").Theme<"Input", {
            fontWeight: string;
            countTextColorDisabled: string;
            countTextColor: string;
            heightTiny: string;
            heightSmall: string;
            heightMedium: string;
            heightLarge: string;
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            lineHeight: string;
            lineHeightTextarea: string;
            borderRadius: string;
            iconSize: string;
            groupLabelColor: string;
            groupLabelTextColor: string;
            textColor: string;
            textColorDisabled: string;
            textDecorationColor: string;
            caretColor: string;
            placeholderColor: string;
            placeholderColorDisabled: string;
            color: string;
            colorDisabled: string;
            colorFocus: string;
            groupLabelBorder: string;
            border: string;
            borderHover: string;
            borderDisabled: string;
            borderFocus: string;
            boxShadowFocus: string;
            loadingColor: string;
            loadingColorWarning: string;
            borderWarning: string;
            borderHoverWarning: string;
            colorFocusWarning: string;
            borderFocusWarning: string;
            boxShadowFocusWarning: string;
            caretColorWarning: string;
            loadingColorError: string;
            borderError: string;
            borderHoverError: string;
            colorFocusError: string;
            borderFocusError: string;
            boxShadowFocusError: string;
            caretColorError: string;
            clearColor: string;
            clearColorHover: string;
            clearColorPressed: string;
            iconColor: string;
            iconColorDisabled: string;
            iconColorHover: string;
            iconColorPressed: string;
            suffixTextColor: string;
            paddingTiny: string;
            paddingSmall: string;
            paddingMedium: string;
            paddingLarge: string;
            clearSize: string;
        }, any>;
    }>>>;
};
export type AutoCompleteProps = ExtractPublicPropTypes<typeof autoCompleteProps>;
export interface AutoCompleteSlots {
    default?: (options: AutoCompleteDefaultSlotProps) => VNode[];
    empty?: () => VNode[];
    prefix?: () => VNode[];
    suffix?: () => VNode[];
}
declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    readonly to: {
        type: PropType<HTMLElement | string | boolean>;
        default: undefined;
    };
    readonly menuProps: PropType<HTMLAttributes>;
    readonly append: BooleanConstructor;
    readonly bordered: {
        readonly type: PropType<boolean | undefined>;
        readonly default: undefined;
    };
    readonly clearable: {
        readonly type: BooleanConstructor;
        readonly default: undefined;
    };
    readonly defaultValue: {
        readonly type: PropType<string | null>;
        readonly default: null;
    };
    readonly loading: {
        readonly type: BooleanConstructor;
        readonly default: undefined;
    };
    readonly disabled: {
        readonly type: PropType<boolean | undefined>;
        readonly default: undefined;
    };
    readonly placeholder: StringConstructor;
    readonly placement: {
        readonly type: PropType<FollowerPlacement>;
        readonly default: "bottom-start";
    };
    readonly value: StringConstructor;
    readonly blurAfterSelect: BooleanConstructor;
    readonly clearAfterSelect: BooleanConstructor;
    readonly getShow: PropType<(inputValue: string) => boolean>;
    readonly showEmpty: BooleanConstructor;
    readonly inputProps: PropType<InputHTMLAttributes>;
    readonly renderOption: PropType<RenderOption>;
    readonly renderLabel: PropType<RenderLabel>;
    readonly size: PropType<"small" | "medium" | "large">;
    readonly options: {
        readonly type: PropType<AutoCompleteOptions>;
        readonly default: () => never[];
    };
    readonly zIndex: NumberConstructor;
    readonly status: PropType<FormValidationStatus>;
    readonly 'onUpdate:value': PropType<MaybeArray<OnUpdateValue>>;
    readonly onUpdateValue: PropType<MaybeArray<OnUpdateValue>>;
    readonly onSelect: PropType<MaybeArray<OnSelect>>;
    readonly onBlur: PropType<MaybeArray<(e: FocusEvent) => void>>;
    readonly onFocus: PropType<MaybeArray<(e: FocusEvent) => void>>;
    readonly onInput: PropType<MaybeArray<OnUpdateValue> | undefined>;
    readonly theme: PropType<import("../../_mixins").Theme<"AutoComplete", {
        menuBoxShadow: string;
    }, {
        InternalSelectMenu: import("../../_mixins").Theme<"InternalSelectMenu", {
            optionFontSizeTiny: string;
            optionFontSizeSmall: string;
            optionFontSizeMedium: string;
            optionFontSizeLarge: string;
            optionFontSizeHuge: string;
            optionHeightTiny: string;
            optionHeightSmall: string;
            optionHeightMedium: string;
            optionHeightLarge: string;
            optionHeightHuge: string;
            borderRadius: string;
            color: string;
            groupHeaderTextColor: string;
            actionDividerColor: string;
            optionTextColor: string;
            optionTextColorPressed: string;
            optionTextColorDisabled: string;
            optionTextColorActive: string;
            optionOpacityDisabled: string;
            optionCheckColor: string;
            optionColorPending: string;
            optionColorActive: string;
            optionColorActivePending: string;
            actionTextColor: string;
            loadingColor: string;
            height: string;
            paddingTiny: string;
            paddingSmall: string;
            paddingMedium: string;
            paddingLarge: string;
            paddingHuge: string;
            optionPaddingTiny: string;
            optionPaddingSmall: string;
            optionPaddingMedium: string;
            optionPaddingLarge: string;
            optionPaddingHuge: string;
            loadingSize: string;
        }, {
            Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                height: string;
                width: string;
                borderRadius: string;
                color: string;
                colorHover: string;
                railInsetHorizontalBottom: string;
                railInsetHorizontalTop: string;
                railInsetVerticalRight: string;
                railInsetVerticalLeft: string;
                railColor: string;
            }, any>;
            Empty: import("../../_mixins").Theme<"Empty", {
                fontSizeTiny: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                fontSizeHuge: string;
                textColor: string;
                iconColor: string;
                extraTextColor: string;
                iconSizeTiny: string;
                iconSizeSmall: string;
                iconSizeMedium: string;
                iconSizeLarge: string;
                iconSizeHuge: string;
            }, any>;
        }>;
        Input: import("../../_mixins").Theme<"Input", {
            fontWeight: string;
            countTextColorDisabled: string;
            countTextColor: string;
            heightTiny: string;
            heightSmall: string;
            heightMedium: string;
            heightLarge: string;
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            lineHeight: string;
            lineHeightTextarea: string;
            borderRadius: string;
            iconSize: string;
            groupLabelColor: string;
            groupLabelTextColor: string;
            textColor: string;
            textColorDisabled: string;
            textDecorationColor: string;
            caretColor: string;
            placeholderColor: string;
            placeholderColorDisabled: string;
            color: string;
            colorDisabled: string;
            colorFocus: string;
            groupLabelBorder: string;
            border: string;
            borderHover: string;
            borderDisabled: string;
            borderFocus: string;
            boxShadowFocus: string;
            loadingColor: string;
            loadingColorWarning: string;
            borderWarning: string;
            borderHoverWarning: string;
            colorFocusWarning: string;
            borderFocusWarning: string;
            boxShadowFocusWarning: string;
            caretColorWarning: string;
            loadingColorError: string;
            borderError: string;
            borderHoverError: string;
            colorFocusError: string;
            borderFocusError: string;
            boxShadowFocusError: string;
            caretColorError: string;
            clearColor: string;
            clearColorHover: string;
            clearColorPressed: string;
            iconColor: string;
            iconColorDisabled: string;
            iconColorHover: string;
            iconColorPressed: string;
            suffixTextColor: string;
            paddingTiny: string;
            paddingSmall: string;
            paddingMedium: string;
            paddingLarge: string;
            clearSize: string;
        }, any>;
    }>>;
    readonly themeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"AutoComplete", {
        menuBoxShadow: string;
    }, {
        InternalSelectMenu: import("../../_mixins").Theme<"InternalSelectMenu", {
            optionFontSizeTiny: string;
            optionFontSizeSmall: string;
            optionFontSizeMedium: string;
            optionFontSizeLarge: string;
            optionFontSizeHuge: string;
            optionHeightTiny: string;
            optionHeightSmall: string;
            optionHeightMedium: string;
            optionHeightLarge: string;
            optionHeightHuge: string;
            borderRadius: string;
            color: string;
            groupHeaderTextColor: string;
            actionDividerColor: string;
            optionTextColor: string;
            optionTextColorPressed: string;
            optionTextColorDisabled: string;
            optionTextColorActive: string;
            optionOpacityDisabled: string;
            optionCheckColor: string;
            optionColorPending: string;
            optionColorActive: string;
            optionColorActivePending: string;
            actionTextColor: string;
            loadingColor: string;
            height: string;
            paddingTiny: string;
            paddingSmall: string;
            paddingMedium: string;
            paddingLarge: string;
            paddingHuge: string;
            optionPaddingTiny: string;
            optionPaddingSmall: string;
            optionPaddingMedium: string;
            optionPaddingLarge: string;
            optionPaddingHuge: string;
            loadingSize: string;
        }, {
            Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                height: string;
                width: string;
                borderRadius: string;
                color: string;
                colorHover: string;
                railInsetHorizontalBottom: string;
                railInsetHorizontalTop: string;
                railInsetVerticalRight: string;
                railInsetVerticalLeft: string;
                railColor: string;
            }, any>;
            Empty: import("../../_mixins").Theme<"Empty", {
                fontSizeTiny: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                fontSizeHuge: string;
                textColor: string;
                iconColor: string;
                extraTextColor: string;
                iconSizeTiny: string;
                iconSizeSmall: string;
                iconSizeMedium: string;
                iconSizeLarge: string;
                iconSizeHuge: string;
            }, any>;
        }>;
        Input: import("../../_mixins").Theme<"Input", {
            fontWeight: string;
            countTextColorDisabled: string;
            countTextColor: string;
            heightTiny: string;
            heightSmall: string;
            heightMedium: string;
            heightLarge: string;
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            lineHeight: string;
            lineHeightTextarea: string;
            borderRadius: string;
            iconSize: string;
            groupLabelColor: string;
            groupLabelTextColor: string;
            textColor: string;
            textColorDisabled: string;
            textDecorationColor: string;
            caretColor: string;
            placeholderColor: string;
            placeholderColorDisabled: string;
            color: string;
            colorDisabled: string;
            colorFocus: string;
            groupLabelBorder: string;
            border: string;
            borderHover: string;
            borderDisabled: string;
            borderFocus: string;
            boxShadowFocus: string;
            loadingColor: string;
            loadingColorWarning: string;
            borderWarning: string;
            borderHoverWarning: string;
            colorFocusWarning: string;
            borderFocusWarning: string;
            boxShadowFocusWarning: string;
            caretColorWarning: string;
            loadingColorError: string;
            borderError: string;
            borderHoverError: string;
            colorFocusError: string;
            borderFocusError: string;
            boxShadowFocusError: string;
            caretColorError: string;
            clearColor: string;
            clearColorHover: string;
            clearColorPressed: string;
            iconColor: string;
            iconColorDisabled: string;
            iconColorHover: string;
            iconColorPressed: string;
            suffixTextColor: string;
            paddingTiny: string;
            paddingSmall: string;
            paddingMedium: string;
            paddingLarge: string;
            clearSize: string;
        }, any>;
    }>>>;
    readonly builtinThemeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"AutoComplete", {
        menuBoxShadow: string;
    }, {
        InternalSelectMenu: import("../../_mixins").Theme<"InternalSelectMenu", {
            optionFontSizeTiny: string;
            optionFontSizeSmall: string;
            optionFontSizeMedium: string;
            optionFontSizeLarge: string;
            optionFontSizeHuge: string;
            optionHeightTiny: string;
            optionHeightSmall: string;
            optionHeightMedium: string;
            optionHeightLarge: string;
            optionHeightHuge: string;
            borderRadius: string;
            color: string;
            groupHeaderTextColor: string;
            actionDividerColor: string;
            optionTextColor: string;
            optionTextColorPressed: string;
            optionTextColorDisabled: string;
            optionTextColorActive: string;
            optionOpacityDisabled: string;
            optionCheckColor: string;
            optionColorPending: string;
            optionColorActive: string;
            optionColorActivePending: string;
            actionTextColor: string;
            loadingColor: string;
            height: string;
            paddingTiny: string;
            paddingSmall: string;
            paddingMedium: string;
            paddingLarge: string;
            paddingHuge: string;
            optionPaddingTiny: string;
            optionPaddingSmall: string;
            optionPaddingMedium: string;
            optionPaddingLarge: string;
            optionPaddingHuge: string;
            loadingSize: string;
        }, {
            Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                height: string;
                width: string;
                borderRadius: string;
                color: string;
                colorHover: string;
                railInsetHorizontalBottom: string;
                railInsetHorizontalTop: string;
                railInsetVerticalRight: string;
                railInsetVerticalLeft: string;
                railColor: string;
            }, any>;
            Empty: import("../../_mixins").Theme<"Empty", {
                fontSizeTiny: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                fontSizeHuge: string;
                textColor: string;
                iconColor: string;
                extraTextColor: string;
                iconSizeTiny: string;
                iconSizeSmall: string;
                iconSizeMedium: string;
                iconSizeLarge: string;
                iconSizeHuge: string;
            }, any>;
        }>;
        Input: import("../../_mixins").Theme<"Input", {
            fontWeight: string;
            countTextColorDisabled: string;
            countTextColor: string;
            heightTiny: string;
            heightSmall: string;
            heightMedium: string;
            heightLarge: string;
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            lineHeight: string;
            lineHeightTextarea: string;
            borderRadius: string;
            iconSize: string;
            groupLabelColor: string;
            groupLabelTextColor: string;
            textColor: string;
            textColorDisabled: string;
            textDecorationColor: string;
            caretColor: string;
            placeholderColor: string;
            placeholderColorDisabled: string;
            color: string;
            colorDisabled: string;
            colorFocus: string;
            groupLabelBorder: string;
            border: string;
            borderHover: string;
            borderDisabled: string;
            borderFocus: string;
            boxShadowFocus: string;
            loadingColor: string;
            loadingColorWarning: string;
            borderWarning: string;
            borderHoverWarning: string;
            colorFocusWarning: string;
            borderFocusWarning: string;
            boxShadowFocusWarning: string;
            caretColorWarning: string;
            loadingColorError: string;
            borderError: string;
            borderHoverError: string;
            colorFocusError: string;
            borderFocusError: string;
            boxShadowFocusError: string;
            caretColorError: string;
            clearColor: string;
            clearColorHover: string;
            clearColorPressed: string;
            iconColor: string;
            iconColorDisabled: string;
            iconColorHover: string;
            iconColorPressed: string;
            suffixTextColor: string;
            paddingTiny: string;
            paddingSmall: string;
            paddingMedium: string;
            paddingLarge: string;
            clearSize: string;
        }, any>;
    }>>>;
}>, {
    focus: () => void;
    blur: () => void;
    inputInstRef: import("vue").Ref<{
        wrapperElRef: HTMLElement | null;
        textareaElRef: HTMLTextAreaElement | null;
        inputElRef: HTMLInputElement | null;
        isCompositing: boolean;
        blur: () => void;
        clear: () => void;
        focus: () => void;
        select: () => void;
        activate: () => void;
        deactivate: () => void;
        scrollTo: (options: ScrollToOptions) => void;
    } | null, {
        wrapperElRef: HTMLElement | null;
        textareaElRef: HTMLTextAreaElement | null;
        inputElRef: HTMLInputElement | null;
        isCompositing: boolean;
        blur: () => void;
        clear: () => void;
        focus: () => void;
        select: () => void;
        activate: () => void;
        deactivate: () => void;
        scrollTo: (options: ScrollToOptions) => void;
    } | {
        wrapperElRef: HTMLElement | null;
        textareaElRef: HTMLTextAreaElement | null;
        inputElRef: HTMLInputElement | null;
        isCompositing: boolean;
        blur: () => void;
        clear: () => void;
        focus: () => void;
        select: () => void;
        activate: () => void;
        deactivate: () => void;
        scrollTo: (options: ScrollToOptions) => void;
    } | null>;
    uncontrolledValue: import("vue").Ref<string | null, string | null>;
    mergedValue: import("vue").ComputedRef<string | null>;
    isMounted: Readonly<import("vue").Ref<boolean, boolean>>;
    adjustedTo: import("vue").ComputedRef<string | HTMLElement>;
    menuInstRef: import("vue").Ref<{
        selfRef: HTMLElement | null;
        getPendingTmNode: () => TreeNode<SelectBaseOption> | null;
        prev: () => void;
        next: () => void;
    } | null, {
        selfRef: HTMLElement | null;
        getPendingTmNode: () => TreeNode<SelectBaseOption> | null;
        prev: () => void;
        next: () => void;
    } | {
        selfRef: HTMLElement | null;
        getPendingTmNode: () => TreeNode<SelectBaseOption> | null;
        prev: () => void;
        next: () => void;
    } | null>;
    triggerElRef: import("vue").Ref<HTMLElement | null, HTMLElement | null>;
    treeMate: import("vue").ComputedRef<import("treemate").TreeMate<SelectBaseOption<string | number, string | ((option: SelectBaseOption<string | number, string | any>, selected: boolean) => import("vue").VNodeChild)>, SelectGroupOption, SelectIgnoredOption>>;
    mergedSize: import("vue").ComputedRef<"small" | "medium" | "large">;
    mergedDisabled: import("vue").ComputedRef<boolean>;
    active: import("vue").ComputedRef<boolean>;
    mergedStatus: import("vue").ComputedRef<FormValidationStatus | undefined>;
    handleClear: () => void;
    handleFocus: (e: FocusEvent) => void;
    handleBlur: (e: FocusEvent) => void;
    handleInput: (value: string) => void;
    handleToggle: (option: TreeNode<SelectBaseOption>) => void;
    handleClickOutsideMenu: (e: MouseEvent) => void;
    handleCompositionStart: () => void;
    handleCompositionEnd: () => void;
    handleKeyDown: (e: KeyboardEvent) => void;
    mergedTheme: import("vue").ComputedRef<{
        common: import("../..").ThemeCommonVars;
        self: {
            menuBoxShadow: string;
        };
        peers: {
            InternalSelectMenu: import("../../_mixins").Theme<"InternalSelectMenu", {
                optionFontSizeTiny: string;
                optionFontSizeSmall: string;
                optionFontSizeMedium: string;
                optionFontSizeLarge: string;
                optionFontSizeHuge: string;
                optionHeightTiny: string;
                optionHeightSmall: string;
                optionHeightMedium: string;
                optionHeightLarge: string;
                optionHeightHuge: string;
                borderRadius: string;
                color: string;
                groupHeaderTextColor: string;
                actionDividerColor: string;
                optionTextColor: string;
                optionTextColorPressed: string;
                optionTextColorDisabled: string;
                optionTextColorActive: string;
                optionOpacityDisabled: string;
                optionCheckColor: string;
                optionColorPending: string;
                optionColorActive: string;
                optionColorActivePending: string;
                actionTextColor: string;
                loadingColor: string;
                height: string;
                paddingTiny: string;
                paddingSmall: string;
                paddingMedium: string;
                paddingLarge: string;
                paddingHuge: string;
                optionPaddingTiny: string;
                optionPaddingSmall: string;
                optionPaddingMedium: string;
                optionPaddingLarge: string;
                optionPaddingHuge: string;
                loadingSize: string;
            }, {
                Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                    height: string;
                    width: string;
                    borderRadius: string;
                    color: string;
                    colorHover: string;
                    railInsetHorizontalBottom: string;
                    railInsetHorizontalTop: string;
                    railInsetVerticalRight: string;
                    railInsetVerticalLeft: string;
                    railColor: string;
                }, any>;
                Empty: import("../../_mixins").Theme<"Empty", {
                    fontSizeTiny: string;
                    fontSizeSmall: string;
                    fontSizeMedium: string;
                    fontSizeLarge: string;
                    fontSizeHuge: string;
                    textColor: string;
                    iconColor: string;
                    extraTextColor: string;
                    iconSizeTiny: string;
                    iconSizeSmall: string;
                    iconSizeMedium: string;
                    iconSizeLarge: string;
                    iconSizeHuge: string;
                }, any>;
            }>;
            Input: import("../../_mixins").Theme<"Input", {
                fontWeight: string;
                countTextColorDisabled: string;
                countTextColor: string;
                heightTiny: string;
                heightSmall: string;
                heightMedium: string;
                heightLarge: string;
                fontSizeTiny: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                lineHeight: string;
                lineHeightTextarea: string;
                borderRadius: string;
                iconSize: string;
                groupLabelColor: string;
                groupLabelTextColor: string;
                textColor: string;
                textColorDisabled: string;
                textDecorationColor: string;
                caretColor: string;
                placeholderColor: string;
                placeholderColorDisabled: string;
                color: string;
                colorDisabled: string;
                colorFocus: string;
                groupLabelBorder: string;
                border: string;
                borderHover: string;
                borderDisabled: string;
                borderFocus: string;
                boxShadowFocus: string;
                loadingColor: string;
                loadingColorWarning: string;
                borderWarning: string;
                borderHoverWarning: string;
                colorFocusWarning: string;
                borderFocusWarning: string;
                boxShadowFocusWarning: string;
                caretColorWarning: string;
                loadingColorError: string;
                borderError: string;
                borderHoverError: string;
                colorFocusError: string;
                borderFocusError: string;
                boxShadowFocusError: string;
                caretColorError: string;
                clearColor: string;
                clearColorHover: string;
                clearColorPressed: string;
                iconColor: string;
                iconColorDisabled: string;
                iconColorHover: string;
                iconColorPressed: string;
                suffixTextColor: string;
                paddingTiny: string;
                paddingSmall: string;
                paddingMedium: string;
                paddingLarge: string;
                clearSize: string;
            }, any>;
        };
        peerOverrides: {
            InternalSelectMenu?: {
                peers?: {
                    Scrollbar?: import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Scrollbar", {
                        height: string;
                        width: string;
                        borderRadius: string;
                        color: string;
                        colorHover: string;
                        railInsetHorizontalBottom: string;
                        railInsetHorizontalTop: string;
                        railInsetVerticalRight: string;
                        railInsetVerticalLeft: string;
                        railColor: string;
                    }, any>> | undefined;
                    Empty?: import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Empty", {
                        fontSizeTiny: string;
                        fontSizeSmall: string;
                        fontSizeMedium: string;
                        fontSizeLarge: string;
                        fontSizeHuge: string;
                        textColor: string;
                        iconColor: string;
                        extraTextColor: string;
                        iconSizeTiny: string;
                        iconSizeSmall: string;
                        iconSizeMedium: string;
                        iconSizeLarge: string;
                        iconSizeHuge: string;
                    }, any>> | undefined;
                } | undefined;
            } | undefined;
            Input?: {
                peers?: {
                    [x: string]: any;
                } | undefined;
            } | undefined;
        };
    }>;
    cssVars: import("vue").ComputedRef<{
        '--n-menu-box-shadow': string;
        '--n-bezier': string;
    }> | undefined;
    themeClass: import("vue").Ref<string, string> | undefined;
    onRender: (() => void) | undefined;
    mergedBordered: import("vue").ComputedRef<boolean>;
    namespace: import("vue").ComputedRef<string | undefined>;
    mergedClsPrefix: import("vue").Ref<string, string>;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    readonly to: {
        type: PropType<HTMLElement | string | boolean>;
        default: undefined;
    };
    readonly menuProps: PropType<HTMLAttributes>;
    readonly append: BooleanConstructor;
    readonly bordered: {
        readonly type: PropType<boolean | undefined>;
        readonly default: undefined;
    };
    readonly clearable: {
        readonly type: BooleanConstructor;
        readonly default: undefined;
    };
    readonly defaultValue: {
        readonly type: PropType<string | null>;
        readonly default: null;
    };
    readonly loading: {
        readonly type: BooleanConstructor;
        readonly default: undefined;
    };
    readonly disabled: {
        readonly type: PropType<boolean | undefined>;
        readonly default: undefined;
    };
    readonly placeholder: StringConstructor;
    readonly placement: {
        readonly type: PropType<FollowerPlacement>;
        readonly default: "bottom-start";
    };
    readonly value: StringConstructor;
    readonly blurAfterSelect: BooleanConstructor;
    readonly clearAfterSelect: BooleanConstructor;
    readonly getShow: PropType<(inputValue: string) => boolean>;
    readonly showEmpty: BooleanConstructor;
    readonly inputProps: PropType<InputHTMLAttributes>;
    readonly renderOption: PropType<RenderOption>;
    readonly renderLabel: PropType<RenderLabel>;
    readonly size: PropType<"small" | "medium" | "large">;
    readonly options: {
        readonly type: PropType<AutoCompleteOptions>;
        readonly default: () => never[];
    };
    readonly zIndex: NumberConstructor;
    readonly status: PropType<FormValidationStatus>;
    readonly 'onUpdate:value': PropType<MaybeArray<OnUpdateValue>>;
    readonly onUpdateValue: PropType<MaybeArray<OnUpdateValue>>;
    readonly onSelect: PropType<MaybeArray<OnSelect>>;
    readonly onBlur: PropType<MaybeArray<(e: FocusEvent) => void>>;
    readonly onFocus: PropType<MaybeArray<(e: FocusEvent) => void>>;
    readonly onInput: PropType<MaybeArray<OnUpdateValue> | undefined>;
    readonly theme: PropType<import("../../_mixins").Theme<"AutoComplete", {
        menuBoxShadow: string;
    }, {
        InternalSelectMenu: import("../../_mixins").Theme<"InternalSelectMenu", {
            optionFontSizeTiny: string;
            optionFontSizeSmall: string;
            optionFontSizeMedium: string;
            optionFontSizeLarge: string;
            optionFontSizeHuge: string;
            optionHeightTiny: string;
            optionHeightSmall: string;
            optionHeightMedium: string;
            optionHeightLarge: string;
            optionHeightHuge: string;
            borderRadius: string;
            color: string;
            groupHeaderTextColor: string;
            actionDividerColor: string;
            optionTextColor: string;
            optionTextColorPressed: string;
            optionTextColorDisabled: string;
            optionTextColorActive: string;
            optionOpacityDisabled: string;
            optionCheckColor: string;
            optionColorPending: string;
            optionColorActive: string;
            optionColorActivePending: string;
            actionTextColor: string;
            loadingColor: string;
            height: string;
            paddingTiny: string;
            paddingSmall: string;
            paddingMedium: string;
            paddingLarge: string;
            paddingHuge: string;
            optionPaddingTiny: string;
            optionPaddingSmall: string;
            optionPaddingMedium: string;
            optionPaddingLarge: string;
            optionPaddingHuge: string;
            loadingSize: string;
        }, {
            Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                height: string;
                width: string;
                borderRadius: string;
                color: string;
                colorHover: string;
                railInsetHorizontalBottom: string;
                railInsetHorizontalTop: string;
                railInsetVerticalRight: string;
                railInsetVerticalLeft: string;
                railColor: string;
            }, any>;
            Empty: import("../../_mixins").Theme<"Empty", {
                fontSizeTiny: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                fontSizeHuge: string;
                textColor: string;
                iconColor: string;
                extraTextColor: string;
                iconSizeTiny: string;
                iconSizeSmall: string;
                iconSizeMedium: string;
                iconSizeLarge: string;
                iconSizeHuge: string;
            }, any>;
        }>;
        Input: import("../../_mixins").Theme<"Input", {
            fontWeight: string;
            countTextColorDisabled: string;
            countTextColor: string;
            heightTiny: string;
            heightSmall: string;
            heightMedium: string;
            heightLarge: string;
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            lineHeight: string;
            lineHeightTextarea: string;
            borderRadius: string;
            iconSize: string;
            groupLabelColor: string;
            groupLabelTextColor: string;
            textColor: string;
            textColorDisabled: string;
            textDecorationColor: string;
            caretColor: string;
            placeholderColor: string;
            placeholderColorDisabled: string;
            color: string;
            colorDisabled: string;
            colorFocus: string;
            groupLabelBorder: string;
            border: string;
            borderHover: string;
            borderDisabled: string;
            borderFocus: string;
            boxShadowFocus: string;
            loadingColor: string;
            loadingColorWarning: string;
            borderWarning: string;
            borderHoverWarning: string;
            colorFocusWarning: string;
            borderFocusWarning: string;
            boxShadowFocusWarning: string;
            caretColorWarning: string;
            loadingColorError: string;
            borderError: string;
            borderHoverError: string;
            colorFocusError: string;
            borderFocusError: string;
            boxShadowFocusError: string;
            caretColorError: string;
            clearColor: string;
            clearColorHover: string;
            clearColorPressed: string;
            iconColor: string;
            iconColorDisabled: string;
            iconColorHover: string;
            iconColorPressed: string;
            suffixTextColor: string;
            paddingTiny: string;
            paddingSmall: string;
            paddingMedium: string;
            paddingLarge: string;
            clearSize: string;
        }, any>;
    }>>;
    readonly themeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"AutoComplete", {
        menuBoxShadow: string;
    }, {
        InternalSelectMenu: import("../../_mixins").Theme<"InternalSelectMenu", {
            optionFontSizeTiny: string;
            optionFontSizeSmall: string;
            optionFontSizeMedium: string;
            optionFontSizeLarge: string;
            optionFontSizeHuge: string;
            optionHeightTiny: string;
            optionHeightSmall: string;
            optionHeightMedium: string;
            optionHeightLarge: string;
            optionHeightHuge: string;
            borderRadius: string;
            color: string;
            groupHeaderTextColor: string;
            actionDividerColor: string;
            optionTextColor: string;
            optionTextColorPressed: string;
            optionTextColorDisabled: string;
            optionTextColorActive: string;
            optionOpacityDisabled: string;
            optionCheckColor: string;
            optionColorPending: string;
            optionColorActive: string;
            optionColorActivePending: string;
            actionTextColor: string;
            loadingColor: string;
            height: string;
            paddingTiny: string;
            paddingSmall: string;
            paddingMedium: string;
            paddingLarge: string;
            paddingHuge: string;
            optionPaddingTiny: string;
            optionPaddingSmall: string;
            optionPaddingMedium: string;
            optionPaddingLarge: string;
            optionPaddingHuge: string;
            loadingSize: string;
        }, {
            Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                height: string;
                width: string;
                borderRadius: string;
                color: string;
                colorHover: string;
                railInsetHorizontalBottom: string;
                railInsetHorizontalTop: string;
                railInsetVerticalRight: string;
                railInsetVerticalLeft: string;
                railColor: string;
            }, any>;
            Empty: import("../../_mixins").Theme<"Empty", {
                fontSizeTiny: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                fontSizeHuge: string;
                textColor: string;
                iconColor: string;
                extraTextColor: string;
                iconSizeTiny: string;
                iconSizeSmall: string;
                iconSizeMedium: string;
                iconSizeLarge: string;
                iconSizeHuge: string;
            }, any>;
        }>;
        Input: import("../../_mixins").Theme<"Input", {
            fontWeight: string;
            countTextColorDisabled: string;
            countTextColor: string;
            heightTiny: string;
            heightSmall: string;
            heightMedium: string;
            heightLarge: string;
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            lineHeight: string;
            lineHeightTextarea: string;
            borderRadius: string;
            iconSize: string;
            groupLabelColor: string;
            groupLabelTextColor: string;
            textColor: string;
            textColorDisabled: string;
            textDecorationColor: string;
            caretColor: string;
            placeholderColor: string;
            placeholderColorDisabled: string;
            color: string;
            colorDisabled: string;
            colorFocus: string;
            groupLabelBorder: string;
            border: string;
            borderHover: string;
            borderDisabled: string;
            borderFocus: string;
            boxShadowFocus: string;
            loadingColor: string;
            loadingColorWarning: string;
            borderWarning: string;
            borderHoverWarning: string;
            colorFocusWarning: string;
            borderFocusWarning: string;
            boxShadowFocusWarning: string;
            caretColorWarning: string;
            loadingColorError: string;
            borderError: string;
            borderHoverError: string;
            colorFocusError: string;
            borderFocusError: string;
            boxShadowFocusError: string;
            caretColorError: string;
            clearColor: string;
            clearColorHover: string;
            clearColorPressed: string;
            iconColor: string;
            iconColorDisabled: string;
            iconColorHover: string;
            iconColorPressed: string;
            suffixTextColor: string;
            paddingTiny: string;
            paddingSmall: string;
            paddingMedium: string;
            paddingLarge: string;
            clearSize: string;
        }, any>;
    }>>>;
    readonly builtinThemeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"AutoComplete", {
        menuBoxShadow: string;
    }, {
        InternalSelectMenu: import("../../_mixins").Theme<"InternalSelectMenu", {
            optionFontSizeTiny: string;
            optionFontSizeSmall: string;
            optionFontSizeMedium: string;
            optionFontSizeLarge: string;
            optionFontSizeHuge: string;
            optionHeightTiny: string;
            optionHeightSmall: string;
            optionHeightMedium: string;
            optionHeightLarge: string;
            optionHeightHuge: string;
            borderRadius: string;
            color: string;
            groupHeaderTextColor: string;
            actionDividerColor: string;
            optionTextColor: string;
            optionTextColorPressed: string;
            optionTextColorDisabled: string;
            optionTextColorActive: string;
            optionOpacityDisabled: string;
            optionCheckColor: string;
            optionColorPending: string;
            optionColorActive: string;
            optionColorActivePending: string;
            actionTextColor: string;
            loadingColor: string;
            height: string;
            paddingTiny: string;
            paddingSmall: string;
            paddingMedium: string;
            paddingLarge: string;
            paddingHuge: string;
            optionPaddingTiny: string;
            optionPaddingSmall: string;
            optionPaddingMedium: string;
            optionPaddingLarge: string;
            optionPaddingHuge: string;
            loadingSize: string;
        }, {
            Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                height: string;
                width: string;
                borderRadius: string;
                color: string;
                colorHover: string;
                railInsetHorizontalBottom: string;
                railInsetHorizontalTop: string;
                railInsetVerticalRight: string;
                railInsetVerticalLeft: string;
                railColor: string;
            }, any>;
            Empty: import("../../_mixins").Theme<"Empty", {
                fontSizeTiny: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                fontSizeHuge: string;
                textColor: string;
                iconColor: string;
                extraTextColor: string;
                iconSizeTiny: string;
                iconSizeSmall: string;
                iconSizeMedium: string;
                iconSizeLarge: string;
                iconSizeHuge: string;
            }, any>;
        }>;
        Input: import("../../_mixins").Theme<"Input", {
            fontWeight: string;
            countTextColorDisabled: string;
            countTextColor: string;
            heightTiny: string;
            heightSmall: string;
            heightMedium: string;
            heightLarge: string;
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            lineHeight: string;
            lineHeightTextarea: string;
            borderRadius: string;
            iconSize: string;
            groupLabelColor: string;
            groupLabelTextColor: string;
            textColor: string;
            textColorDisabled: string;
            textDecorationColor: string;
            caretColor: string;
            placeholderColor: string;
            placeholderColorDisabled: string;
            color: string;
            colorDisabled: string;
            colorFocus: string;
            groupLabelBorder: string;
            border: string;
            borderHover: string;
            borderDisabled: string;
            borderFocus: string;
            boxShadowFocus: string;
            loadingColor: string;
            loadingColorWarning: string;
            borderWarning: string;
            borderHoverWarning: string;
            colorFocusWarning: string;
            borderFocusWarning: string;
            boxShadowFocusWarning: string;
            caretColorWarning: string;
            loadingColorError: string;
            borderError: string;
            borderHoverError: string;
            colorFocusError: string;
            borderFocusError: string;
            boxShadowFocusError: string;
            caretColorError: string;
            clearColor: string;
            clearColorHover: string;
            clearColorPressed: string;
            iconColor: string;
            iconColorDisabled: string;
            iconColorHover: string;
            iconColorPressed: string;
            suffixTextColor: string;
            paddingTiny: string;
            paddingSmall: string;
            paddingMedium: string;
            paddingLarge: string;
            clearSize: string;
        }, any>;
    }>>>;
}>> & Readonly<{}>, {
    readonly options: AutoCompleteOptions;
    readonly to: string | boolean | HTMLElement;
    readonly disabled: boolean | undefined;
    readonly loading: boolean;
    readonly bordered: boolean | undefined;
    readonly placement: FollowerPlacement;
    readonly clearable: boolean;
    readonly defaultValue: string | null;
    readonly append: boolean;
    readonly blurAfterSelect: boolean;
    readonly clearAfterSelect: boolean;
    readonly showEmpty: boolean;
}, SlotsType<AutoCompleteSlots>, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;

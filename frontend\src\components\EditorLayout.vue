<template>
  <div class="editor-layout">
    <!-- 顶部预览区域 -->
    <div class="preview-section">
      <VideoPreview />
    </div>
    
    <!-- 底部编辑区域 -->
    <div class="editing-section">
      <!-- 左侧素材库 -->
      <div class="media-library-section">
        <MediaLibrary />
      </div>
      
      <!-- 右侧时间轴 -->
      <div class="timeline-section">
        <Timeline />
      </div>
    </div>
    
    <!-- 底部控制栏 -->
    <div class="controls-section">
      <PlaybackControls />
    </div>
  </div>
</template>

<script setup lang="ts">
import VideoPreview from './VideoPreview.vue'
import MediaLibrary from './MediaLibrary.vue'
import Timeline from './Timeline.vue'
import PlaybackControls from './PlaybackControls.vue'
</script>

<style scoped>
.editor-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #1a1a1a;
  color: #ffffff;
}

.preview-section {
  height: 50%;
  border-bottom: 1px solid #333;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #000;
}

.editing-section {
  height: 40%;
  display: flex;
  border-bottom: 1px solid #333;
}

.media-library-section {
  width: 300px;
  border-right: 1px solid #333;
  background-color: #2a2a2a;
}

.timeline-section {
  flex: 1;
  background-color: #1e1e1e;
}

.controls-section {
  height: 10%;
  background-color: #2a2a2a;
  border-top: 1px solid #333;
}
</style>

import type { ThemeCommonVars } from '../../_styles/common';
export declare function self(vars: ThemeCommonVars): {
    menuBoxShadow: string;
};
declare const selectLight: import("../../_mixins").Theme<"Select", {
    menuBoxShadow: string;
}, {
    InternalSelection: import("../../_mixins").Theme<"InternalSelection", {
        fontSizeTiny: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        heightTiny: string;
        heightSmall: string;
        heightMedium: string;
        heightLarge: string;
        borderRadius: string;
        fontWeight: string;
        textColor: string;
        textColorDisabled: string;
        placeholderColor: string;
        placeholderColorDisabled: string;
        color: string;
        colorDisabled: string;
        colorActive: string;
        border: string;
        borderHover: string;
        borderActive: string;
        borderFocus: string;
        boxShadowHover: string;
        boxShadowActive: string;
        boxShadowFocus: string;
        caretColor: string;
        arrowColor: string;
        arrowColorDisabled: string;
        loadingColor: string;
        borderWarning: string;
        borderHoverWarning: string;
        borderActiveWarning: string;
        borderFocusWarning: string;
        boxShadowHoverWarning: string;
        boxShadowActiveWarning: string;
        boxShadowFocusWarning: string;
        colorActiveWarning: string;
        caretColorWarning: string;
        borderError: string;
        borderHoverError: string;
        borderActiveError: string;
        borderFocusError: string;
        boxShadowHoverError: string;
        boxShadowActiveError: string;
        boxShadowFocusError: string;
        colorActiveError: string;
        caretColorError: string;
        clearColor: string;
        clearColorHover: string;
        clearColorPressed: string;
        paddingSingle: string;
        paddingMultiple: string;
        clearSize: string;
        arrowSize: string;
    }, {
        Popover: import("../../_mixins").Theme<"Popover", {
            fontSize: string;
            borderRadius: string;
            color: string;
            dividerColor: string;
            textColor: string;
            boxShadow: string;
            space: string;
            spaceArrow: string;
            arrowOffset: string;
            arrowOffsetVertical: string;
            arrowHeight: string;
            padding: string;
        }, any>;
    }>;
    InternalSelectMenu: import("../../_mixins").Theme<"InternalSelectMenu", {
        optionFontSizeTiny: string;
        optionFontSizeSmall: string;
        optionFontSizeMedium: string;
        optionFontSizeLarge: string;
        optionFontSizeHuge: string;
        optionHeightTiny: string;
        optionHeightSmall: string;
        optionHeightMedium: string;
        optionHeightLarge: string;
        optionHeightHuge: string;
        borderRadius: string;
        color: string;
        groupHeaderTextColor: string;
        actionDividerColor: string;
        optionTextColor: string;
        optionTextColorPressed: string;
        optionTextColorDisabled: string;
        optionTextColorActive: string;
        optionOpacityDisabled: string;
        optionCheckColor: string;
        optionColorPending: string;
        optionColorActive: string;
        optionColorActivePending: string;
        actionTextColor: string;
        loadingColor: string;
        height: string;
        paddingTiny: string;
        paddingSmall: string;
        paddingMedium: string;
        paddingLarge: string;
        paddingHuge: string;
        optionPaddingTiny: string;
        optionPaddingSmall: string;
        optionPaddingMedium: string;
        optionPaddingLarge: string;
        optionPaddingHuge: string;
        loadingSize: string;
    }, {
        Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
            height: string;
            width: string;
            borderRadius: string;
            color: string;
            colorHover: string;
            railInsetHorizontalBottom: string;
            railInsetHorizontalTop: string;
            railInsetVerticalRight: string;
            railInsetVerticalLeft: string;
            railColor: string;
        }, any>;
        Empty: import("../../_mixins").Theme<"Empty", {
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            fontSizeHuge: string;
            textColor: string;
            iconColor: string;
            extraTextColor: string;
            iconSizeTiny: string;
            iconSizeSmall: string;
            iconSizeMedium: string;
            iconSizeLarge: string;
            iconSizeHuge: string;
        }, any>;
    }>;
}>;
export default selectLight;
export type SelectThemeVars = ReturnType<typeof self>;
export type SelectTheme = typeof selectLight;

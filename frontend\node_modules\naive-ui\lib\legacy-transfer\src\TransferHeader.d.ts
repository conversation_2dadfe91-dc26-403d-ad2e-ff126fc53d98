import { type PropType } from 'vue';
declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    source: {
        type: BooleanConstructor;
        default: boolean;
    };
    onChange: {
        type: PropType<(value: boolean) => void>;
        required: true;
    };
    title: StringConstructor;
}>, () => JSX.Element, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    source: {
        type: BooleanConstructor;
        default: boolean;
    };
    onChange: {
        type: PropType<(value: boolean) => void>;
        required: true;
    };
    title: StringConstructor;
}>> & Readonly<{}>, {
    source: boolean;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;

import type { DialogApiInjection, DialogProviderInjection, DialogReactiveListInjection } from './DialogProvider';
export declare const dialogProviderInjectionKey: import("vue").InjectionKey<DialogProviderInjection>;
export declare const dialogApiInjectionKey: import("vue").InjectionKey<DialogApiInjection>;
export declare const dialogReactiveListInjectionKey: import("vue").InjectionKey<DialogReactiveListInjection>;

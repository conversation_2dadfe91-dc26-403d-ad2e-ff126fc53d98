declare const _default: {
    tabFontSizeSmall: string;
    tabFontSizeMedium: string;
    tabFontSizeLarge: string;
    tabGapSmallLine: string;
    tabGapMediumLine: string;
    tabGapLargeLine: string;
    tabGapSmallLineVertical: string;
    tabGapMediumLineVertical: string;
    tabGapLargeLineVertical: string;
    tabPaddingSmallLine: string;
    tabPaddingMediumLine: string;
    tabPaddingLargeLine: string;
    tabPaddingVerticalSmallLine: string;
    tabPaddingVerticalMediumLine: string;
    tabPaddingVerticalLargeLine: string;
    tabGapSmallBar: string;
    tabGapMediumBar: string;
    tabGapLargeBar: string;
    tabGapSmallBarVertical: string;
    tabGapMediumBarVertical: string;
    tabGapLargeBarVertical: string;
    tabPaddingSmallBar: string;
    tabPaddingMediumBar: string;
    tabPaddingLargeBar: string;
    tabPaddingVerticalSmallBar: string;
    tabPaddingVerticalMediumBar: string;
    tabPaddingVerticalLargeBar: string;
    tabGapSmallCard: string;
    tabGapMediumCard: string;
    tabGapLargeCard: string;
    tabGapSmallCardVertical: string;
    tabGapMediumCardVertical: string;
    tabGapLargeCardVertical: string;
    tabPaddingSmallCard: string;
    tabPaddingMediumCard: string;
    tabPaddingLargeCard: string;
    tabPaddingSmallSegment: string;
    tabPaddingMediumSegment: string;
    tabPaddingLargeSegment: string;
    tabPaddingVerticalLargeSegment: string;
    tabPaddingVerticalSmallCard: string;
    tabPaddingVerticalMediumCard: string;
    tabPaddingVerticalLargeCard: string;
    tabPaddingVerticalSmallSegment: string;
    tabPaddingVerticalMediumSegment: string;
    tabGapSmallSegment: string;
    tabGapMediumSegment: string;
    tabGapLargeSegment: string;
    tabGapSmallSegmentVertical: string;
    tabGapMediumSegmentVertical: string;
    tabGapLargeSegmentVertical: string;
    panePaddingSmall: string;
    panePaddingMedium: string;
    panePaddingLarge: string;
    closeSize: string;
    closeIconSize: string;
};
export default _default;

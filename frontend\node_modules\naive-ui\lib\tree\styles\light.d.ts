import type { ThemeCommonVars } from '../../_styles/common';
export declare function self(vars: ThemeCommonVars): {
    fontSize: string;
    lineHeight: string;
    nodeHeight: string;
    nodeWrapperPadding: string;
    nodeBorderRadius: string;
    nodeColorHover: string;
    nodeColorPressed: string;
    nodeColorActive: string;
    arrowColor: string;
    nodeTextColor: string;
    nodeTextColorDisabled: string;
    loadingColor: string;
    dropMarkColor: string;
    lineColor: string;
};
export type TreeThemeVars = ReturnType<typeof self>;
declare const treeLight: import("../../_mixins/use-theme").Theme<"Tree", {
    fontSize: string;
    lineHeight: string;
    nodeHeight: string;
    nodeWrapperPadding: string;
    nodeBorderRadius: string;
    nodeColorHover: string;
    nodeColorPressed: string;
    nodeColorActive: string;
    arrowColor: string;
    nodeTextColor: string;
    nodeTextColorDisabled: string;
    loadingColor: string;
    dropMarkColor: string;
    lineColor: string;
}, {
    Checkbox: import("../../_mixins/use-theme").Theme<"Checkbox", {
        labelLineHeight: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        borderRadius: string;
        color: string;
        colorChecked: string;
        colorDisabled: string;
        colorDisabledChecked: string;
        colorTableHeader: string;
        colorTableHeaderModal: string;
        colorTableHeaderPopover: string;
        checkMarkColor: string;
        checkMarkColorDisabled: string;
        checkMarkColorDisabledChecked: string;
        border: string;
        borderDisabled: string;
        borderDisabledChecked: string;
        borderChecked: string;
        borderFocus: string;
        boxShadowFocus: string;
        textColor: string;
        textColorDisabled: string;
        sizeSmall: string;
        sizeMedium: string;
        sizeLarge: string;
        labelPadding: string;
        labelFontWeight: string;
    }, any>;
    Scrollbar: import("../../_mixins/use-theme").Theme<"Scrollbar", {
        height: string;
        width: string;
        borderRadius: string;
        color: string;
        colorHover: string;
        railInsetHorizontalBottom: string;
        railInsetHorizontalTop: string;
        railInsetVerticalRight: string;
        railInsetVerticalLeft: string;
        railColor: string;
    }, any>;
    Empty: import("../../_mixins/use-theme").Theme<"Empty", {
        fontSizeTiny: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        fontSizeHuge: string;
        textColor: string;
        iconColor: string;
        extraTextColor: string;
        iconSizeTiny: string;
        iconSizeSmall: string;
        iconSizeMedium: string;
        iconSizeLarge: string;
        iconSizeHuge: string;
    }, any>;
}>;
export default treeLight;
export type TreeTheme = typeof treeLight;

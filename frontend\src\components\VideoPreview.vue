<template>
  <div class="video-preview">
    <!-- 模式切换按钮 -->
    <div class="mode-switcher">
      <button
        class="mode-btn"
        :class="{ active: previewMode === 'legacy' }"
        @click="setPreviewMode('legacy')"
        title="传统模式 - 快速切换"
      >
        🔄 传统模式
      </button>
      <button
        class="mode-btn"
        :class="{ active: previewMode === 'hls' }"
        @click="setPreviewMode('hls')"
        title="流媒体模式 - 无缝播放"
      >
        📡 流媒体模式
      </button>
    </div>

    <!-- HLS流媒体预览 -->
    <HLSPreview
      v-if="previewMode === 'hls'"
      :timeline-config="hlsTimelineConfig"
      :auto-start="false"
      ref="hlsPreview"
      @stream-ready="handleStreamReady"
      @stream-error="handleStreamError"
      @time-update="handleHLSTimeUpdate"
    />

    <!-- 传统预览模式 -->
    <div v-else class="legacy-preview">
      <div class="preview-container">
        <!-- 双缓冲视频元素 -->
        <video
          ref="videoElementA"
          class="preview-video"
          :class="{ active: activeVideoElement === 'A' }"
          @loadedmetadata="handleVideoLoaded"
          @timeupdate="handleTimeUpdate"
          @ended="handleVideoEnded"
          @error="handleVideoError"
          preload="metadata"
          controls
        >
          <source v-if="videoSrcA" :src="videoSrcA" type="video/mp4">
          <source v-if="videoSrcA" :src="videoSrcA" type="video/webm">
          <source v-if="videoSrcA" :src="videoSrcA" type="video/ogg">
          您的浏览器不支持视频播放
        </video>

        <video
          ref="videoElementB"
          class="preview-video"
          :class="{ active: activeVideoElement === 'B' }"
          @loadedmetadata="handleVideoLoaded"
          @timeupdate="handleTimeUpdate"
          @ended="handleVideoEnded"
          @error="handleVideoError"
          preload="metadata"
          controls
        >
          <source v-if="videoSrcB" :src="videoSrcB" type="video/mp4">
          <source v-if="videoSrcB" :src="videoSrcB" type="video/webm">
          <source v-if="videoSrcB" :src="videoSrcB" type="video/ogg">
          您的浏览器不支持视频播放
        </video>

        <!-- 无视频时的占位符 -->
        <div v-if="!currentVideoSrc" class="preview-placeholder">
          <div class="placeholder-content">
            <div class="placeholder-icon">🎬</div>
            <div class="placeholder-text">预览窗口</div>
            <div class="placeholder-subtext">拖拽视频到时间轴开始编辑</div>
          </div>
        </div>

        <!-- 加载状态 -->
        <div v-if="isLoading" class="loading-overlay">
          <div class="loading-spinner">⏳</div>
          <div class="loading-text">加载中...</div>
        </div>
      </div>
    </div>
    
    <!-- 预览信息 -->
    <div v-if="currentVideoSrc" class="preview-info">
      <div class="time-display">
        {{ formatTime(currentTime) }} / {{ formatTime(videoDuration) }}
      </div>
      <div v-if="currentClip" class="clip-info">
        当前片段: {{ getClipName(currentClip) }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onUnmounted } from 'vue'
import { useEditorStore } from '@/stores/editor'
import { storeToRefs } from 'pinia'
import type { TimelineClip } from '@/types'
import HLSPreview from './HLSPreview.vue'

const editorStore = useEditorStore()
const {
  currentTime,
  isPlaying,
  currentClip,
  mediaLibrary,
  videoTrack
} = storeToRefs(editorStore)

// 预览模式
const previewMode = ref<'legacy' | 'hls'>('legacy')
const hlsPreview = ref<InstanceType<typeof HLSPreview>>()

// 双缓冲视频元素（传统模式）
const videoElementA = ref<HTMLVideoElement>()
const videoElementB = ref<HTMLVideoElement>()
const activeVideoElement = ref<'A' | 'B'>('A')
const isLoading = ref(false)
const videoDuration = ref(0)
const playbackTimer = ref<number | null>(null)

// 视频源管理
const videoSrcA = ref<string | null>(null)
const videoSrcB = ref<string | null>(null)
const currentClipId = ref<string | null>(null)
const nextClipId = ref<string | null>(null)

// 当前应该播放的视频源
const currentVideoSrc = computed(() => {
  if (!currentClip.value) return null

  const media = mediaLibrary.value.find(m => m.id === currentClip.value?.mediaId)
  return media?.proxy_url || null
})

// 获取当前活跃的视频元素
const getCurrentVideoElement = (): HTMLVideoElement | undefined => {
  return activeVideoElement.value === 'A' ? videoElementA.value : videoElementB.value
}

// 获取备用视频元素
const getBackupVideoElement = (): HTMLVideoElement | undefined => {
  return activeVideoElement.value === 'A' ? videoElementB.value : videoElementA.value
}

// HLS时间轴配置
const hlsTimelineConfig = computed(() => {
  if (!videoTrack.value || !videoTrack.value.clips.length) {
    return null
  }

  return {
    clips: videoTrack.value.clips.map(clip => ({
      id: clip.id,
      mediaId: clip.mediaId,
      startTime: clip.startTime,
      duration: clip.duration,
      trimStart: clip.trimStart,
      trimEnd: clip.trimEnd
    }))
  }
})

// 预览模式切换
const setPreviewMode = (mode: 'legacy' | 'hls') => {
  if (previewMode.value === mode) return

  // 停止当前播放
  if (isPlaying.value) {
    editorStore.setPlaying(false)
  }

  previewMode.value = mode
  console.log('切换预览模式:', mode)
}

// HLS事件处理
const handleStreamReady = (streamId: string) => {
  console.log('HLS流就绪:', streamId)
}

const handleStreamError = (error: string) => {
  console.error('HLS流错误:', error)
  // 可以考虑自动切换回传统模式
}

const handleHLSTimeUpdate = (currentTime: number) => {
  // HLS播放器的时间更新，可以用来同步时间轴
  console.log('HLS时间更新:', currentTime)
}

// 当前片段在视频中的相对时间
const relativeTime = computed(() => {
  if (!currentClip.value) return 0
  
  const clipTime = currentTime.value - currentClip.value.startTime
  return Math.max(0, Math.min(clipTime + currentClip.value.trimStart, currentClip.value.trimEnd))
})

// 格式化时间显示
const formatTime = (seconds: number): string => {
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

// 获取片段名称
const getClipName = (clip: TimelineClip): string => {
  const media = mediaLibrary.value.find(m => m.id === clip.mediaId)
  return media?.name || 'Unknown'
}

// 预加载下一个片段
const preloadNextClip = () => {
  if (!editorStore.videoTrack) return

  const clips = editorStore.videoTrack.clips
  const currentIndex = clips.findIndex(clip => clip.id === currentClip.value?.id)

  if (currentIndex >= 0 && currentIndex < clips.length - 1) {
    const nextClip = clips[currentIndex + 1]
    const nextMedia = mediaLibrary.value.find(m => m.id === nextClip.mediaId)

    if (nextMedia?.proxy_url) {
      const backupElement = getBackupVideoElement()
      if (backupElement) {
        // 设置备用视频元素的源
        if (activeVideoElement.value === 'A') {
          videoSrcB.value = nextMedia.proxy_url
        } else {
          videoSrcA.value = nextMedia.proxy_url
        }
        nextClipId.value = nextClip.id

        // 预加载视频
        backupElement.load()
        console.log('预加载下一个片段:', nextClip.id, nextMedia.proxy_url)
      }
    }
  }
}

// 切换到下一个视频元素
const switchToNextVideo = () => {
  if (nextClipId.value && nextClipId.value === currentClip.value?.id) {
    // 切换活跃的视频元素
    activeVideoElement.value = activeVideoElement.value === 'A' ? 'B' : 'A'
    console.log('切换到视频元素:', activeVideoElement.value)

    // 更新当前片段ID
    currentClipId.value = nextClipId.value
    nextClipId.value = null

    // 预加载下一个片段
    setTimeout(preloadNextClip, 100)

    return true
  }
  return false
}

// 处理视频加载完成
const handleVideoLoaded = (event: Event) => {
  const videoElement = event.target as HTMLVideoElement
  if (videoElement) {
    videoDuration.value = videoElement.duration
    isLoading.value = false

    // 设置初始播放位置
    videoElement.currentTime = relativeTime.value

    console.log('视频加载完成:', videoElement === getCurrentVideoElement() ? '当前' : '备用', videoElement.src)
  }
}

// 处理视频时间更新 - 简化版本，主要用于检测播放结束
const handleTimeUpdate = () => {
  if (!videoElement.value || !currentClip.value || !isPlaying.value) return

  const videoTime = videoElement.value.currentTime
  const clipEndTime = currentClip.value.trimEnd

  // 如果视频播放超出了片段范围，暂停播放
  if (videoTime >= clipEndTime) {
    editorStore.setPlaying(false)
  }
}

// 处理视频播放结束
const handleVideoEnded = () => {
  editorStore.setPlaying(false)
}

// 处理视频错误
const handleVideoError = (event: Event) => {
  console.error('视频加载错误:', event)
  const video = event.target as HTMLVideoElement
  if (video.error) {
    console.error('错误详情:', {
      code: video.error.code,
      message: video.error.message,
      src: video.currentSrc || video.src
    })
  }
}

// 启动播放定时器
const startPlaybackTimer = () => {
  if (playbackTimer.value) {
    clearInterval(playbackTimer.value)
  }

  playbackTimer.value = window.setInterval(() => {
    if (!isPlaying.value || !videoElement.value || !currentClip.value) {
      stopPlaybackTimer()
      return
    }

    const videoTime = videoElement.value.currentTime
    const clipStartTime = currentClip.value.trimStart
    const clipEndTime = currentClip.value.trimEnd

    // 检查是否超出片段范围
    if (videoTime >= clipEndTime) {
      // 查找下一个片段
      const nextClip = editorStore.videoTrack?.clips.find(clip =>
        clip.startTime > currentClip.value!.startTime + currentClip.value!.duration
      )

      if (nextClip) {
        // 跳转到下一个片段
        editorStore.setCurrentTime(nextClip.startTime)
      } else {
        // 没有下一个片段，停止播放
        editorStore.setPlaying(false)
      }
      return
    }

    // 更新时间轴时间
    const timelineTime = currentClip.value.startTime + (videoTime - clipStartTime)
    editorStore.setCurrentTime(timelineTime)
  }, 100) // 每100ms更新一次
}

// 停止播放定时器
const stopPlaybackTimer = () => {
  if (playbackTimer.value) {
    clearInterval(playbackTimer.value)
    playbackTimer.value = null
  }
}

// 监听播放状态变化
watch(isPlaying, async (playing) => {
  console.log('播放状态变化:', playing, '预览模式:', previewMode.value)

  if (previewMode.value === 'hls') {
    // HLS模式：启动或停止流
    if (playing && hlsTimelineConfig.value) {
      try {
        await hlsPreview.value?.startStream(hlsTimelineConfig.value)
      } catch (error) {
        console.error('启动HLS流失败:', error)
        editorStore.setPlaying(false)
      }
    } else if (!playing) {
      // HLS流会自动处理暂停，这里不需要特殊处理
    }
    return
  }

  // 传统模式的播放逻辑
  const videoElement = getCurrentVideoElement()
  if (!videoElement) {
    console.log('视频元素不存在')
    return
  }

  if (playing) {
    if (!currentClip.value) {
      console.log('没有当前片段，无法播放')
      await nextTick()
      editorStore.setPlaying(false)
      return
    }

    try {
      console.log('开始播放视频')
      if (videoElement.readyState >= 2) {
        await videoElement.play()
        startPlaybackTimer()
      } else {
        console.log('视频未准备好，等待加载')
        const handleCanPlay = () => {
          videoElement?.play().then(() => {
            startPlaybackTimer()
          }).catch(error => {
            console.error('延迟播放失败:', error)
            editorStore.setPlaying(false)
          })
          videoElement?.removeEventListener('canplay', handleCanPlay)
        }
        videoElement.addEventListener('canplay', handleCanPlay)
        editorStore.setPlaying(false)
      }
    } catch (error) {
      console.error('Failed to play video:', error)
      editorStore.setPlaying(false)
    }
  } else {
    console.log('暂停播放')
    if (videoElement && !videoElement.paused) {
      videoElement.pause()
    }
    stopPlaybackTimer()
  }
})

// 监听当前时间变化（用户拖拽时间轴时）
watch(currentTime, (newTime) => {
  if (!videoElement.value || !currentClip.value) return
  
  // 如果视频正在播放，不要干扰自然播放
  if (isPlaying.value) return
  
  // 计算视频中的相对时间
  const clipTime = newTime - currentClip.value.startTime
  const videoTime = Math.max(0, Math.min(
    clipTime + currentClip.value.trimStart, 
    currentClip.value.trimEnd
  ))
  
  // 设置视频播放位置
  if (Math.abs(videoElement.value.currentTime - videoTime) > 0.1) {
    videoElement.value.currentTime = videoTime
  }
})

// 监听当前片段变化
watch(currentClip, async (newClip, oldClip) => {
  if (!videoElement.value) return

  // 如果片段发生变化，需要重新加载视频
  if (newClip?.mediaId !== oldClip?.mediaId) {
    isLoading.value = true

    // 停止当前播放
    if (isPlaying.value) {
      editorStore.setPlaying(false)
    }

    // 等待视频源更新
    await nextTick()

    if (newClip && videoElement.value.src) {
      // 等待视频加载
      videoElement.value.load()
    } else {
      isLoading.value = false
    }
  } else if (newClip && !isPlaying.value) {
    // 同一个媒体文件，只需要调整播放位置（仅在非播放状态下）
    const videoTime = relativeTime.value
    if (Math.abs(videoElement.value.currentTime - videoTime) > 0.1) {
      videoElement.value.currentTime = videoTime
    }
  }
})

// 监听视频源变化
watch(currentVideoSrc, (newSrc) => {
  if (newSrc) {
    isLoading.value = true
  } else {
    isLoading.value = false
    videoDuration.value = 0
  }
})

// 组件卸载时清理定时器
onUnmounted(() => {
  stopPlaybackTimer()
})
</script>

<style scoped>
.video-preview {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #000;
}

.mode-switcher {
  display: flex;
  gap: 8px;
  padding: 8px 12px;
  background-color: #1a1a1a;
  border-bottom: 1px solid #333;
}

.mode-btn {
  background: none;
  border: 1px solid #555;
  color: #ccc;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.mode-btn:hover {
  background-color: #333;
  border-color: #666;
  color: #fff;
}

.mode-btn.active {
  background-color: #007acc;
  border-color: #007acc;
  color: #fff;
}

.legacy-preview {
  flex: 1;
  display: flex;
  flex-direction: column;
}</style>

.preview-container {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-video {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.preview-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.placeholder-content {
  text-align: center;
  color: #666;
}

.placeholder-icon {
  font-size: 64px;
  margin-bottom: 16px;
}

.placeholder-text {
  font-size: 18px;
  margin-bottom: 8px;
}

.placeholder-subtext {
  font-size: 14px;
  color: #888;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #fff;
}

.loading-spinner {
  font-size: 32px;
  margin-bottom: 8px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.loading-text {
  font-size: 14px;
}

.preview-info {
  padding: 8px 16px;
  background-color: rgba(0, 0, 0, 0.8);
  color: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.time-display {
  font-family: monospace;
}

.clip-info {
  color: #ccc;
}
</style>

<template>
  <div class="video-preview">
    <!-- HLS流媒体预览 -->
    <HLSPreview
      :timeline-config="hlsTimelineConfig"
      :auto-start="false"
      ref="hlsPreview"
      @stream-ready="handleStreamReady"
      @stream-error="handleStreamError"
      @time-update="handleHLSTimeUpdate"
    />
    
    <!-- 预览信息 -->
    <div v-if="hlsTimelineConfig" class="preview-info">
      <div class="info-item">
        <span class="label">模式:</span>
        <span class="value">📡 流媒体模式</span>
      </div>
      <div class="info-item">
        <span class="label">片段数:</span>
        <span class="value">{{ hlsTimelineConfig.clips.length }}</span>
      </div>
      <div class="info-item">
        <span class="label">状态:</span>
        <span class="value" :class="statusClass">{{ statusText }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useEditorStore } from '@/stores/editor'
import { storeToRefs } from 'pinia'
import HLSPreview from './HLSPreview.vue'

const editorStore = useEditorStore()
const { 
  isPlaying, 
  videoTrack
} = storeToRefs(editorStore)

// HLS预览组件引用
const hlsPreview = ref<InstanceType<typeof HLSPreview>>()
const streamStatus = ref<'disconnected' | 'starting' | 'ready' | 'error'>('disconnected')

// HLS时间轴配置
const hlsTimelineConfig = computed(() => {
  if (!videoTrack.value || !videoTrack.value.clips.length) {
    return null
  }

  return {
    clips: videoTrack.value.clips.map(clip => ({
      id: clip.id,
      mediaId: clip.mediaId,
      startTime: clip.startTime,
      duration: clip.duration,
      trimStart: clip.trimStart,
      trimEnd: clip.trimEnd
    }))
  }
})

// 状态显示
const statusText = computed(() => {
  switch (streamStatus.value) {
    case 'disconnected': return '未连接'
    case 'starting': return '启动中'
    case 'ready': return '就绪'
    case 'error': return '错误'
    default: return '未知'
  }
})

const statusClass = computed(() => {
  return `status-${streamStatus.value}`
})

// HLS事件处理
const handleStreamReady = (streamId: string) => {
  console.log('HLS流就绪:', streamId)
  streamStatus.value = 'ready'
}

const handleStreamError = (error: string) => {
  console.error('HLS流错误:', error)
  streamStatus.value = 'error'
}

const handleHLSTimeUpdate = (currentTime: number) => {
  console.log('HLS时间更新:', currentTime)
}

// 监听播放状态变化
watch(isPlaying, async (playing) => {
  console.log('播放状态变化:', playing)

  if (playing && hlsTimelineConfig.value) {
    try {
      streamStatus.value = 'starting'
      await hlsPreview.value?.startStream(hlsTimelineConfig.value)
    } catch (error) {
      console.error('启动HLS流失败:', error)
      streamStatus.value = 'error'
      editorStore.setPlaying(false)
    }
  } else if (!playing) {
    streamStatus.value = 'disconnected'
  }
})

// 监听时间轴配置变化
watch(hlsTimelineConfig, (newConfig) => {
  if (!newConfig) {
    streamStatus.value = 'disconnected'
  }
}, { deep: true })
</script>

<style scoped>
.video-preview {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #000;
}

.preview-info {
  padding: 8px 16px;
  background-color: rgba(0, 0, 0, 0.8);
  color: #fff;
  display: flex;
  gap: 16px;
  font-size: 12px;
  border-top: 1px solid #333;
}

.info-item {
  display: flex;
  gap: 4px;
}

.label {
  color: #ccc;
}

.value {
  font-weight: 500;
}

.status-disconnected { color: #666; }
.status-starting { color: #ffa500; }
.status-ready { color: #00ff00; }
.status-error { color: #ff6b6b; }
</style>

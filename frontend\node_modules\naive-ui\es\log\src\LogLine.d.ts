declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    line: {
        type: StringConstructor;
        default: string;
    };
}>, {
    highlight: import("vue").Ref<boolean, boolean>;
    selfRef: import("vue").Ref<HTMLElement | null, HTMLElement | null>;
    maybeTrimmedLines: import("vue").ComputedRef<string>;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    line: {
        type: StringConstructor;
        default: string;
    };
}>> & Readonly<{}>, {
    line: string;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;

import type { MaybeArray } from '../../_utils';
import type { DropdownProps } from '../../dropdown';
import type { MenuGroupOption, MenuIgnoredOption, MenuMixedOption, MenuNodeProps, MenuOption, OnUpdateKeys, OnUpdateValue } from './interface';
import { type Key } from 'treemate';
import { type ExtractPropTypes, type PropType, type VNodeChild } from 'vue';
import { type FollowerPlacement, type VOverflowInst } from 'vueuc';
export declare const menuProps: {
    readonly options: {
        readonly type: PropType<MenuMixedOption[]>;
        readonly default: () => never[];
    };
    readonly collapsed: {
        readonly type: PropType<boolean | undefined>;
        readonly default: undefined;
    };
    readonly collapsedWidth: {
        readonly type: NumberConstructor;
        readonly default: 48;
    };
    readonly iconSize: {
        readonly type: NumberConstructor;
        readonly default: 20;
    };
    readonly collapsedIconSize: {
        readonly type: NumberConstructor;
        readonly default: 24;
    };
    readonly rootIndent: NumberConstructor;
    readonly indent: {
        readonly type: NumberConstructor;
        readonly default: 32;
    };
    readonly labelField: {
        readonly type: StringConstructor;
        readonly default: "label";
    };
    readonly keyField: {
        readonly type: StringConstructor;
        readonly default: "key";
    };
    readonly childrenField: {
        readonly type: StringConstructor;
        readonly default: "children";
    };
    readonly disabledField: {
        readonly type: StringConstructor;
        readonly default: "disabled";
    };
    readonly defaultExpandAll: BooleanConstructor;
    readonly defaultExpandedKeys: PropType<Key[]>;
    readonly expandedKeys: PropType<Key[]>;
    readonly value: PropType<Key | null>;
    readonly defaultValue: {
        readonly type: PropType<Key | null>;
        readonly default: null;
    };
    readonly mode: {
        readonly type: PropType<"vertical" | "horizontal">;
        readonly default: "vertical";
    };
    readonly watchProps: {
        readonly type: PropType<Array<"defaultExpandedKeys" | "defaultValue">>;
        readonly default: undefined;
    };
    readonly disabled: BooleanConstructor;
    readonly show: {
        readonly type: BooleanConstructor;
        readonly default: true;
    };
    readonly inverted: BooleanConstructor;
    readonly 'onUpdate:expandedKeys': PropType<MaybeArray<OnUpdateKeys>>;
    readonly onUpdateExpandedKeys: PropType<MaybeArray<OnUpdateKeys>>;
    readonly onUpdateValue: PropType<MaybeArray<OnUpdateValue>>;
    readonly 'onUpdate:value': PropType<MaybeArray<OnUpdateValue>>;
    readonly expandIcon: PropType<(option: MenuOption) => VNodeChild>;
    readonly renderIcon: PropType<(option: MenuOption) => VNodeChild>;
    readonly renderLabel: PropType<(option: MenuOption | MenuGroupOption) => VNodeChild>;
    readonly renderExtra: PropType<(option: MenuOption | MenuGroupOption) => VNodeChild>;
    readonly dropdownProps: PropType<DropdownProps>;
    readonly accordion: BooleanConstructor;
    readonly nodeProps: PropType<MenuNodeProps>;
    readonly dropdownPlacement: {
        readonly type: PropType<FollowerPlacement>;
        readonly default: "bottom";
    };
    readonly responsive: BooleanConstructor;
    readonly items: PropType<Array<MenuOption | MenuGroupOption>>;
    readonly onOpenNamesChange: PropType<MaybeArray<OnUpdateKeys>>;
    readonly onSelect: PropType<MaybeArray<OnUpdateValue>>;
    readonly onExpandedNamesChange: PropType<MaybeArray<OnUpdateKeys>>;
    readonly expandedNames: PropType<Key[]>;
    readonly defaultExpandedNames: PropType<Key[]>;
    readonly theme: PropType<import("../../_mixins").Theme<"Menu", {
        itemColorHoverInverted: string;
        itemColorActiveInverted: string;
        itemColorActiveHoverInverted: string;
        itemColorActiveCollapsedInverted: string;
        itemTextColorInverted: string;
        itemTextColorHoverInverted: string;
        itemTextColorChildActiveInverted: string;
        itemTextColorChildActiveHoverInverted: string;
        itemTextColorActiveInverted: string;
        itemTextColorActiveHoverInverted: string;
        itemTextColorHorizontalInverted: string;
        itemTextColorHoverHorizontalInverted: string;
        itemTextColorChildActiveHorizontalInverted: string;
        itemTextColorChildActiveHoverHorizontalInverted: string;
        itemTextColorActiveHorizontalInverted: string;
        itemTextColorActiveHoverHorizontalInverted: string;
        itemIconColorInverted: string;
        itemIconColorHoverInverted: string;
        itemIconColorActiveInverted: string;
        itemIconColorActiveHoverInverted: string;
        itemIconColorChildActiveInverted: string;
        itemIconColorChildActiveHoverInverted: string;
        itemIconColorCollapsedInverted: string;
        itemIconColorHorizontalInverted: string;
        itemIconColorHoverHorizontalInverted: string;
        itemIconColorActiveHorizontalInverted: string;
        itemIconColorActiveHoverHorizontalInverted: string;
        itemIconColorChildActiveHorizontalInverted: string;
        itemIconColorChildActiveHoverHorizontalInverted: string;
        arrowColorInverted: string;
        arrowColorHoverInverted: string;
        arrowColorActiveInverted: string;
        arrowColorActiveHoverInverted: string;
        arrowColorChildActiveInverted: string;
        arrowColorChildActiveHoverInverted: string;
        groupTextColorInverted: string;
        borderRadius: string;
        color: string;
        groupTextColor: string;
        itemColorHover: string;
        itemColorActive: string;
        itemColorActiveHover: string;
        itemColorActiveCollapsed: string;
        itemTextColor: string;
        itemTextColorHover: string;
        itemTextColorActive: string;
        itemTextColorActiveHover: string;
        itemTextColorChildActive: string;
        itemTextColorChildActiveHover: string;
        itemTextColorHorizontal: string;
        itemTextColorHoverHorizontal: string;
        itemTextColorActiveHorizontal: string;
        itemTextColorActiveHoverHorizontal: string;
        itemTextColorChildActiveHorizontal: string;
        itemTextColorChildActiveHoverHorizontal: string;
        itemIconColor: string;
        itemIconColorHover: string;
        itemIconColorActive: string;
        itemIconColorActiveHover: string;
        itemIconColorChildActive: string;
        itemIconColorChildActiveHover: string;
        itemIconColorCollapsed: string;
        itemIconColorHorizontal: string;
        itemIconColorHoverHorizontal: string;
        itemIconColorActiveHorizontal: string;
        itemIconColorActiveHoverHorizontal: string;
        itemIconColorChildActiveHorizontal: string;
        itemIconColorChildActiveHoverHorizontal: string;
        itemHeight: string;
        arrowColor: string;
        arrowColorHover: string;
        arrowColorActive: string;
        arrowColorActiveHover: string;
        arrowColorChildActive: string;
        arrowColorChildActiveHover: string;
        colorInverted: string;
        borderColorHorizontal: string;
        fontSize: string;
        dividerColor: string;
    }, {
        Tooltip: import("../../_mixins").Theme<"Tooltip", {
            borderRadius: string;
            boxShadow: string;
            color: string;
            textColor: string;
            padding: string;
        }, {
            Popover: import("../../_mixins").Theme<"Popover", {
                fontSize: string;
                borderRadius: string;
                color: string;
                dividerColor: string;
                textColor: string;
                boxShadow: string;
                space: string;
                spaceArrow: string;
                arrowOffset: string;
                arrowOffsetVertical: string;
                arrowHeight: string;
                padding: string;
            }, any>;
        }>;
        Dropdown: import("../../_mixins").Theme<"Dropdown", {
            optionHeightSmall: string;
            optionHeightMedium: string;
            optionHeightLarge: string;
            optionHeightHuge: string;
            borderRadius: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            fontSizeHuge: string;
            optionTextColor: string;
            optionTextColorHover: string;
            optionTextColorActive: string;
            optionTextColorChildActive: string;
            color: string;
            dividerColor: string;
            suffixColor: string;
            prefixColor: string;
            optionColorHover: string;
            optionColorActive: string;
            groupHeaderTextColor: string;
            optionTextColorInverted: string;
            optionTextColorHoverInverted: string;
            optionTextColorActiveInverted: string;
            optionTextColorChildActiveInverted: string;
            colorInverted: string;
            dividerColorInverted: string;
            suffixColorInverted: string;
            prefixColorInverted: string;
            optionColorHoverInverted: string;
            optionColorActiveInverted: string;
            groupHeaderTextColorInverted: string;
            optionOpacityDisabled: string;
            padding: string;
            optionIconSizeSmall: string;
            optionIconSizeMedium: string;
            optionIconSizeLarge: string;
            optionIconSizeHuge: string;
            optionSuffixWidthSmall: string;
            optionSuffixWidthMedium: string;
            optionSuffixWidthLarge: string;
            optionSuffixWidthHuge: string;
            optionIconSuffixWidthSmall: string;
            optionIconSuffixWidthMedium: string;
            optionIconSuffixWidthLarge: string;
            optionIconSuffixWidthHuge: string;
            optionPrefixWidthSmall: string;
            optionPrefixWidthMedium: string;
            optionPrefixWidthLarge: string;
            optionPrefixWidthHuge: string;
            optionIconPrefixWidthSmall: string;
            optionIconPrefixWidthMedium: string;
            optionIconPrefixWidthLarge: string;
            optionIconPrefixWidthHuge: string;
        }, {
            Popover: import("../../_mixins").Theme<"Popover", {
                fontSize: string;
                borderRadius: string;
                color: string;
                dividerColor: string;
                textColor: string;
                boxShadow: string;
                space: string;
                spaceArrow: string;
                arrowOffset: string;
                arrowOffsetVertical: string;
                arrowHeight: string;
                padding: string;
            }, any>;
        }>;
    }>>;
    readonly themeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Menu", {
        itemColorHoverInverted: string;
        itemColorActiveInverted: string;
        itemColorActiveHoverInverted: string;
        itemColorActiveCollapsedInverted: string;
        itemTextColorInverted: string;
        itemTextColorHoverInverted: string;
        itemTextColorChildActiveInverted: string;
        itemTextColorChildActiveHoverInverted: string;
        itemTextColorActiveInverted: string;
        itemTextColorActiveHoverInverted: string;
        itemTextColorHorizontalInverted: string;
        itemTextColorHoverHorizontalInverted: string;
        itemTextColorChildActiveHorizontalInverted: string;
        itemTextColorChildActiveHoverHorizontalInverted: string;
        itemTextColorActiveHorizontalInverted: string;
        itemTextColorActiveHoverHorizontalInverted: string;
        itemIconColorInverted: string;
        itemIconColorHoverInverted: string;
        itemIconColorActiveInverted: string;
        itemIconColorActiveHoverInverted: string;
        itemIconColorChildActiveInverted: string;
        itemIconColorChildActiveHoverInverted: string;
        itemIconColorCollapsedInverted: string;
        itemIconColorHorizontalInverted: string;
        itemIconColorHoverHorizontalInverted: string;
        itemIconColorActiveHorizontalInverted: string;
        itemIconColorActiveHoverHorizontalInverted: string;
        itemIconColorChildActiveHorizontalInverted: string;
        itemIconColorChildActiveHoverHorizontalInverted: string;
        arrowColorInverted: string;
        arrowColorHoverInverted: string;
        arrowColorActiveInverted: string;
        arrowColorActiveHoverInverted: string;
        arrowColorChildActiveInverted: string;
        arrowColorChildActiveHoverInverted: string;
        groupTextColorInverted: string;
        borderRadius: string;
        color: string;
        groupTextColor: string;
        itemColorHover: string;
        itemColorActive: string;
        itemColorActiveHover: string;
        itemColorActiveCollapsed: string;
        itemTextColor: string;
        itemTextColorHover: string;
        itemTextColorActive: string;
        itemTextColorActiveHover: string;
        itemTextColorChildActive: string;
        itemTextColorChildActiveHover: string;
        itemTextColorHorizontal: string;
        itemTextColorHoverHorizontal: string;
        itemTextColorActiveHorizontal: string;
        itemTextColorActiveHoverHorizontal: string;
        itemTextColorChildActiveHorizontal: string;
        itemTextColorChildActiveHoverHorizontal: string;
        itemIconColor: string;
        itemIconColorHover: string;
        itemIconColorActive: string;
        itemIconColorActiveHover: string;
        itemIconColorChildActive: string;
        itemIconColorChildActiveHover: string;
        itemIconColorCollapsed: string;
        itemIconColorHorizontal: string;
        itemIconColorHoverHorizontal: string;
        itemIconColorActiveHorizontal: string;
        itemIconColorActiveHoverHorizontal: string;
        itemIconColorChildActiveHorizontal: string;
        itemIconColorChildActiveHoverHorizontal: string;
        itemHeight: string;
        arrowColor: string;
        arrowColorHover: string;
        arrowColorActive: string;
        arrowColorActiveHover: string;
        arrowColorChildActive: string;
        arrowColorChildActiveHover: string;
        colorInverted: string;
        borderColorHorizontal: string;
        fontSize: string;
        dividerColor: string;
    }, {
        Tooltip: import("../../_mixins").Theme<"Tooltip", {
            borderRadius: string;
            boxShadow: string;
            color: string;
            textColor: string;
            padding: string;
        }, {
            Popover: import("../../_mixins").Theme<"Popover", {
                fontSize: string;
                borderRadius: string;
                color: string;
                dividerColor: string;
                textColor: string;
                boxShadow: string;
                space: string;
                spaceArrow: string;
                arrowOffset: string;
                arrowOffsetVertical: string;
                arrowHeight: string;
                padding: string;
            }, any>;
        }>;
        Dropdown: import("../../_mixins").Theme<"Dropdown", {
            optionHeightSmall: string;
            optionHeightMedium: string;
            optionHeightLarge: string;
            optionHeightHuge: string;
            borderRadius: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            fontSizeHuge: string;
            optionTextColor: string;
            optionTextColorHover: string;
            optionTextColorActive: string;
            optionTextColorChildActive: string;
            color: string;
            dividerColor: string;
            suffixColor: string;
            prefixColor: string;
            optionColorHover: string;
            optionColorActive: string;
            groupHeaderTextColor: string;
            optionTextColorInverted: string;
            optionTextColorHoverInverted: string;
            optionTextColorActiveInverted: string;
            optionTextColorChildActiveInverted: string;
            colorInverted: string;
            dividerColorInverted: string;
            suffixColorInverted: string;
            prefixColorInverted: string;
            optionColorHoverInverted: string;
            optionColorActiveInverted: string;
            groupHeaderTextColorInverted: string;
            optionOpacityDisabled: string;
            padding: string;
            optionIconSizeSmall: string;
            optionIconSizeMedium: string;
            optionIconSizeLarge: string;
            optionIconSizeHuge: string;
            optionSuffixWidthSmall: string;
            optionSuffixWidthMedium: string;
            optionSuffixWidthLarge: string;
            optionSuffixWidthHuge: string;
            optionIconSuffixWidthSmall: string;
            optionIconSuffixWidthMedium: string;
            optionIconSuffixWidthLarge: string;
            optionIconSuffixWidthHuge: string;
            optionPrefixWidthSmall: string;
            optionPrefixWidthMedium: string;
            optionPrefixWidthLarge: string;
            optionPrefixWidthHuge: string;
            optionIconPrefixWidthSmall: string;
            optionIconPrefixWidthMedium: string;
            optionIconPrefixWidthLarge: string;
            optionIconPrefixWidthHuge: string;
        }, {
            Popover: import("../../_mixins").Theme<"Popover", {
                fontSize: string;
                borderRadius: string;
                color: string;
                dividerColor: string;
                textColor: string;
                boxShadow: string;
                space: string;
                spaceArrow: string;
                arrowOffset: string;
                arrowOffsetVertical: string;
                arrowHeight: string;
                padding: string;
            }, any>;
        }>;
    }>>>;
    readonly builtinThemeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Menu", {
        itemColorHoverInverted: string;
        itemColorActiveInverted: string;
        itemColorActiveHoverInverted: string;
        itemColorActiveCollapsedInverted: string;
        itemTextColorInverted: string;
        itemTextColorHoverInverted: string;
        itemTextColorChildActiveInverted: string;
        itemTextColorChildActiveHoverInverted: string;
        itemTextColorActiveInverted: string;
        itemTextColorActiveHoverInverted: string;
        itemTextColorHorizontalInverted: string;
        itemTextColorHoverHorizontalInverted: string;
        itemTextColorChildActiveHorizontalInverted: string;
        itemTextColorChildActiveHoverHorizontalInverted: string;
        itemTextColorActiveHorizontalInverted: string;
        itemTextColorActiveHoverHorizontalInverted: string;
        itemIconColorInverted: string;
        itemIconColorHoverInverted: string;
        itemIconColorActiveInverted: string;
        itemIconColorActiveHoverInverted: string;
        itemIconColorChildActiveInverted: string;
        itemIconColorChildActiveHoverInverted: string;
        itemIconColorCollapsedInverted: string;
        itemIconColorHorizontalInverted: string;
        itemIconColorHoverHorizontalInverted: string;
        itemIconColorActiveHorizontalInverted: string;
        itemIconColorActiveHoverHorizontalInverted: string;
        itemIconColorChildActiveHorizontalInverted: string;
        itemIconColorChildActiveHoverHorizontalInverted: string;
        arrowColorInverted: string;
        arrowColorHoverInverted: string;
        arrowColorActiveInverted: string;
        arrowColorActiveHoverInverted: string;
        arrowColorChildActiveInverted: string;
        arrowColorChildActiveHoverInverted: string;
        groupTextColorInverted: string;
        borderRadius: string;
        color: string;
        groupTextColor: string;
        itemColorHover: string;
        itemColorActive: string;
        itemColorActiveHover: string;
        itemColorActiveCollapsed: string;
        itemTextColor: string;
        itemTextColorHover: string;
        itemTextColorActive: string;
        itemTextColorActiveHover: string;
        itemTextColorChildActive: string;
        itemTextColorChildActiveHover: string;
        itemTextColorHorizontal: string;
        itemTextColorHoverHorizontal: string;
        itemTextColorActiveHorizontal: string;
        itemTextColorActiveHoverHorizontal: string;
        itemTextColorChildActiveHorizontal: string;
        itemTextColorChildActiveHoverHorizontal: string;
        itemIconColor: string;
        itemIconColorHover: string;
        itemIconColorActive: string;
        itemIconColorActiveHover: string;
        itemIconColorChildActive: string;
        itemIconColorChildActiveHover: string;
        itemIconColorCollapsed: string;
        itemIconColorHorizontal: string;
        itemIconColorHoverHorizontal: string;
        itemIconColorActiveHorizontal: string;
        itemIconColorActiveHoverHorizontal: string;
        itemIconColorChildActiveHorizontal: string;
        itemIconColorChildActiveHoverHorizontal: string;
        itemHeight: string;
        arrowColor: string;
        arrowColorHover: string;
        arrowColorActive: string;
        arrowColorActiveHover: string;
        arrowColorChildActive: string;
        arrowColorChildActiveHover: string;
        colorInverted: string;
        borderColorHorizontal: string;
        fontSize: string;
        dividerColor: string;
    }, {
        Tooltip: import("../../_mixins").Theme<"Tooltip", {
            borderRadius: string;
            boxShadow: string;
            color: string;
            textColor: string;
            padding: string;
        }, {
            Popover: import("../../_mixins").Theme<"Popover", {
                fontSize: string;
                borderRadius: string;
                color: string;
                dividerColor: string;
                textColor: string;
                boxShadow: string;
                space: string;
                spaceArrow: string;
                arrowOffset: string;
                arrowOffsetVertical: string;
                arrowHeight: string;
                padding: string;
            }, any>;
        }>;
        Dropdown: import("../../_mixins").Theme<"Dropdown", {
            optionHeightSmall: string;
            optionHeightMedium: string;
            optionHeightLarge: string;
            optionHeightHuge: string;
            borderRadius: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            fontSizeHuge: string;
            optionTextColor: string;
            optionTextColorHover: string;
            optionTextColorActive: string;
            optionTextColorChildActive: string;
            color: string;
            dividerColor: string;
            suffixColor: string;
            prefixColor: string;
            optionColorHover: string;
            optionColorActive: string;
            groupHeaderTextColor: string;
            optionTextColorInverted: string;
            optionTextColorHoverInverted: string;
            optionTextColorActiveInverted: string;
            optionTextColorChildActiveInverted: string;
            colorInverted: string;
            dividerColorInverted: string;
            suffixColorInverted: string;
            prefixColorInverted: string;
            optionColorHoverInverted: string;
            optionColorActiveInverted: string;
            groupHeaderTextColorInverted: string;
            optionOpacityDisabled: string;
            padding: string;
            optionIconSizeSmall: string;
            optionIconSizeMedium: string;
            optionIconSizeLarge: string;
            optionIconSizeHuge: string;
            optionSuffixWidthSmall: string;
            optionSuffixWidthMedium: string;
            optionSuffixWidthLarge: string;
            optionSuffixWidthHuge: string;
            optionIconSuffixWidthSmall: string;
            optionIconSuffixWidthMedium: string;
            optionIconSuffixWidthLarge: string;
            optionIconSuffixWidthHuge: string;
            optionPrefixWidthSmall: string;
            optionPrefixWidthMedium: string;
            optionPrefixWidthLarge: string;
            optionPrefixWidthHuge: string;
            optionIconPrefixWidthSmall: string;
            optionIconPrefixWidthMedium: string;
            optionIconPrefixWidthLarge: string;
            optionIconPrefixWidthHuge: string;
        }, {
            Popover: import("../../_mixins").Theme<"Popover", {
                fontSize: string;
                borderRadius: string;
                color: string;
                dividerColor: string;
                textColor: string;
                boxShadow: string;
                space: string;
                spaceArrow: string;
                arrowOffset: string;
                arrowOffsetVertical: string;
                arrowHeight: string;
                padding: string;
            }, any>;
        }>;
    }>>>;
};
export type MenuSetupProps = ExtractPropTypes<typeof menuProps>;
export type MenuProps = Partial<MenuSetupProps>;
declare const _default: import("vue").DefineComponent<ExtractPropTypes<{
    readonly options: {
        readonly type: PropType<MenuMixedOption[]>;
        readonly default: () => never[];
    };
    readonly collapsed: {
        readonly type: PropType<boolean | undefined>;
        readonly default: undefined;
    };
    readonly collapsedWidth: {
        readonly type: NumberConstructor;
        readonly default: 48;
    };
    readonly iconSize: {
        readonly type: NumberConstructor;
        readonly default: 20;
    };
    readonly collapsedIconSize: {
        readonly type: NumberConstructor;
        readonly default: 24;
    };
    readonly rootIndent: NumberConstructor;
    readonly indent: {
        readonly type: NumberConstructor;
        readonly default: 32;
    };
    readonly labelField: {
        readonly type: StringConstructor;
        readonly default: "label";
    };
    readonly keyField: {
        readonly type: StringConstructor;
        readonly default: "key";
    };
    readonly childrenField: {
        readonly type: StringConstructor;
        readonly default: "children";
    };
    readonly disabledField: {
        readonly type: StringConstructor;
        readonly default: "disabled";
    };
    readonly defaultExpandAll: BooleanConstructor;
    readonly defaultExpandedKeys: PropType<Key[]>;
    readonly expandedKeys: PropType<Key[]>;
    readonly value: PropType<Key | null>;
    readonly defaultValue: {
        readonly type: PropType<Key | null>;
        readonly default: null;
    };
    readonly mode: {
        readonly type: PropType<"vertical" | "horizontal">;
        readonly default: "vertical";
    };
    readonly watchProps: {
        readonly type: PropType<Array<"defaultExpandedKeys" | "defaultValue">>;
        readonly default: undefined;
    };
    readonly disabled: BooleanConstructor;
    readonly show: {
        readonly type: BooleanConstructor;
        readonly default: true;
    };
    readonly inverted: BooleanConstructor;
    readonly 'onUpdate:expandedKeys': PropType<MaybeArray<OnUpdateKeys>>;
    readonly onUpdateExpandedKeys: PropType<MaybeArray<OnUpdateKeys>>;
    readonly onUpdateValue: PropType<MaybeArray<OnUpdateValue>>;
    readonly 'onUpdate:value': PropType<MaybeArray<OnUpdateValue>>;
    readonly expandIcon: PropType<(option: MenuOption) => VNodeChild>;
    readonly renderIcon: PropType<(option: MenuOption) => VNodeChild>;
    readonly renderLabel: PropType<(option: MenuOption | MenuGroupOption) => VNodeChild>;
    readonly renderExtra: PropType<(option: MenuOption | MenuGroupOption) => VNodeChild>;
    readonly dropdownProps: PropType<DropdownProps>;
    readonly accordion: BooleanConstructor;
    readonly nodeProps: PropType<MenuNodeProps>;
    readonly dropdownPlacement: {
        readonly type: PropType<FollowerPlacement>;
        readonly default: "bottom";
    };
    readonly responsive: BooleanConstructor;
    readonly items: PropType<Array<MenuOption | MenuGroupOption>>;
    readonly onOpenNamesChange: PropType<MaybeArray<OnUpdateKeys>>;
    readonly onSelect: PropType<MaybeArray<OnUpdateValue>>;
    readonly onExpandedNamesChange: PropType<MaybeArray<OnUpdateKeys>>;
    readonly expandedNames: PropType<Key[]>;
    readonly defaultExpandedNames: PropType<Key[]>;
    readonly theme: PropType<import("../../_mixins").Theme<"Menu", {
        itemColorHoverInverted: string;
        itemColorActiveInverted: string;
        itemColorActiveHoverInverted: string;
        itemColorActiveCollapsedInverted: string;
        itemTextColorInverted: string;
        itemTextColorHoverInverted: string;
        itemTextColorChildActiveInverted: string;
        itemTextColorChildActiveHoverInverted: string;
        itemTextColorActiveInverted: string;
        itemTextColorActiveHoverInverted: string;
        itemTextColorHorizontalInverted: string;
        itemTextColorHoverHorizontalInverted: string;
        itemTextColorChildActiveHorizontalInverted: string;
        itemTextColorChildActiveHoverHorizontalInverted: string;
        itemTextColorActiveHorizontalInverted: string;
        itemTextColorActiveHoverHorizontalInverted: string;
        itemIconColorInverted: string;
        itemIconColorHoverInverted: string;
        itemIconColorActiveInverted: string;
        itemIconColorActiveHoverInverted: string;
        itemIconColorChildActiveInverted: string;
        itemIconColorChildActiveHoverInverted: string;
        itemIconColorCollapsedInverted: string;
        itemIconColorHorizontalInverted: string;
        itemIconColorHoverHorizontalInverted: string;
        itemIconColorActiveHorizontalInverted: string;
        itemIconColorActiveHoverHorizontalInverted: string;
        itemIconColorChildActiveHorizontalInverted: string;
        itemIconColorChildActiveHoverHorizontalInverted: string;
        arrowColorInverted: string;
        arrowColorHoverInverted: string;
        arrowColorActiveInverted: string;
        arrowColorActiveHoverInverted: string;
        arrowColorChildActiveInverted: string;
        arrowColorChildActiveHoverInverted: string;
        groupTextColorInverted: string;
        borderRadius: string;
        color: string;
        groupTextColor: string;
        itemColorHover: string;
        itemColorActive: string;
        itemColorActiveHover: string;
        itemColorActiveCollapsed: string;
        itemTextColor: string;
        itemTextColorHover: string;
        itemTextColorActive: string;
        itemTextColorActiveHover: string;
        itemTextColorChildActive: string;
        itemTextColorChildActiveHover: string;
        itemTextColorHorizontal: string;
        itemTextColorHoverHorizontal: string;
        itemTextColorActiveHorizontal: string;
        itemTextColorActiveHoverHorizontal: string;
        itemTextColorChildActiveHorizontal: string;
        itemTextColorChildActiveHoverHorizontal: string;
        itemIconColor: string;
        itemIconColorHover: string;
        itemIconColorActive: string;
        itemIconColorActiveHover: string;
        itemIconColorChildActive: string;
        itemIconColorChildActiveHover: string;
        itemIconColorCollapsed: string;
        itemIconColorHorizontal: string;
        itemIconColorHoverHorizontal: string;
        itemIconColorActiveHorizontal: string;
        itemIconColorActiveHoverHorizontal: string;
        itemIconColorChildActiveHorizontal: string;
        itemIconColorChildActiveHoverHorizontal: string;
        itemHeight: string;
        arrowColor: string;
        arrowColorHover: string;
        arrowColorActive: string;
        arrowColorActiveHover: string;
        arrowColorChildActive: string;
        arrowColorChildActiveHover: string;
        colorInverted: string;
        borderColorHorizontal: string;
        fontSize: string;
        dividerColor: string;
    }, {
        Tooltip: import("../../_mixins").Theme<"Tooltip", {
            borderRadius: string;
            boxShadow: string;
            color: string;
            textColor: string;
            padding: string;
        }, {
            Popover: import("../../_mixins").Theme<"Popover", {
                fontSize: string;
                borderRadius: string;
                color: string;
                dividerColor: string;
                textColor: string;
                boxShadow: string;
                space: string;
                spaceArrow: string;
                arrowOffset: string;
                arrowOffsetVertical: string;
                arrowHeight: string;
                padding: string;
            }, any>;
        }>;
        Dropdown: import("../../_mixins").Theme<"Dropdown", {
            optionHeightSmall: string;
            optionHeightMedium: string;
            optionHeightLarge: string;
            optionHeightHuge: string;
            borderRadius: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            fontSizeHuge: string;
            optionTextColor: string;
            optionTextColorHover: string;
            optionTextColorActive: string;
            optionTextColorChildActive: string;
            color: string;
            dividerColor: string;
            suffixColor: string;
            prefixColor: string;
            optionColorHover: string;
            optionColorActive: string;
            groupHeaderTextColor: string;
            optionTextColorInverted: string;
            optionTextColorHoverInverted: string;
            optionTextColorActiveInverted: string;
            optionTextColorChildActiveInverted: string;
            colorInverted: string;
            dividerColorInverted: string;
            suffixColorInverted: string;
            prefixColorInverted: string;
            optionColorHoverInverted: string;
            optionColorActiveInverted: string;
            groupHeaderTextColorInverted: string;
            optionOpacityDisabled: string;
            padding: string;
            optionIconSizeSmall: string;
            optionIconSizeMedium: string;
            optionIconSizeLarge: string;
            optionIconSizeHuge: string;
            optionSuffixWidthSmall: string;
            optionSuffixWidthMedium: string;
            optionSuffixWidthLarge: string;
            optionSuffixWidthHuge: string;
            optionIconSuffixWidthSmall: string;
            optionIconSuffixWidthMedium: string;
            optionIconSuffixWidthLarge: string;
            optionIconSuffixWidthHuge: string;
            optionPrefixWidthSmall: string;
            optionPrefixWidthMedium: string;
            optionPrefixWidthLarge: string;
            optionPrefixWidthHuge: string;
            optionIconPrefixWidthSmall: string;
            optionIconPrefixWidthMedium: string;
            optionIconPrefixWidthLarge: string;
            optionIconPrefixWidthHuge: string;
        }, {
            Popover: import("../../_mixins").Theme<"Popover", {
                fontSize: string;
                borderRadius: string;
                color: string;
                dividerColor: string;
                textColor: string;
                boxShadow: string;
                space: string;
                spaceArrow: string;
                arrowOffset: string;
                arrowOffsetVertical: string;
                arrowHeight: string;
                padding: string;
            }, any>;
        }>;
    }>>;
    readonly themeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Menu", {
        itemColorHoverInverted: string;
        itemColorActiveInverted: string;
        itemColorActiveHoverInverted: string;
        itemColorActiveCollapsedInverted: string;
        itemTextColorInverted: string;
        itemTextColorHoverInverted: string;
        itemTextColorChildActiveInverted: string;
        itemTextColorChildActiveHoverInverted: string;
        itemTextColorActiveInverted: string;
        itemTextColorActiveHoverInverted: string;
        itemTextColorHorizontalInverted: string;
        itemTextColorHoverHorizontalInverted: string;
        itemTextColorChildActiveHorizontalInverted: string;
        itemTextColorChildActiveHoverHorizontalInverted: string;
        itemTextColorActiveHorizontalInverted: string;
        itemTextColorActiveHoverHorizontalInverted: string;
        itemIconColorInverted: string;
        itemIconColorHoverInverted: string;
        itemIconColorActiveInverted: string;
        itemIconColorActiveHoverInverted: string;
        itemIconColorChildActiveInverted: string;
        itemIconColorChildActiveHoverInverted: string;
        itemIconColorCollapsedInverted: string;
        itemIconColorHorizontalInverted: string;
        itemIconColorHoverHorizontalInverted: string;
        itemIconColorActiveHorizontalInverted: string;
        itemIconColorActiveHoverHorizontalInverted: string;
        itemIconColorChildActiveHorizontalInverted: string;
        itemIconColorChildActiveHoverHorizontalInverted: string;
        arrowColorInverted: string;
        arrowColorHoverInverted: string;
        arrowColorActiveInverted: string;
        arrowColorActiveHoverInverted: string;
        arrowColorChildActiveInverted: string;
        arrowColorChildActiveHoverInverted: string;
        groupTextColorInverted: string;
        borderRadius: string;
        color: string;
        groupTextColor: string;
        itemColorHover: string;
        itemColorActive: string;
        itemColorActiveHover: string;
        itemColorActiveCollapsed: string;
        itemTextColor: string;
        itemTextColorHover: string;
        itemTextColorActive: string;
        itemTextColorActiveHover: string;
        itemTextColorChildActive: string;
        itemTextColorChildActiveHover: string;
        itemTextColorHorizontal: string;
        itemTextColorHoverHorizontal: string;
        itemTextColorActiveHorizontal: string;
        itemTextColorActiveHoverHorizontal: string;
        itemTextColorChildActiveHorizontal: string;
        itemTextColorChildActiveHoverHorizontal: string;
        itemIconColor: string;
        itemIconColorHover: string;
        itemIconColorActive: string;
        itemIconColorActiveHover: string;
        itemIconColorChildActive: string;
        itemIconColorChildActiveHover: string;
        itemIconColorCollapsed: string;
        itemIconColorHorizontal: string;
        itemIconColorHoverHorizontal: string;
        itemIconColorActiveHorizontal: string;
        itemIconColorActiveHoverHorizontal: string;
        itemIconColorChildActiveHorizontal: string;
        itemIconColorChildActiveHoverHorizontal: string;
        itemHeight: string;
        arrowColor: string;
        arrowColorHover: string;
        arrowColorActive: string;
        arrowColorActiveHover: string;
        arrowColorChildActive: string;
        arrowColorChildActiveHover: string;
        colorInverted: string;
        borderColorHorizontal: string;
        fontSize: string;
        dividerColor: string;
    }, {
        Tooltip: import("../../_mixins").Theme<"Tooltip", {
            borderRadius: string;
            boxShadow: string;
            color: string;
            textColor: string;
            padding: string;
        }, {
            Popover: import("../../_mixins").Theme<"Popover", {
                fontSize: string;
                borderRadius: string;
                color: string;
                dividerColor: string;
                textColor: string;
                boxShadow: string;
                space: string;
                spaceArrow: string;
                arrowOffset: string;
                arrowOffsetVertical: string;
                arrowHeight: string;
                padding: string;
            }, any>;
        }>;
        Dropdown: import("../../_mixins").Theme<"Dropdown", {
            optionHeightSmall: string;
            optionHeightMedium: string;
            optionHeightLarge: string;
            optionHeightHuge: string;
            borderRadius: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            fontSizeHuge: string;
            optionTextColor: string;
            optionTextColorHover: string;
            optionTextColorActive: string;
            optionTextColorChildActive: string;
            color: string;
            dividerColor: string;
            suffixColor: string;
            prefixColor: string;
            optionColorHover: string;
            optionColorActive: string;
            groupHeaderTextColor: string;
            optionTextColorInverted: string;
            optionTextColorHoverInverted: string;
            optionTextColorActiveInverted: string;
            optionTextColorChildActiveInverted: string;
            colorInverted: string;
            dividerColorInverted: string;
            suffixColorInverted: string;
            prefixColorInverted: string;
            optionColorHoverInverted: string;
            optionColorActiveInverted: string;
            groupHeaderTextColorInverted: string;
            optionOpacityDisabled: string;
            padding: string;
            optionIconSizeSmall: string;
            optionIconSizeMedium: string;
            optionIconSizeLarge: string;
            optionIconSizeHuge: string;
            optionSuffixWidthSmall: string;
            optionSuffixWidthMedium: string;
            optionSuffixWidthLarge: string;
            optionSuffixWidthHuge: string;
            optionIconSuffixWidthSmall: string;
            optionIconSuffixWidthMedium: string;
            optionIconSuffixWidthLarge: string;
            optionIconSuffixWidthHuge: string;
            optionPrefixWidthSmall: string;
            optionPrefixWidthMedium: string;
            optionPrefixWidthLarge: string;
            optionPrefixWidthHuge: string;
            optionIconPrefixWidthSmall: string;
            optionIconPrefixWidthMedium: string;
            optionIconPrefixWidthLarge: string;
            optionIconPrefixWidthHuge: string;
        }, {
            Popover: import("../../_mixins").Theme<"Popover", {
                fontSize: string;
                borderRadius: string;
                color: string;
                dividerColor: string;
                textColor: string;
                boxShadow: string;
                space: string;
                spaceArrow: string;
                arrowOffset: string;
                arrowOffsetVertical: string;
                arrowHeight: string;
                padding: string;
            }, any>;
        }>;
    }>>>;
    readonly builtinThemeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Menu", {
        itemColorHoverInverted: string;
        itemColorActiveInverted: string;
        itemColorActiveHoverInverted: string;
        itemColorActiveCollapsedInverted: string;
        itemTextColorInverted: string;
        itemTextColorHoverInverted: string;
        itemTextColorChildActiveInverted: string;
        itemTextColorChildActiveHoverInverted: string;
        itemTextColorActiveInverted: string;
        itemTextColorActiveHoverInverted: string;
        itemTextColorHorizontalInverted: string;
        itemTextColorHoverHorizontalInverted: string;
        itemTextColorChildActiveHorizontalInverted: string;
        itemTextColorChildActiveHoverHorizontalInverted: string;
        itemTextColorActiveHorizontalInverted: string;
        itemTextColorActiveHoverHorizontalInverted: string;
        itemIconColorInverted: string;
        itemIconColorHoverInverted: string;
        itemIconColorActiveInverted: string;
        itemIconColorActiveHoverInverted: string;
        itemIconColorChildActiveInverted: string;
        itemIconColorChildActiveHoverInverted: string;
        itemIconColorCollapsedInverted: string;
        itemIconColorHorizontalInverted: string;
        itemIconColorHoverHorizontalInverted: string;
        itemIconColorActiveHorizontalInverted: string;
        itemIconColorActiveHoverHorizontalInverted: string;
        itemIconColorChildActiveHorizontalInverted: string;
        itemIconColorChildActiveHoverHorizontalInverted: string;
        arrowColorInverted: string;
        arrowColorHoverInverted: string;
        arrowColorActiveInverted: string;
        arrowColorActiveHoverInverted: string;
        arrowColorChildActiveInverted: string;
        arrowColorChildActiveHoverInverted: string;
        groupTextColorInverted: string;
        borderRadius: string;
        color: string;
        groupTextColor: string;
        itemColorHover: string;
        itemColorActive: string;
        itemColorActiveHover: string;
        itemColorActiveCollapsed: string;
        itemTextColor: string;
        itemTextColorHover: string;
        itemTextColorActive: string;
        itemTextColorActiveHover: string;
        itemTextColorChildActive: string;
        itemTextColorChildActiveHover: string;
        itemTextColorHorizontal: string;
        itemTextColorHoverHorizontal: string;
        itemTextColorActiveHorizontal: string;
        itemTextColorActiveHoverHorizontal: string;
        itemTextColorChildActiveHorizontal: string;
        itemTextColorChildActiveHoverHorizontal: string;
        itemIconColor: string;
        itemIconColorHover: string;
        itemIconColorActive: string;
        itemIconColorActiveHover: string;
        itemIconColorChildActive: string;
        itemIconColorChildActiveHover: string;
        itemIconColorCollapsed: string;
        itemIconColorHorizontal: string;
        itemIconColorHoverHorizontal: string;
        itemIconColorActiveHorizontal: string;
        itemIconColorActiveHoverHorizontal: string;
        itemIconColorChildActiveHorizontal: string;
        itemIconColorChildActiveHoverHorizontal: string;
        itemHeight: string;
        arrowColor: string;
        arrowColorHover: string;
        arrowColorActive: string;
        arrowColorActiveHover: string;
        arrowColorChildActive: string;
        arrowColorChildActiveHover: string;
        colorInverted: string;
        borderColorHorizontal: string;
        fontSize: string;
        dividerColor: string;
    }, {
        Tooltip: import("../../_mixins").Theme<"Tooltip", {
            borderRadius: string;
            boxShadow: string;
            color: string;
            textColor: string;
            padding: string;
        }, {
            Popover: import("../../_mixins").Theme<"Popover", {
                fontSize: string;
                borderRadius: string;
                color: string;
                dividerColor: string;
                textColor: string;
                boxShadow: string;
                space: string;
                spaceArrow: string;
                arrowOffset: string;
                arrowOffsetVertical: string;
                arrowHeight: string;
                padding: string;
            }, any>;
        }>;
        Dropdown: import("../../_mixins").Theme<"Dropdown", {
            optionHeightSmall: string;
            optionHeightMedium: string;
            optionHeightLarge: string;
            optionHeightHuge: string;
            borderRadius: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            fontSizeHuge: string;
            optionTextColor: string;
            optionTextColorHover: string;
            optionTextColorActive: string;
            optionTextColorChildActive: string;
            color: string;
            dividerColor: string;
            suffixColor: string;
            prefixColor: string;
            optionColorHover: string;
            optionColorActive: string;
            groupHeaderTextColor: string;
            optionTextColorInverted: string;
            optionTextColorHoverInverted: string;
            optionTextColorActiveInverted: string;
            optionTextColorChildActiveInverted: string;
            colorInverted: string;
            dividerColorInverted: string;
            suffixColorInverted: string;
            prefixColorInverted: string;
            optionColorHoverInverted: string;
            optionColorActiveInverted: string;
            groupHeaderTextColorInverted: string;
            optionOpacityDisabled: string;
            padding: string;
            optionIconSizeSmall: string;
            optionIconSizeMedium: string;
            optionIconSizeLarge: string;
            optionIconSizeHuge: string;
            optionSuffixWidthSmall: string;
            optionSuffixWidthMedium: string;
            optionSuffixWidthLarge: string;
            optionSuffixWidthHuge: string;
            optionIconSuffixWidthSmall: string;
            optionIconSuffixWidthMedium: string;
            optionIconSuffixWidthLarge: string;
            optionIconSuffixWidthHuge: string;
            optionPrefixWidthSmall: string;
            optionPrefixWidthMedium: string;
            optionPrefixWidthLarge: string;
            optionPrefixWidthHuge: string;
            optionIconPrefixWidthSmall: string;
            optionIconPrefixWidthMedium: string;
            optionIconPrefixWidthLarge: string;
            optionIconPrefixWidthHuge: string;
        }, {
            Popover: import("../../_mixins").Theme<"Popover", {
                fontSize: string;
                borderRadius: string;
                color: string;
                dividerColor: string;
                textColor: string;
                boxShadow: string;
                space: string;
                spaceArrow: string;
                arrowOffset: string;
                arrowOffsetVertical: string;
                arrowHeight: string;
                padding: string;
            }, any>;
        }>;
    }>>>;
}>, {
    mergedClsPrefix: import("vue").Ref<string, string>;
    controlledExpandedKeys: import("vue").ComputedRef<Key[] | undefined>;
    uncontrolledExpanededKeys: import("vue").Ref<Key[], Key[]>;
    mergedExpandedKeys: import("vue").ComputedRef<Key[]>;
    uncontrolledValue: import("vue").Ref<Key | null, Key | null>;
    mergedValue: import("vue").ComputedRef<Key | null>;
    activePath: import("vue").ComputedRef<Key[]>;
    tmNodes: import("vue").ComputedRef<import("treemate").TreeNode<MenuOption, MenuGroupOption, MenuIgnoredOption>[]>;
    mergedTheme: import("vue").ComputedRef<{
        common: import("../..").ThemeCommonVars;
        self: {
            itemColorHoverInverted: string;
            itemColorActiveInverted: string;
            itemColorActiveHoverInverted: string;
            itemColorActiveCollapsedInverted: string;
            itemTextColorInverted: string;
            itemTextColorHoverInverted: string;
            itemTextColorChildActiveInverted: string;
            itemTextColorChildActiveHoverInverted: string;
            itemTextColorActiveInverted: string;
            itemTextColorActiveHoverInverted: string;
            itemTextColorHorizontalInverted: string;
            itemTextColorHoverHorizontalInverted: string;
            itemTextColorChildActiveHorizontalInverted: string;
            itemTextColorChildActiveHoverHorizontalInverted: string;
            itemTextColorActiveHorizontalInverted: string;
            itemTextColorActiveHoverHorizontalInverted: string;
            itemIconColorInverted: string;
            itemIconColorHoverInverted: string;
            itemIconColorActiveInverted: string;
            itemIconColorActiveHoverInverted: string;
            itemIconColorChildActiveInverted: string;
            itemIconColorChildActiveHoverInverted: string;
            itemIconColorCollapsedInverted: string;
            itemIconColorHorizontalInverted: string;
            itemIconColorHoverHorizontalInverted: string;
            itemIconColorActiveHorizontalInverted: string;
            itemIconColorActiveHoverHorizontalInverted: string;
            itemIconColorChildActiveHorizontalInverted: string;
            itemIconColorChildActiveHoverHorizontalInverted: string;
            arrowColorInverted: string;
            arrowColorHoverInverted: string;
            arrowColorActiveInverted: string;
            arrowColorActiveHoverInverted: string;
            arrowColorChildActiveInverted: string;
            arrowColorChildActiveHoverInverted: string;
            groupTextColorInverted: string;
            borderRadius: string;
            color: string;
            groupTextColor: string;
            itemColorHover: string;
            itemColorActive: string;
            itemColorActiveHover: string;
            itemColorActiveCollapsed: string;
            itemTextColor: string;
            itemTextColorHover: string;
            itemTextColorActive: string;
            itemTextColorActiveHover: string;
            itemTextColorChildActive: string;
            itemTextColorChildActiveHover: string;
            itemTextColorHorizontal: string;
            itemTextColorHoverHorizontal: string;
            itemTextColorActiveHorizontal: string;
            itemTextColorActiveHoverHorizontal: string;
            itemTextColorChildActiveHorizontal: string;
            itemTextColorChildActiveHoverHorizontal: string;
            itemIconColor: string;
            itemIconColorHover: string;
            itemIconColorActive: string;
            itemIconColorActiveHover: string;
            itemIconColorChildActive: string;
            itemIconColorChildActiveHover: string;
            itemIconColorCollapsed: string;
            itemIconColorHorizontal: string;
            itemIconColorHoverHorizontal: string;
            itemIconColorActiveHorizontal: string;
            itemIconColorActiveHoverHorizontal: string;
            itemIconColorChildActiveHorizontal: string;
            itemIconColorChildActiveHoverHorizontal: string;
            itemHeight: string;
            arrowColor: string;
            arrowColorHover: string;
            arrowColorActive: string;
            arrowColorActiveHover: string;
            arrowColorChildActive: string;
            arrowColorChildActiveHover: string;
            colorInverted: string;
            borderColorHorizontal: string;
            fontSize: string;
            dividerColor: string;
        };
        peers: {
            Tooltip: import("../../_mixins").Theme<"Tooltip", {
                borderRadius: string;
                boxShadow: string;
                color: string;
                textColor: string;
                padding: string;
            }, {
                Popover: import("../../_mixins").Theme<"Popover", {
                    fontSize: string;
                    borderRadius: string;
                    color: string;
                    dividerColor: string;
                    textColor: string;
                    boxShadow: string;
                    space: string;
                    spaceArrow: string;
                    arrowOffset: string;
                    arrowOffsetVertical: string;
                    arrowHeight: string;
                    padding: string;
                }, any>;
            }>;
            Dropdown: import("../../_mixins").Theme<"Dropdown", {
                optionHeightSmall: string;
                optionHeightMedium: string;
                optionHeightLarge: string;
                optionHeightHuge: string;
                borderRadius: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                fontSizeHuge: string;
                optionTextColor: string;
                optionTextColorHover: string;
                optionTextColorActive: string;
                optionTextColorChildActive: string;
                color: string;
                dividerColor: string;
                suffixColor: string;
                prefixColor: string;
                optionColorHover: string;
                optionColorActive: string;
                groupHeaderTextColor: string;
                optionTextColorInverted: string;
                optionTextColorHoverInverted: string;
                optionTextColorActiveInverted: string;
                optionTextColorChildActiveInverted: string;
                colorInverted: string;
                dividerColorInverted: string;
                suffixColorInverted: string;
                prefixColorInverted: string;
                optionColorHoverInverted: string;
                optionColorActiveInverted: string;
                groupHeaderTextColorInverted: string;
                optionOpacityDisabled: string;
                padding: string;
                optionIconSizeSmall: string;
                optionIconSizeMedium: string;
                optionIconSizeLarge: string;
                optionIconSizeHuge: string;
                optionSuffixWidthSmall: string;
                optionSuffixWidthMedium: string;
                optionSuffixWidthLarge: string;
                optionSuffixWidthHuge: string;
                optionIconSuffixWidthSmall: string;
                optionIconSuffixWidthMedium: string;
                optionIconSuffixWidthLarge: string;
                optionIconSuffixWidthHuge: string;
                optionPrefixWidthSmall: string;
                optionPrefixWidthMedium: string;
                optionPrefixWidthLarge: string;
                optionPrefixWidthHuge: string;
                optionIconPrefixWidthSmall: string;
                optionIconPrefixWidthMedium: string;
                optionIconPrefixWidthLarge: string;
                optionIconPrefixWidthHuge: string;
            }, {
                Popover: import("../../_mixins").Theme<"Popover", {
                    fontSize: string;
                    borderRadius: string;
                    color: string;
                    dividerColor: string;
                    textColor: string;
                    boxShadow: string;
                    space: string;
                    spaceArrow: string;
                    arrowOffset: string;
                    arrowOffsetVertical: string;
                    arrowHeight: string;
                    padding: string;
                }, any>;
            }>;
        };
        peerOverrides: {
            Tooltip?: {
                peers?: {
                    Popover?: import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Popover", {
                        fontSize: string;
                        borderRadius: string;
                        color: string;
                        dividerColor: string;
                        textColor: string;
                        boxShadow: string;
                        space: string;
                        spaceArrow: string;
                        arrowOffset: string;
                        arrowOffsetVertical: string;
                        arrowHeight: string;
                        padding: string;
                    }, any>> | undefined;
                } | undefined;
            } | undefined;
            Dropdown?: {
                peers?: {
                    Popover?: import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Popover", {
                        fontSize: string;
                        borderRadius: string;
                        color: string;
                        dividerColor: string;
                        textColor: string;
                        boxShadow: string;
                        space: string;
                        spaceArrow: string;
                        arrowOffset: string;
                        arrowOffsetVertical: string;
                        arrowHeight: string;
                        padding: string;
                    }, any>> | undefined;
                } | undefined;
            } | undefined;
        };
    }>;
    mergedCollapsed: import("vue").ComputedRef<boolean>;
    cssVars: import("vue").ComputedRef<any> | undefined;
    themeClass: import("vue").Ref<string, string> | undefined;
    overflowRef: import("vue").Ref<{
        sync: (options: {
            showAllItemsBeforeCalculate: boolean;
        }) => void;
    } | null, VOverflowInst | {
        sync: (options: {
            showAllItemsBeforeCalculate: boolean;
        }) => void;
    } | null>;
    counterRef: import("vue").Ref<HTMLElement | null, HTMLElement | null>;
    updateCounter: () => void;
    onResize: () => void;
    onUpdateOverflow: (overflow: boolean) => void;
    onUpdateCount: (count: number) => void;
    renderCounter: () => VNodeChild;
    getCounter: () => HTMLElement | null;
    onRender: (() => void) | undefined;
    showOption: (key?: import("./interface").Key) => void;
    deriveResponsiveState: () => void;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<ExtractPropTypes<{
    readonly options: {
        readonly type: PropType<MenuMixedOption[]>;
        readonly default: () => never[];
    };
    readonly collapsed: {
        readonly type: PropType<boolean | undefined>;
        readonly default: undefined;
    };
    readonly collapsedWidth: {
        readonly type: NumberConstructor;
        readonly default: 48;
    };
    readonly iconSize: {
        readonly type: NumberConstructor;
        readonly default: 20;
    };
    readonly collapsedIconSize: {
        readonly type: NumberConstructor;
        readonly default: 24;
    };
    readonly rootIndent: NumberConstructor;
    readonly indent: {
        readonly type: NumberConstructor;
        readonly default: 32;
    };
    readonly labelField: {
        readonly type: StringConstructor;
        readonly default: "label";
    };
    readonly keyField: {
        readonly type: StringConstructor;
        readonly default: "key";
    };
    readonly childrenField: {
        readonly type: StringConstructor;
        readonly default: "children";
    };
    readonly disabledField: {
        readonly type: StringConstructor;
        readonly default: "disabled";
    };
    readonly defaultExpandAll: BooleanConstructor;
    readonly defaultExpandedKeys: PropType<Key[]>;
    readonly expandedKeys: PropType<Key[]>;
    readonly value: PropType<Key | null>;
    readonly defaultValue: {
        readonly type: PropType<Key | null>;
        readonly default: null;
    };
    readonly mode: {
        readonly type: PropType<"vertical" | "horizontal">;
        readonly default: "vertical";
    };
    readonly watchProps: {
        readonly type: PropType<Array<"defaultExpandedKeys" | "defaultValue">>;
        readonly default: undefined;
    };
    readonly disabled: BooleanConstructor;
    readonly show: {
        readonly type: BooleanConstructor;
        readonly default: true;
    };
    readonly inverted: BooleanConstructor;
    readonly 'onUpdate:expandedKeys': PropType<MaybeArray<OnUpdateKeys>>;
    readonly onUpdateExpandedKeys: PropType<MaybeArray<OnUpdateKeys>>;
    readonly onUpdateValue: PropType<MaybeArray<OnUpdateValue>>;
    readonly 'onUpdate:value': PropType<MaybeArray<OnUpdateValue>>;
    readonly expandIcon: PropType<(option: MenuOption) => VNodeChild>;
    readonly renderIcon: PropType<(option: MenuOption) => VNodeChild>;
    readonly renderLabel: PropType<(option: MenuOption | MenuGroupOption) => VNodeChild>;
    readonly renderExtra: PropType<(option: MenuOption | MenuGroupOption) => VNodeChild>;
    readonly dropdownProps: PropType<DropdownProps>;
    readonly accordion: BooleanConstructor;
    readonly nodeProps: PropType<MenuNodeProps>;
    readonly dropdownPlacement: {
        readonly type: PropType<FollowerPlacement>;
        readonly default: "bottom";
    };
    readonly responsive: BooleanConstructor;
    readonly items: PropType<Array<MenuOption | MenuGroupOption>>;
    readonly onOpenNamesChange: PropType<MaybeArray<OnUpdateKeys>>;
    readonly onSelect: PropType<MaybeArray<OnUpdateValue>>;
    readonly onExpandedNamesChange: PropType<MaybeArray<OnUpdateKeys>>;
    readonly expandedNames: PropType<Key[]>;
    readonly defaultExpandedNames: PropType<Key[]>;
    readonly theme: PropType<import("../../_mixins").Theme<"Menu", {
        itemColorHoverInverted: string;
        itemColorActiveInverted: string;
        itemColorActiveHoverInverted: string;
        itemColorActiveCollapsedInverted: string;
        itemTextColorInverted: string;
        itemTextColorHoverInverted: string;
        itemTextColorChildActiveInverted: string;
        itemTextColorChildActiveHoverInverted: string;
        itemTextColorActiveInverted: string;
        itemTextColorActiveHoverInverted: string;
        itemTextColorHorizontalInverted: string;
        itemTextColorHoverHorizontalInverted: string;
        itemTextColorChildActiveHorizontalInverted: string;
        itemTextColorChildActiveHoverHorizontalInverted: string;
        itemTextColorActiveHorizontalInverted: string;
        itemTextColorActiveHoverHorizontalInverted: string;
        itemIconColorInverted: string;
        itemIconColorHoverInverted: string;
        itemIconColorActiveInverted: string;
        itemIconColorActiveHoverInverted: string;
        itemIconColorChildActiveInverted: string;
        itemIconColorChildActiveHoverInverted: string;
        itemIconColorCollapsedInverted: string;
        itemIconColorHorizontalInverted: string;
        itemIconColorHoverHorizontalInverted: string;
        itemIconColorActiveHorizontalInverted: string;
        itemIconColorActiveHoverHorizontalInverted: string;
        itemIconColorChildActiveHorizontalInverted: string;
        itemIconColorChildActiveHoverHorizontalInverted: string;
        arrowColorInverted: string;
        arrowColorHoverInverted: string;
        arrowColorActiveInverted: string;
        arrowColorActiveHoverInverted: string;
        arrowColorChildActiveInverted: string;
        arrowColorChildActiveHoverInverted: string;
        groupTextColorInverted: string;
        borderRadius: string;
        color: string;
        groupTextColor: string;
        itemColorHover: string;
        itemColorActive: string;
        itemColorActiveHover: string;
        itemColorActiveCollapsed: string;
        itemTextColor: string;
        itemTextColorHover: string;
        itemTextColorActive: string;
        itemTextColorActiveHover: string;
        itemTextColorChildActive: string;
        itemTextColorChildActiveHover: string;
        itemTextColorHorizontal: string;
        itemTextColorHoverHorizontal: string;
        itemTextColorActiveHorizontal: string;
        itemTextColorActiveHoverHorizontal: string;
        itemTextColorChildActiveHorizontal: string;
        itemTextColorChildActiveHoverHorizontal: string;
        itemIconColor: string;
        itemIconColorHover: string;
        itemIconColorActive: string;
        itemIconColorActiveHover: string;
        itemIconColorChildActive: string;
        itemIconColorChildActiveHover: string;
        itemIconColorCollapsed: string;
        itemIconColorHorizontal: string;
        itemIconColorHoverHorizontal: string;
        itemIconColorActiveHorizontal: string;
        itemIconColorActiveHoverHorizontal: string;
        itemIconColorChildActiveHorizontal: string;
        itemIconColorChildActiveHoverHorizontal: string;
        itemHeight: string;
        arrowColor: string;
        arrowColorHover: string;
        arrowColorActive: string;
        arrowColorActiveHover: string;
        arrowColorChildActive: string;
        arrowColorChildActiveHover: string;
        colorInverted: string;
        borderColorHorizontal: string;
        fontSize: string;
        dividerColor: string;
    }, {
        Tooltip: import("../../_mixins").Theme<"Tooltip", {
            borderRadius: string;
            boxShadow: string;
            color: string;
            textColor: string;
            padding: string;
        }, {
            Popover: import("../../_mixins").Theme<"Popover", {
                fontSize: string;
                borderRadius: string;
                color: string;
                dividerColor: string;
                textColor: string;
                boxShadow: string;
                space: string;
                spaceArrow: string;
                arrowOffset: string;
                arrowOffsetVertical: string;
                arrowHeight: string;
                padding: string;
            }, any>;
        }>;
        Dropdown: import("../../_mixins").Theme<"Dropdown", {
            optionHeightSmall: string;
            optionHeightMedium: string;
            optionHeightLarge: string;
            optionHeightHuge: string;
            borderRadius: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            fontSizeHuge: string;
            optionTextColor: string;
            optionTextColorHover: string;
            optionTextColorActive: string;
            optionTextColorChildActive: string;
            color: string;
            dividerColor: string;
            suffixColor: string;
            prefixColor: string;
            optionColorHover: string;
            optionColorActive: string;
            groupHeaderTextColor: string;
            optionTextColorInverted: string;
            optionTextColorHoverInverted: string;
            optionTextColorActiveInverted: string;
            optionTextColorChildActiveInverted: string;
            colorInverted: string;
            dividerColorInverted: string;
            suffixColorInverted: string;
            prefixColorInverted: string;
            optionColorHoverInverted: string;
            optionColorActiveInverted: string;
            groupHeaderTextColorInverted: string;
            optionOpacityDisabled: string;
            padding: string;
            optionIconSizeSmall: string;
            optionIconSizeMedium: string;
            optionIconSizeLarge: string;
            optionIconSizeHuge: string;
            optionSuffixWidthSmall: string;
            optionSuffixWidthMedium: string;
            optionSuffixWidthLarge: string;
            optionSuffixWidthHuge: string;
            optionIconSuffixWidthSmall: string;
            optionIconSuffixWidthMedium: string;
            optionIconSuffixWidthLarge: string;
            optionIconSuffixWidthHuge: string;
            optionPrefixWidthSmall: string;
            optionPrefixWidthMedium: string;
            optionPrefixWidthLarge: string;
            optionPrefixWidthHuge: string;
            optionIconPrefixWidthSmall: string;
            optionIconPrefixWidthMedium: string;
            optionIconPrefixWidthLarge: string;
            optionIconPrefixWidthHuge: string;
        }, {
            Popover: import("../../_mixins").Theme<"Popover", {
                fontSize: string;
                borderRadius: string;
                color: string;
                dividerColor: string;
                textColor: string;
                boxShadow: string;
                space: string;
                spaceArrow: string;
                arrowOffset: string;
                arrowOffsetVertical: string;
                arrowHeight: string;
                padding: string;
            }, any>;
        }>;
    }>>;
    readonly themeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Menu", {
        itemColorHoverInverted: string;
        itemColorActiveInverted: string;
        itemColorActiveHoverInverted: string;
        itemColorActiveCollapsedInverted: string;
        itemTextColorInverted: string;
        itemTextColorHoverInverted: string;
        itemTextColorChildActiveInverted: string;
        itemTextColorChildActiveHoverInverted: string;
        itemTextColorActiveInverted: string;
        itemTextColorActiveHoverInverted: string;
        itemTextColorHorizontalInverted: string;
        itemTextColorHoverHorizontalInverted: string;
        itemTextColorChildActiveHorizontalInverted: string;
        itemTextColorChildActiveHoverHorizontalInverted: string;
        itemTextColorActiveHorizontalInverted: string;
        itemTextColorActiveHoverHorizontalInverted: string;
        itemIconColorInverted: string;
        itemIconColorHoverInverted: string;
        itemIconColorActiveInverted: string;
        itemIconColorActiveHoverInverted: string;
        itemIconColorChildActiveInverted: string;
        itemIconColorChildActiveHoverInverted: string;
        itemIconColorCollapsedInverted: string;
        itemIconColorHorizontalInverted: string;
        itemIconColorHoverHorizontalInverted: string;
        itemIconColorActiveHorizontalInverted: string;
        itemIconColorActiveHoverHorizontalInverted: string;
        itemIconColorChildActiveHorizontalInverted: string;
        itemIconColorChildActiveHoverHorizontalInverted: string;
        arrowColorInverted: string;
        arrowColorHoverInverted: string;
        arrowColorActiveInverted: string;
        arrowColorActiveHoverInverted: string;
        arrowColorChildActiveInverted: string;
        arrowColorChildActiveHoverInverted: string;
        groupTextColorInverted: string;
        borderRadius: string;
        color: string;
        groupTextColor: string;
        itemColorHover: string;
        itemColorActive: string;
        itemColorActiveHover: string;
        itemColorActiveCollapsed: string;
        itemTextColor: string;
        itemTextColorHover: string;
        itemTextColorActive: string;
        itemTextColorActiveHover: string;
        itemTextColorChildActive: string;
        itemTextColorChildActiveHover: string;
        itemTextColorHorizontal: string;
        itemTextColorHoverHorizontal: string;
        itemTextColorActiveHorizontal: string;
        itemTextColorActiveHoverHorizontal: string;
        itemTextColorChildActiveHorizontal: string;
        itemTextColorChildActiveHoverHorizontal: string;
        itemIconColor: string;
        itemIconColorHover: string;
        itemIconColorActive: string;
        itemIconColorActiveHover: string;
        itemIconColorChildActive: string;
        itemIconColorChildActiveHover: string;
        itemIconColorCollapsed: string;
        itemIconColorHorizontal: string;
        itemIconColorHoverHorizontal: string;
        itemIconColorActiveHorizontal: string;
        itemIconColorActiveHoverHorizontal: string;
        itemIconColorChildActiveHorizontal: string;
        itemIconColorChildActiveHoverHorizontal: string;
        itemHeight: string;
        arrowColor: string;
        arrowColorHover: string;
        arrowColorActive: string;
        arrowColorActiveHover: string;
        arrowColorChildActive: string;
        arrowColorChildActiveHover: string;
        colorInverted: string;
        borderColorHorizontal: string;
        fontSize: string;
        dividerColor: string;
    }, {
        Tooltip: import("../../_mixins").Theme<"Tooltip", {
            borderRadius: string;
            boxShadow: string;
            color: string;
            textColor: string;
            padding: string;
        }, {
            Popover: import("../../_mixins").Theme<"Popover", {
                fontSize: string;
                borderRadius: string;
                color: string;
                dividerColor: string;
                textColor: string;
                boxShadow: string;
                space: string;
                spaceArrow: string;
                arrowOffset: string;
                arrowOffsetVertical: string;
                arrowHeight: string;
                padding: string;
            }, any>;
        }>;
        Dropdown: import("../../_mixins").Theme<"Dropdown", {
            optionHeightSmall: string;
            optionHeightMedium: string;
            optionHeightLarge: string;
            optionHeightHuge: string;
            borderRadius: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            fontSizeHuge: string;
            optionTextColor: string;
            optionTextColorHover: string;
            optionTextColorActive: string;
            optionTextColorChildActive: string;
            color: string;
            dividerColor: string;
            suffixColor: string;
            prefixColor: string;
            optionColorHover: string;
            optionColorActive: string;
            groupHeaderTextColor: string;
            optionTextColorInverted: string;
            optionTextColorHoverInverted: string;
            optionTextColorActiveInverted: string;
            optionTextColorChildActiveInverted: string;
            colorInverted: string;
            dividerColorInverted: string;
            suffixColorInverted: string;
            prefixColorInverted: string;
            optionColorHoverInverted: string;
            optionColorActiveInverted: string;
            groupHeaderTextColorInverted: string;
            optionOpacityDisabled: string;
            padding: string;
            optionIconSizeSmall: string;
            optionIconSizeMedium: string;
            optionIconSizeLarge: string;
            optionIconSizeHuge: string;
            optionSuffixWidthSmall: string;
            optionSuffixWidthMedium: string;
            optionSuffixWidthLarge: string;
            optionSuffixWidthHuge: string;
            optionIconSuffixWidthSmall: string;
            optionIconSuffixWidthMedium: string;
            optionIconSuffixWidthLarge: string;
            optionIconSuffixWidthHuge: string;
            optionPrefixWidthSmall: string;
            optionPrefixWidthMedium: string;
            optionPrefixWidthLarge: string;
            optionPrefixWidthHuge: string;
            optionIconPrefixWidthSmall: string;
            optionIconPrefixWidthMedium: string;
            optionIconPrefixWidthLarge: string;
            optionIconPrefixWidthHuge: string;
        }, {
            Popover: import("../../_mixins").Theme<"Popover", {
                fontSize: string;
                borderRadius: string;
                color: string;
                dividerColor: string;
                textColor: string;
                boxShadow: string;
                space: string;
                spaceArrow: string;
                arrowOffset: string;
                arrowOffsetVertical: string;
                arrowHeight: string;
                padding: string;
            }, any>;
        }>;
    }>>>;
    readonly builtinThemeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Menu", {
        itemColorHoverInverted: string;
        itemColorActiveInverted: string;
        itemColorActiveHoverInverted: string;
        itemColorActiveCollapsedInverted: string;
        itemTextColorInverted: string;
        itemTextColorHoverInverted: string;
        itemTextColorChildActiveInverted: string;
        itemTextColorChildActiveHoverInverted: string;
        itemTextColorActiveInverted: string;
        itemTextColorActiveHoverInverted: string;
        itemTextColorHorizontalInverted: string;
        itemTextColorHoverHorizontalInverted: string;
        itemTextColorChildActiveHorizontalInverted: string;
        itemTextColorChildActiveHoverHorizontalInverted: string;
        itemTextColorActiveHorizontalInverted: string;
        itemTextColorActiveHoverHorizontalInverted: string;
        itemIconColorInverted: string;
        itemIconColorHoverInverted: string;
        itemIconColorActiveInverted: string;
        itemIconColorActiveHoverInverted: string;
        itemIconColorChildActiveInverted: string;
        itemIconColorChildActiveHoverInverted: string;
        itemIconColorCollapsedInverted: string;
        itemIconColorHorizontalInverted: string;
        itemIconColorHoverHorizontalInverted: string;
        itemIconColorActiveHorizontalInverted: string;
        itemIconColorActiveHoverHorizontalInverted: string;
        itemIconColorChildActiveHorizontalInverted: string;
        itemIconColorChildActiveHoverHorizontalInverted: string;
        arrowColorInverted: string;
        arrowColorHoverInverted: string;
        arrowColorActiveInverted: string;
        arrowColorActiveHoverInverted: string;
        arrowColorChildActiveInverted: string;
        arrowColorChildActiveHoverInverted: string;
        groupTextColorInverted: string;
        borderRadius: string;
        color: string;
        groupTextColor: string;
        itemColorHover: string;
        itemColorActive: string;
        itemColorActiveHover: string;
        itemColorActiveCollapsed: string;
        itemTextColor: string;
        itemTextColorHover: string;
        itemTextColorActive: string;
        itemTextColorActiveHover: string;
        itemTextColorChildActive: string;
        itemTextColorChildActiveHover: string;
        itemTextColorHorizontal: string;
        itemTextColorHoverHorizontal: string;
        itemTextColorActiveHorizontal: string;
        itemTextColorActiveHoverHorizontal: string;
        itemTextColorChildActiveHorizontal: string;
        itemTextColorChildActiveHoverHorizontal: string;
        itemIconColor: string;
        itemIconColorHover: string;
        itemIconColorActive: string;
        itemIconColorActiveHover: string;
        itemIconColorChildActive: string;
        itemIconColorChildActiveHover: string;
        itemIconColorCollapsed: string;
        itemIconColorHorizontal: string;
        itemIconColorHoverHorizontal: string;
        itemIconColorActiveHorizontal: string;
        itemIconColorActiveHoverHorizontal: string;
        itemIconColorChildActiveHorizontal: string;
        itemIconColorChildActiveHoverHorizontal: string;
        itemHeight: string;
        arrowColor: string;
        arrowColorHover: string;
        arrowColorActive: string;
        arrowColorActiveHover: string;
        arrowColorChildActive: string;
        arrowColorChildActiveHover: string;
        colorInverted: string;
        borderColorHorizontal: string;
        fontSize: string;
        dividerColor: string;
    }, {
        Tooltip: import("../../_mixins").Theme<"Tooltip", {
            borderRadius: string;
            boxShadow: string;
            color: string;
            textColor: string;
            padding: string;
        }, {
            Popover: import("../../_mixins").Theme<"Popover", {
                fontSize: string;
                borderRadius: string;
                color: string;
                dividerColor: string;
                textColor: string;
                boxShadow: string;
                space: string;
                spaceArrow: string;
                arrowOffset: string;
                arrowOffsetVertical: string;
                arrowHeight: string;
                padding: string;
            }, any>;
        }>;
        Dropdown: import("../../_mixins").Theme<"Dropdown", {
            optionHeightSmall: string;
            optionHeightMedium: string;
            optionHeightLarge: string;
            optionHeightHuge: string;
            borderRadius: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            fontSizeHuge: string;
            optionTextColor: string;
            optionTextColorHover: string;
            optionTextColorActive: string;
            optionTextColorChildActive: string;
            color: string;
            dividerColor: string;
            suffixColor: string;
            prefixColor: string;
            optionColorHover: string;
            optionColorActive: string;
            groupHeaderTextColor: string;
            optionTextColorInverted: string;
            optionTextColorHoverInverted: string;
            optionTextColorActiveInverted: string;
            optionTextColorChildActiveInverted: string;
            colorInverted: string;
            dividerColorInverted: string;
            suffixColorInverted: string;
            prefixColorInverted: string;
            optionColorHoverInverted: string;
            optionColorActiveInverted: string;
            groupHeaderTextColorInverted: string;
            optionOpacityDisabled: string;
            padding: string;
            optionIconSizeSmall: string;
            optionIconSizeMedium: string;
            optionIconSizeLarge: string;
            optionIconSizeHuge: string;
            optionSuffixWidthSmall: string;
            optionSuffixWidthMedium: string;
            optionSuffixWidthLarge: string;
            optionSuffixWidthHuge: string;
            optionIconSuffixWidthSmall: string;
            optionIconSuffixWidthMedium: string;
            optionIconSuffixWidthLarge: string;
            optionIconSuffixWidthHuge: string;
            optionPrefixWidthSmall: string;
            optionPrefixWidthMedium: string;
            optionPrefixWidthLarge: string;
            optionPrefixWidthHuge: string;
            optionIconPrefixWidthSmall: string;
            optionIconPrefixWidthMedium: string;
            optionIconPrefixWidthLarge: string;
            optionIconPrefixWidthHuge: string;
        }, {
            Popover: import("../../_mixins").Theme<"Popover", {
                fontSize: string;
                borderRadius: string;
                color: string;
                dividerColor: string;
                textColor: string;
                boxShadow: string;
                space: string;
                spaceArrow: string;
                arrowOffset: string;
                arrowOffsetVertical: string;
                arrowHeight: string;
                padding: string;
            }, any>;
        }>;
    }>>>;
}>> & Readonly<{}>, {
    readonly options: MenuMixedOption[];
    readonly mode: "horizontal" | "vertical";
    readonly disabled: boolean;
    readonly show: boolean;
    readonly iconSize: number;
    readonly keyField: string;
    readonly labelField: string;
    readonly responsive: boolean;
    readonly defaultValue: Key | null;
    readonly childrenField: string;
    readonly defaultExpandAll: boolean;
    readonly indent: number;
    readonly inverted: boolean;
    readonly disabledField: string;
    readonly accordion: boolean;
    readonly collapsed: boolean | undefined;
    readonly collapsedWidth: number;
    readonly collapsedIconSize: number;
    readonly watchProps: ("defaultValue" | "defaultExpandedKeys")[];
    readonly dropdownPlacement: FollowerPlacement;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;

import { type PropType } from 'vue';
declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    displayDirective: {
        type: PropType<"if" | "show">;
        required: true;
    };
    show: BooleanConstructor;
    clsPrefix: {
        type: StringConstructor;
        required: true;
    };
}>, {
    onceTrue: Readonly<import("vue").Ref<boolean, boolean>>;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    displayDirective: {
        type: PropType<"if" | "show">;
        required: true;
    };
    show: BooleanConstructor;
    clsPrefix: {
        type: StringConstructor;
        required: true;
    };
}>> & Readonly<{}>, {
    show: boolean;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;

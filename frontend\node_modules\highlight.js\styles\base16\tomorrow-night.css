pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Tomorrow Night
  Author: <PERSON> (http://chriskempson.com)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme tomorrow-night
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #2d2d2d  Default Background
base01  #393939  Lighter Background (Used for status bars, line number and folding marks)
base02  #515151  Selection Background
base03  #999999  Comments, Invisibles, Line Highlighting
base04  #b4b7b4  Dark Foreground (Used for status bars)
base05  #cccccc  Default Foreground, Caret, Delimiters, Operators
base06  #e0e0e0  Light Foreground (Not often used)
base07  #ffffff  Light Background (Not often used)
base08  #f2777a  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #f99157  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #ffcc66  Classes, Markup Bold, Search Text Background
base0B  #99cc99  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #66cccc  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #6699cc  Functions, Methods, Attribute IDs, Headings
base0E  #cc99cc  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #a3685a  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #cccccc;
  background: #2d2d2d
}
.hljs::selection,
.hljs ::selection {
  background-color: #515151;
  color: #cccccc
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #999999 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #999999
}
/* base04 - #b4b7b4 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #b4b7b4
}
/* base05 - #cccccc -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #cccccc
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #f2777a
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #f99157
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #ffcc66
}
.hljs-strong {
  font-weight: bold;
  color: #ffcc66
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #99cc99
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #66cccc
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #6699cc
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #cc99cc
}
.hljs-emphasis {
  color: #cc99cc;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #a3685a
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}
import type { FollowerPlacement } from 'vueuc';
import type { DropdownMenuProps, DropdownMixedOption, NodeProps, OnUpdateValue, OnUpdateValueImpl, RenderIcon, RenderIconImpl, RenderLabel, RenderLabelImpl, RenderOption, RenderOptionImpl } from './interface';
import { type Key, type TreeNode } from 'treemate';
import { type PropType, type Ref } from 'vue';
import { type ExtractPublicPropTypes, type MaybeArray } from '../../_utils';
export interface DropdownInjection {
    renderLabelRef: Ref<RenderLabelImpl | undefined>;
    renderIconRef: Ref<RenderIconImpl | undefined>;
    renderOptionRef: Ref<RenderOptionImpl | undefined>;
    menuPropsRef: Ref<DropdownMenuProps | undefined>;
    nodePropsRef: Ref<NodeProps | undefined>;
    hoverKeyRef: Ref<Key | null>;
    keyboardKeyRef: Ref<Key | null>;
    lastToggledSubmenuKeyRef: Ref<Key | null>;
    pendingKeyPathRef: Ref<Key[]>;
    activeKeyPathRef: Ref<Key[]>;
    animatedRef: Ref<boolean>;
    mergedShowRef: Ref<boolean>;
    labelFieldRef: Ref<string>;
    childrenFieldRef: Ref<string>;
    doSelect: OnUpdateValueImpl;
    doUpdateShow: (value: boolean) => void;
}
export declare const dropdownProps: {
    readonly theme: PropType<import("../../_mixins").Theme<"Dropdown", {
        optionHeightSmall: string;
        optionHeightMedium: string;
        optionHeightLarge: string;
        optionHeightHuge: string;
        borderRadius: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        fontSizeHuge: string;
        optionTextColor: string;
        optionTextColorHover: string;
        optionTextColorActive: string;
        optionTextColorChildActive: string;
        color: string;
        dividerColor: string;
        suffixColor: string;
        prefixColor: string;
        optionColorHover: string;
        optionColorActive: string;
        groupHeaderTextColor: string;
        optionTextColorInverted: string;
        optionTextColorHoverInverted: string;
        optionTextColorActiveInverted: string;
        optionTextColorChildActiveInverted: string;
        colorInverted: string;
        dividerColorInverted: string;
        suffixColorInverted: string;
        prefixColorInverted: string;
        optionColorHoverInverted: string;
        optionColorActiveInverted: string;
        groupHeaderTextColorInverted: string;
        optionOpacityDisabled: string;
        padding: string;
        optionIconSizeSmall: string;
        optionIconSizeMedium: string;
        optionIconSizeLarge: string;
        optionIconSizeHuge: string;
        optionSuffixWidthSmall: string;
        optionSuffixWidthMedium: string;
        optionSuffixWidthLarge: string;
        optionSuffixWidthHuge: string;
        optionIconSuffixWidthSmall: string;
        optionIconSuffixWidthMedium: string;
        optionIconSuffixWidthLarge: string;
        optionIconSuffixWidthHuge: string;
        optionPrefixWidthSmall: string;
        optionPrefixWidthMedium: string;
        optionPrefixWidthLarge: string;
        optionPrefixWidthHuge: string;
        optionIconPrefixWidthSmall: string;
        optionIconPrefixWidthMedium: string;
        optionIconPrefixWidthLarge: string;
        optionIconPrefixWidthHuge: string;
    }, {
        Popover: import("../../_mixins").Theme<"Popover", {
            fontSize: string;
            borderRadius: string;
            color: string;
            dividerColor: string;
            textColor: string;
            boxShadow: string;
            space: string;
            spaceArrow: string;
            arrowOffset: string;
            arrowOffsetVertical: string;
            arrowHeight: string;
            padding: string;
        }, any>;
    }>>;
    readonly themeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Dropdown", {
        optionHeightSmall: string;
        optionHeightMedium: string;
        optionHeightLarge: string;
        optionHeightHuge: string;
        borderRadius: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        fontSizeHuge: string;
        optionTextColor: string;
        optionTextColorHover: string;
        optionTextColorActive: string;
        optionTextColorChildActive: string;
        color: string;
        dividerColor: string;
        suffixColor: string;
        prefixColor: string;
        optionColorHover: string;
        optionColorActive: string;
        groupHeaderTextColor: string;
        optionTextColorInverted: string;
        optionTextColorHoverInverted: string;
        optionTextColorActiveInverted: string;
        optionTextColorChildActiveInverted: string;
        colorInverted: string;
        dividerColorInverted: string;
        suffixColorInverted: string;
        prefixColorInverted: string;
        optionColorHoverInverted: string;
        optionColorActiveInverted: string;
        groupHeaderTextColorInverted: string;
        optionOpacityDisabled: string;
        padding: string;
        optionIconSizeSmall: string;
        optionIconSizeMedium: string;
        optionIconSizeLarge: string;
        optionIconSizeHuge: string;
        optionSuffixWidthSmall: string;
        optionSuffixWidthMedium: string;
        optionSuffixWidthLarge: string;
        optionSuffixWidthHuge: string;
        optionIconSuffixWidthSmall: string;
        optionIconSuffixWidthMedium: string;
        optionIconSuffixWidthLarge: string;
        optionIconSuffixWidthHuge: string;
        optionPrefixWidthSmall: string;
        optionPrefixWidthMedium: string;
        optionPrefixWidthLarge: string;
        optionPrefixWidthHuge: string;
        optionIconPrefixWidthSmall: string;
        optionIconPrefixWidthMedium: string;
        optionIconPrefixWidthLarge: string;
        optionIconPrefixWidthHuge: string;
    }, {
        Popover: import("../../_mixins").Theme<"Popover", {
            fontSize: string;
            borderRadius: string;
            color: string;
            dividerColor: string;
            textColor: string;
            boxShadow: string;
            space: string;
            spaceArrow: string;
            arrowOffset: string;
            arrowOffsetVertical: string;
            arrowHeight: string;
            padding: string;
        }, any>;
    }>>>;
    readonly builtinThemeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Dropdown", {
        optionHeightSmall: string;
        optionHeightMedium: string;
        optionHeightLarge: string;
        optionHeightHuge: string;
        borderRadius: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        fontSizeHuge: string;
        optionTextColor: string;
        optionTextColorHover: string;
        optionTextColorActive: string;
        optionTextColorChildActive: string;
        color: string;
        dividerColor: string;
        suffixColor: string;
        prefixColor: string;
        optionColorHover: string;
        optionColorActive: string;
        groupHeaderTextColor: string;
        optionTextColorInverted: string;
        optionTextColorHoverInverted: string;
        optionTextColorActiveInverted: string;
        optionTextColorChildActiveInverted: string;
        colorInverted: string;
        dividerColorInverted: string;
        suffixColorInverted: string;
        prefixColorInverted: string;
        optionColorHoverInverted: string;
        optionColorActiveInverted: string;
        groupHeaderTextColorInverted: string;
        optionOpacityDisabled: string;
        padding: string;
        optionIconSizeSmall: string;
        optionIconSizeMedium: string;
        optionIconSizeLarge: string;
        optionIconSizeHuge: string;
        optionSuffixWidthSmall: string;
        optionSuffixWidthMedium: string;
        optionSuffixWidthLarge: string;
        optionSuffixWidthHuge: string;
        optionIconSuffixWidthSmall: string;
        optionIconSuffixWidthMedium: string;
        optionIconSuffixWidthLarge: string;
        optionIconSuffixWidthHuge: string;
        optionPrefixWidthSmall: string;
        optionPrefixWidthMedium: string;
        optionPrefixWidthLarge: string;
        optionPrefixWidthHuge: string;
        optionIconPrefixWidthSmall: string;
        optionIconPrefixWidthMedium: string;
        optionIconPrefixWidthLarge: string;
        optionIconPrefixWidthHuge: string;
    }, {
        Popover: import("../../_mixins").Theme<"Popover", {
            fontSize: string;
            borderRadius: string;
            color: string;
            dividerColor: string;
            textColor: string;
            boxShadow: string;
            space: string;
            spaceArrow: string;
            arrowOffset: string;
            arrowOffsetVertical: string;
            arrowHeight: string;
            padding: string;
        }, any>;
    }>>>;
    readonly animated: {
        readonly type: BooleanConstructor;
        readonly default: true;
    };
    readonly keyboard: {
        readonly type: BooleanConstructor;
        readonly default: true;
    };
    readonly size: {
        readonly type: PropType<"small" | "medium" | "large" | "huge">;
        readonly default: "medium";
    };
    readonly inverted: BooleanConstructor;
    readonly placement: {
        readonly type: PropType<FollowerPlacement>;
        readonly default: "bottom";
    };
    readonly onSelect: PropType<MaybeArray<OnUpdateValue>>;
    readonly options: {
        readonly type: PropType<DropdownMixedOption[]>;
        readonly default: () => never[];
    };
    readonly menuProps: PropType<DropdownMenuProps>;
    readonly showArrow: BooleanConstructor;
    readonly renderLabel: PropType<RenderLabel>;
    readonly renderIcon: PropType<RenderIcon>;
    readonly renderOption: PropType<RenderOption>;
    readonly nodeProps: PropType<NodeProps>;
    readonly labelField: {
        readonly type: StringConstructor;
        readonly default: "label";
    };
    readonly keyField: {
        readonly type: StringConstructor;
        readonly default: "key";
    };
    readonly childrenField: {
        readonly type: StringConstructor;
        readonly default: "children";
    };
    readonly value: PropType<Key | null>;
    readonly show: {
        type: PropType<boolean | undefined>;
        default: undefined;
    };
    readonly defaultShow: BooleanConstructor;
    readonly trigger: {
        type: PropType<import("../../popover").PopoverTrigger>;
        default: string;
    };
    readonly delay: {
        type: NumberConstructor;
        default: number;
    };
    readonly duration: {
        type: NumberConstructor;
        default: number;
    };
    readonly raw: BooleanConstructor;
    readonly x: NumberConstructor;
    readonly y: NumberConstructor;
    readonly arrowPointToCenter: BooleanConstructor;
    readonly disabled: BooleanConstructor;
    readonly getDisabled: PropType<() => boolean>;
    readonly displayDirective: {
        type: PropType<"if" | "show">;
        default: string;
    };
    readonly arrowClass: StringConstructor;
    readonly arrowStyle: PropType<string | import("vue").CSSProperties>;
    readonly arrowWrapperClass: StringConstructor;
    readonly arrowWrapperStyle: PropType<string | import("vue").CSSProperties>;
    readonly flip: {
        type: BooleanConstructor;
        default: boolean;
    };
    readonly width: {
        type: PropType<number | "trigger">;
        default: undefined;
    };
    readonly overlap: BooleanConstructor;
    readonly keepAliveOnHover: {
        type: BooleanConstructor;
        default: boolean;
    };
    readonly zIndex: NumberConstructor;
    readonly to: {
        type: PropType<HTMLElement | string | boolean>;
        default: undefined;
    };
    readonly scrollable: BooleanConstructor;
    readonly contentClass: StringConstructor;
    readonly contentStyle: PropType<import("vue").CSSProperties | string>;
    readonly headerClass: StringConstructor;
    readonly headerStyle: PropType<import("vue").CSSProperties | string>;
    readonly footerClass: StringConstructor;
    readonly footerStyle: PropType<import("vue").CSSProperties | string>;
    readonly onClickoutside: PropType<(e: MouseEvent) => void>;
    readonly 'onUpdate:show': PropType<MaybeArray<(value: boolean) => void>>;
    readonly onUpdateShow: PropType<MaybeArray<(value: boolean) => void>>;
    readonly internalDeactivateImmediately: BooleanConstructor;
    readonly internalSyncTargetWithParent: BooleanConstructor;
    readonly internalInheritedEventHandlers: {
        type: PropType<import("../../popover/src/Popover").TriggerEventHandlers[]>;
        default: () => never[];
    };
    readonly internalTrapFocus: BooleanConstructor;
    readonly internalExtraClass: {
        type: PropType<string[]>;
        default: () => never[];
    };
    readonly onShow: PropType<MaybeArray<(value: boolean) => void> | undefined>;
    readonly onHide: PropType<MaybeArray<(value: boolean) => void> | undefined>;
    readonly arrow: {
        type: PropType<boolean | undefined>;
        default: undefined;
    };
    readonly minWidth: NumberConstructor;
    readonly maxWidth: NumberConstructor;
};
export type DropdownProps = ExtractPublicPropTypes<typeof dropdownProps>;
declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    readonly theme: PropType<import("../../_mixins").Theme<"Dropdown", {
        optionHeightSmall: string;
        optionHeightMedium: string;
        optionHeightLarge: string;
        optionHeightHuge: string;
        borderRadius: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        fontSizeHuge: string;
        optionTextColor: string;
        optionTextColorHover: string;
        optionTextColorActive: string;
        optionTextColorChildActive: string;
        color: string;
        dividerColor: string;
        suffixColor: string;
        prefixColor: string;
        optionColorHover: string;
        optionColorActive: string;
        groupHeaderTextColor: string;
        optionTextColorInverted: string;
        optionTextColorHoverInverted: string;
        optionTextColorActiveInverted: string;
        optionTextColorChildActiveInverted: string;
        colorInverted: string;
        dividerColorInverted: string;
        suffixColorInverted: string;
        prefixColorInverted: string;
        optionColorHoverInverted: string;
        optionColorActiveInverted: string;
        groupHeaderTextColorInverted: string;
        optionOpacityDisabled: string;
        padding: string;
        optionIconSizeSmall: string;
        optionIconSizeMedium: string;
        optionIconSizeLarge: string;
        optionIconSizeHuge: string;
        optionSuffixWidthSmall: string;
        optionSuffixWidthMedium: string;
        optionSuffixWidthLarge: string;
        optionSuffixWidthHuge: string;
        optionIconSuffixWidthSmall: string;
        optionIconSuffixWidthMedium: string;
        optionIconSuffixWidthLarge: string;
        optionIconSuffixWidthHuge: string;
        optionPrefixWidthSmall: string;
        optionPrefixWidthMedium: string;
        optionPrefixWidthLarge: string;
        optionPrefixWidthHuge: string;
        optionIconPrefixWidthSmall: string;
        optionIconPrefixWidthMedium: string;
        optionIconPrefixWidthLarge: string;
        optionIconPrefixWidthHuge: string;
    }, {
        Popover: import("../../_mixins").Theme<"Popover", {
            fontSize: string;
            borderRadius: string;
            color: string;
            dividerColor: string;
            textColor: string;
            boxShadow: string;
            space: string;
            spaceArrow: string;
            arrowOffset: string;
            arrowOffsetVertical: string;
            arrowHeight: string;
            padding: string;
        }, any>;
    }>>;
    readonly themeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Dropdown", {
        optionHeightSmall: string;
        optionHeightMedium: string;
        optionHeightLarge: string;
        optionHeightHuge: string;
        borderRadius: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        fontSizeHuge: string;
        optionTextColor: string;
        optionTextColorHover: string;
        optionTextColorActive: string;
        optionTextColorChildActive: string;
        color: string;
        dividerColor: string;
        suffixColor: string;
        prefixColor: string;
        optionColorHover: string;
        optionColorActive: string;
        groupHeaderTextColor: string;
        optionTextColorInverted: string;
        optionTextColorHoverInverted: string;
        optionTextColorActiveInverted: string;
        optionTextColorChildActiveInverted: string;
        colorInverted: string;
        dividerColorInverted: string;
        suffixColorInverted: string;
        prefixColorInverted: string;
        optionColorHoverInverted: string;
        optionColorActiveInverted: string;
        groupHeaderTextColorInverted: string;
        optionOpacityDisabled: string;
        padding: string;
        optionIconSizeSmall: string;
        optionIconSizeMedium: string;
        optionIconSizeLarge: string;
        optionIconSizeHuge: string;
        optionSuffixWidthSmall: string;
        optionSuffixWidthMedium: string;
        optionSuffixWidthLarge: string;
        optionSuffixWidthHuge: string;
        optionIconSuffixWidthSmall: string;
        optionIconSuffixWidthMedium: string;
        optionIconSuffixWidthLarge: string;
        optionIconSuffixWidthHuge: string;
        optionPrefixWidthSmall: string;
        optionPrefixWidthMedium: string;
        optionPrefixWidthLarge: string;
        optionPrefixWidthHuge: string;
        optionIconPrefixWidthSmall: string;
        optionIconPrefixWidthMedium: string;
        optionIconPrefixWidthLarge: string;
        optionIconPrefixWidthHuge: string;
    }, {
        Popover: import("../../_mixins").Theme<"Popover", {
            fontSize: string;
            borderRadius: string;
            color: string;
            dividerColor: string;
            textColor: string;
            boxShadow: string;
            space: string;
            spaceArrow: string;
            arrowOffset: string;
            arrowOffsetVertical: string;
            arrowHeight: string;
            padding: string;
        }, any>;
    }>>>;
    readonly builtinThemeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Dropdown", {
        optionHeightSmall: string;
        optionHeightMedium: string;
        optionHeightLarge: string;
        optionHeightHuge: string;
        borderRadius: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        fontSizeHuge: string;
        optionTextColor: string;
        optionTextColorHover: string;
        optionTextColorActive: string;
        optionTextColorChildActive: string;
        color: string;
        dividerColor: string;
        suffixColor: string;
        prefixColor: string;
        optionColorHover: string;
        optionColorActive: string;
        groupHeaderTextColor: string;
        optionTextColorInverted: string;
        optionTextColorHoverInverted: string;
        optionTextColorActiveInverted: string;
        optionTextColorChildActiveInverted: string;
        colorInverted: string;
        dividerColorInverted: string;
        suffixColorInverted: string;
        prefixColorInverted: string;
        optionColorHoverInverted: string;
        optionColorActiveInverted: string;
        groupHeaderTextColorInverted: string;
        optionOpacityDisabled: string;
        padding: string;
        optionIconSizeSmall: string;
        optionIconSizeMedium: string;
        optionIconSizeLarge: string;
        optionIconSizeHuge: string;
        optionSuffixWidthSmall: string;
        optionSuffixWidthMedium: string;
        optionSuffixWidthLarge: string;
        optionSuffixWidthHuge: string;
        optionIconSuffixWidthSmall: string;
        optionIconSuffixWidthMedium: string;
        optionIconSuffixWidthLarge: string;
        optionIconSuffixWidthHuge: string;
        optionPrefixWidthSmall: string;
        optionPrefixWidthMedium: string;
        optionPrefixWidthLarge: string;
        optionPrefixWidthHuge: string;
        optionIconPrefixWidthSmall: string;
        optionIconPrefixWidthMedium: string;
        optionIconPrefixWidthLarge: string;
        optionIconPrefixWidthHuge: string;
    }, {
        Popover: import("../../_mixins").Theme<"Popover", {
            fontSize: string;
            borderRadius: string;
            color: string;
            dividerColor: string;
            textColor: string;
            boxShadow: string;
            space: string;
            spaceArrow: string;
            arrowOffset: string;
            arrowOffsetVertical: string;
            arrowHeight: string;
            padding: string;
        }, any>;
    }>>>;
    readonly animated: {
        readonly type: BooleanConstructor;
        readonly default: true;
    };
    readonly keyboard: {
        readonly type: BooleanConstructor;
        readonly default: true;
    };
    readonly size: {
        readonly type: PropType<"small" | "medium" | "large" | "huge">;
        readonly default: "medium";
    };
    readonly inverted: BooleanConstructor;
    readonly placement: {
        readonly type: PropType<FollowerPlacement>;
        readonly default: "bottom";
    };
    readonly onSelect: PropType<MaybeArray<OnUpdateValue>>;
    readonly options: {
        readonly type: PropType<DropdownMixedOption[]>;
        readonly default: () => never[];
    };
    readonly menuProps: PropType<DropdownMenuProps>;
    readonly showArrow: BooleanConstructor;
    readonly renderLabel: PropType<RenderLabel>;
    readonly renderIcon: PropType<RenderIcon>;
    readonly renderOption: PropType<RenderOption>;
    readonly nodeProps: PropType<NodeProps>;
    readonly labelField: {
        readonly type: StringConstructor;
        readonly default: "label";
    };
    readonly keyField: {
        readonly type: StringConstructor;
        readonly default: "key";
    };
    readonly childrenField: {
        readonly type: StringConstructor;
        readonly default: "children";
    };
    readonly value: PropType<Key | null>;
    readonly show: {
        type: PropType<boolean | undefined>;
        default: undefined;
    };
    readonly defaultShow: BooleanConstructor;
    readonly trigger: {
        type: PropType<import("../../popover").PopoverTrigger>;
        default: string;
    };
    readonly delay: {
        type: NumberConstructor;
        default: number;
    };
    readonly duration: {
        type: NumberConstructor;
        default: number;
    };
    readonly raw: BooleanConstructor;
    readonly x: NumberConstructor;
    readonly y: NumberConstructor;
    readonly arrowPointToCenter: BooleanConstructor;
    readonly disabled: BooleanConstructor;
    readonly getDisabled: PropType<() => boolean>;
    readonly displayDirective: {
        type: PropType<"if" | "show">;
        default: string;
    };
    readonly arrowClass: StringConstructor;
    readonly arrowStyle: PropType<string | import("vue").CSSProperties>;
    readonly arrowWrapperClass: StringConstructor;
    readonly arrowWrapperStyle: PropType<string | import("vue").CSSProperties>;
    readonly flip: {
        type: BooleanConstructor;
        default: boolean;
    };
    readonly width: {
        type: PropType<number | "trigger">;
        default: undefined;
    };
    readonly overlap: BooleanConstructor;
    readonly keepAliveOnHover: {
        type: BooleanConstructor;
        default: boolean;
    };
    readonly zIndex: NumberConstructor;
    readonly to: {
        type: PropType<HTMLElement | string | boolean>;
        default: undefined;
    };
    readonly scrollable: BooleanConstructor;
    readonly contentClass: StringConstructor;
    readonly contentStyle: PropType<import("vue").CSSProperties | string>;
    readonly headerClass: StringConstructor;
    readonly headerStyle: PropType<import("vue").CSSProperties | string>;
    readonly footerClass: StringConstructor;
    readonly footerStyle: PropType<import("vue").CSSProperties | string>;
    readonly onClickoutside: PropType<(e: MouseEvent) => void>;
    readonly 'onUpdate:show': PropType<MaybeArray<(value: boolean) => void>>;
    readonly onUpdateShow: PropType<MaybeArray<(value: boolean) => void>>;
    readonly internalDeactivateImmediately: BooleanConstructor;
    readonly internalSyncTargetWithParent: BooleanConstructor;
    readonly internalInheritedEventHandlers: {
        type: PropType<import("../../popover/src/Popover").TriggerEventHandlers[]>;
        default: () => never[];
    };
    readonly internalTrapFocus: BooleanConstructor;
    readonly internalExtraClass: {
        type: PropType<string[]>;
        default: () => never[];
    };
    readonly onShow: PropType<MaybeArray<(value: boolean) => void> | undefined>;
    readonly onHide: PropType<MaybeArray<(value: boolean) => void> | undefined>;
    readonly arrow: {
        type: PropType<boolean | undefined>;
        default: undefined;
    };
    readonly minWidth: NumberConstructor;
    readonly maxWidth: NumberConstructor;
}>, {
    mergedClsPrefix: Ref<string, string>;
    mergedTheme: import("vue").ComputedRef<{
        common: import("../..").ThemeCommonVars;
        self: {
            optionHeightSmall: string;
            optionHeightMedium: string;
            optionHeightLarge: string;
            optionHeightHuge: string;
            borderRadius: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            fontSizeHuge: string;
            optionTextColor: string;
            optionTextColorHover: string;
            optionTextColorActive: string;
            optionTextColorChildActive: string;
            color: string;
            dividerColor: string;
            suffixColor: string;
            prefixColor: string;
            optionColorHover: string;
            optionColorActive: string;
            groupHeaderTextColor: string;
            optionTextColorInverted: string;
            optionTextColorHoverInverted: string;
            optionTextColorActiveInverted: string;
            optionTextColorChildActiveInverted: string;
            colorInverted: string;
            dividerColorInverted: string;
            suffixColorInverted: string;
            prefixColorInverted: string;
            optionColorHoverInverted: string;
            optionColorActiveInverted: string;
            groupHeaderTextColorInverted: string;
            optionOpacityDisabled: string;
            padding: string;
            optionIconSizeSmall: string;
            optionIconSizeMedium: string;
            optionIconSizeLarge: string;
            optionIconSizeHuge: string;
            optionSuffixWidthSmall: string;
            optionSuffixWidthMedium: string;
            optionSuffixWidthLarge: string;
            optionSuffixWidthHuge: string;
            optionIconSuffixWidthSmall: string;
            optionIconSuffixWidthMedium: string;
            optionIconSuffixWidthLarge: string;
            optionIconSuffixWidthHuge: string;
            optionPrefixWidthSmall: string;
            optionPrefixWidthMedium: string;
            optionPrefixWidthLarge: string;
            optionPrefixWidthHuge: string;
            optionIconPrefixWidthSmall: string;
            optionIconPrefixWidthMedium: string;
            optionIconPrefixWidthLarge: string;
            optionIconPrefixWidthHuge: string;
        };
        peers: {
            Popover: import("../../_mixins").Theme<"Popover", {
                fontSize: string;
                borderRadius: string;
                color: string;
                dividerColor: string;
                textColor: string;
                boxShadow: string;
                space: string;
                spaceArrow: string;
                arrowOffset: string;
                arrowOffsetVertical: string;
                arrowHeight: string;
                padding: string;
            }, any>;
        };
        peerOverrides: {
            Popover?: {
                peers?: {
                    [x: string]: any;
                } | undefined;
            } | undefined;
        };
    }>;
    tmNodes: import("vue").ComputedRef<TreeNode<import("../..").MenuOption | import("../../menu/src/interface").MenuRenderOption, import("../..").MenuGroupOption, import("../../menu/src/interface").MenuIgnoredOption>[]>;
    mergedShow: import("vue").ComputedRef<boolean>;
    handleAfterLeave: () => void;
    doUpdateShow: (value: boolean) => void;
    cssVars: import("vue").ComputedRef<any> | undefined;
    themeClass: Ref<string, string> | undefined;
    onRender: (() => void) | undefined;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    readonly theme: PropType<import("../../_mixins").Theme<"Dropdown", {
        optionHeightSmall: string;
        optionHeightMedium: string;
        optionHeightLarge: string;
        optionHeightHuge: string;
        borderRadius: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        fontSizeHuge: string;
        optionTextColor: string;
        optionTextColorHover: string;
        optionTextColorActive: string;
        optionTextColorChildActive: string;
        color: string;
        dividerColor: string;
        suffixColor: string;
        prefixColor: string;
        optionColorHover: string;
        optionColorActive: string;
        groupHeaderTextColor: string;
        optionTextColorInverted: string;
        optionTextColorHoverInverted: string;
        optionTextColorActiveInverted: string;
        optionTextColorChildActiveInverted: string;
        colorInverted: string;
        dividerColorInverted: string;
        suffixColorInverted: string;
        prefixColorInverted: string;
        optionColorHoverInverted: string;
        optionColorActiveInverted: string;
        groupHeaderTextColorInverted: string;
        optionOpacityDisabled: string;
        padding: string;
        optionIconSizeSmall: string;
        optionIconSizeMedium: string;
        optionIconSizeLarge: string;
        optionIconSizeHuge: string;
        optionSuffixWidthSmall: string;
        optionSuffixWidthMedium: string;
        optionSuffixWidthLarge: string;
        optionSuffixWidthHuge: string;
        optionIconSuffixWidthSmall: string;
        optionIconSuffixWidthMedium: string;
        optionIconSuffixWidthLarge: string;
        optionIconSuffixWidthHuge: string;
        optionPrefixWidthSmall: string;
        optionPrefixWidthMedium: string;
        optionPrefixWidthLarge: string;
        optionPrefixWidthHuge: string;
        optionIconPrefixWidthSmall: string;
        optionIconPrefixWidthMedium: string;
        optionIconPrefixWidthLarge: string;
        optionIconPrefixWidthHuge: string;
    }, {
        Popover: import("../../_mixins").Theme<"Popover", {
            fontSize: string;
            borderRadius: string;
            color: string;
            dividerColor: string;
            textColor: string;
            boxShadow: string;
            space: string;
            spaceArrow: string;
            arrowOffset: string;
            arrowOffsetVertical: string;
            arrowHeight: string;
            padding: string;
        }, any>;
    }>>;
    readonly themeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Dropdown", {
        optionHeightSmall: string;
        optionHeightMedium: string;
        optionHeightLarge: string;
        optionHeightHuge: string;
        borderRadius: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        fontSizeHuge: string;
        optionTextColor: string;
        optionTextColorHover: string;
        optionTextColorActive: string;
        optionTextColorChildActive: string;
        color: string;
        dividerColor: string;
        suffixColor: string;
        prefixColor: string;
        optionColorHover: string;
        optionColorActive: string;
        groupHeaderTextColor: string;
        optionTextColorInverted: string;
        optionTextColorHoverInverted: string;
        optionTextColorActiveInverted: string;
        optionTextColorChildActiveInverted: string;
        colorInverted: string;
        dividerColorInverted: string;
        suffixColorInverted: string;
        prefixColorInverted: string;
        optionColorHoverInverted: string;
        optionColorActiveInverted: string;
        groupHeaderTextColorInverted: string;
        optionOpacityDisabled: string;
        padding: string;
        optionIconSizeSmall: string;
        optionIconSizeMedium: string;
        optionIconSizeLarge: string;
        optionIconSizeHuge: string;
        optionSuffixWidthSmall: string;
        optionSuffixWidthMedium: string;
        optionSuffixWidthLarge: string;
        optionSuffixWidthHuge: string;
        optionIconSuffixWidthSmall: string;
        optionIconSuffixWidthMedium: string;
        optionIconSuffixWidthLarge: string;
        optionIconSuffixWidthHuge: string;
        optionPrefixWidthSmall: string;
        optionPrefixWidthMedium: string;
        optionPrefixWidthLarge: string;
        optionPrefixWidthHuge: string;
        optionIconPrefixWidthSmall: string;
        optionIconPrefixWidthMedium: string;
        optionIconPrefixWidthLarge: string;
        optionIconPrefixWidthHuge: string;
    }, {
        Popover: import("../../_mixins").Theme<"Popover", {
            fontSize: string;
            borderRadius: string;
            color: string;
            dividerColor: string;
            textColor: string;
            boxShadow: string;
            space: string;
            spaceArrow: string;
            arrowOffset: string;
            arrowOffsetVertical: string;
            arrowHeight: string;
            padding: string;
        }, any>;
    }>>>;
    readonly builtinThemeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Dropdown", {
        optionHeightSmall: string;
        optionHeightMedium: string;
        optionHeightLarge: string;
        optionHeightHuge: string;
        borderRadius: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        fontSizeHuge: string;
        optionTextColor: string;
        optionTextColorHover: string;
        optionTextColorActive: string;
        optionTextColorChildActive: string;
        color: string;
        dividerColor: string;
        suffixColor: string;
        prefixColor: string;
        optionColorHover: string;
        optionColorActive: string;
        groupHeaderTextColor: string;
        optionTextColorInverted: string;
        optionTextColorHoverInverted: string;
        optionTextColorActiveInverted: string;
        optionTextColorChildActiveInverted: string;
        colorInverted: string;
        dividerColorInverted: string;
        suffixColorInverted: string;
        prefixColorInverted: string;
        optionColorHoverInverted: string;
        optionColorActiveInverted: string;
        groupHeaderTextColorInverted: string;
        optionOpacityDisabled: string;
        padding: string;
        optionIconSizeSmall: string;
        optionIconSizeMedium: string;
        optionIconSizeLarge: string;
        optionIconSizeHuge: string;
        optionSuffixWidthSmall: string;
        optionSuffixWidthMedium: string;
        optionSuffixWidthLarge: string;
        optionSuffixWidthHuge: string;
        optionIconSuffixWidthSmall: string;
        optionIconSuffixWidthMedium: string;
        optionIconSuffixWidthLarge: string;
        optionIconSuffixWidthHuge: string;
        optionPrefixWidthSmall: string;
        optionPrefixWidthMedium: string;
        optionPrefixWidthLarge: string;
        optionPrefixWidthHuge: string;
        optionIconPrefixWidthSmall: string;
        optionIconPrefixWidthMedium: string;
        optionIconPrefixWidthLarge: string;
        optionIconPrefixWidthHuge: string;
    }, {
        Popover: import("../../_mixins").Theme<"Popover", {
            fontSize: string;
            borderRadius: string;
            color: string;
            dividerColor: string;
            textColor: string;
            boxShadow: string;
            space: string;
            spaceArrow: string;
            arrowOffset: string;
            arrowOffsetVertical: string;
            arrowHeight: string;
            padding: string;
        }, any>;
    }>>>;
    readonly animated: {
        readonly type: BooleanConstructor;
        readonly default: true;
    };
    readonly keyboard: {
        readonly type: BooleanConstructor;
        readonly default: true;
    };
    readonly size: {
        readonly type: PropType<"small" | "medium" | "large" | "huge">;
        readonly default: "medium";
    };
    readonly inverted: BooleanConstructor;
    readonly placement: {
        readonly type: PropType<FollowerPlacement>;
        readonly default: "bottom";
    };
    readonly onSelect: PropType<MaybeArray<OnUpdateValue>>;
    readonly options: {
        readonly type: PropType<DropdownMixedOption[]>;
        readonly default: () => never[];
    };
    readonly menuProps: PropType<DropdownMenuProps>;
    readonly showArrow: BooleanConstructor;
    readonly renderLabel: PropType<RenderLabel>;
    readonly renderIcon: PropType<RenderIcon>;
    readonly renderOption: PropType<RenderOption>;
    readonly nodeProps: PropType<NodeProps>;
    readonly labelField: {
        readonly type: StringConstructor;
        readonly default: "label";
    };
    readonly keyField: {
        readonly type: StringConstructor;
        readonly default: "key";
    };
    readonly childrenField: {
        readonly type: StringConstructor;
        readonly default: "children";
    };
    readonly value: PropType<Key | null>;
    readonly show: {
        type: PropType<boolean | undefined>;
        default: undefined;
    };
    readonly defaultShow: BooleanConstructor;
    readonly trigger: {
        type: PropType<import("../../popover").PopoverTrigger>;
        default: string;
    };
    readonly delay: {
        type: NumberConstructor;
        default: number;
    };
    readonly duration: {
        type: NumberConstructor;
        default: number;
    };
    readonly raw: BooleanConstructor;
    readonly x: NumberConstructor;
    readonly y: NumberConstructor;
    readonly arrowPointToCenter: BooleanConstructor;
    readonly disabled: BooleanConstructor;
    readonly getDisabled: PropType<() => boolean>;
    readonly displayDirective: {
        type: PropType<"if" | "show">;
        default: string;
    };
    readonly arrowClass: StringConstructor;
    readonly arrowStyle: PropType<string | import("vue").CSSProperties>;
    readonly arrowWrapperClass: StringConstructor;
    readonly arrowWrapperStyle: PropType<string | import("vue").CSSProperties>;
    readonly flip: {
        type: BooleanConstructor;
        default: boolean;
    };
    readonly width: {
        type: PropType<number | "trigger">;
        default: undefined;
    };
    readonly overlap: BooleanConstructor;
    readonly keepAliveOnHover: {
        type: BooleanConstructor;
        default: boolean;
    };
    readonly zIndex: NumberConstructor;
    readonly to: {
        type: PropType<HTMLElement | string | boolean>;
        default: undefined;
    };
    readonly scrollable: BooleanConstructor;
    readonly contentClass: StringConstructor;
    readonly contentStyle: PropType<import("vue").CSSProperties | string>;
    readonly headerClass: StringConstructor;
    readonly headerStyle: PropType<import("vue").CSSProperties | string>;
    readonly footerClass: StringConstructor;
    readonly footerStyle: PropType<import("vue").CSSProperties | string>;
    readonly onClickoutside: PropType<(e: MouseEvent) => void>;
    readonly 'onUpdate:show': PropType<MaybeArray<(value: boolean) => void>>;
    readonly onUpdateShow: PropType<MaybeArray<(value: boolean) => void>>;
    readonly internalDeactivateImmediately: BooleanConstructor;
    readonly internalSyncTargetWithParent: BooleanConstructor;
    readonly internalInheritedEventHandlers: {
        type: PropType<import("../../popover/src/Popover").TriggerEventHandlers[]>;
        default: () => never[];
    };
    readonly internalTrapFocus: BooleanConstructor;
    readonly internalExtraClass: {
        type: PropType<string[]>;
        default: () => never[];
    };
    readonly onShow: PropType<MaybeArray<(value: boolean) => void> | undefined>;
    readonly onHide: PropType<MaybeArray<(value: boolean) => void> | undefined>;
    readonly arrow: {
        type: PropType<boolean | undefined>;
        default: undefined;
    };
    readonly minWidth: NumberConstructor;
    readonly maxWidth: NumberConstructor;
}>> & Readonly<{}>, {
    readonly options: DropdownMixedOption[];
    readonly size: "small" | "medium" | "large" | "huge";
    readonly to: string | boolean | HTMLElement;
    readonly disabled: boolean;
    readonly show: boolean | undefined;
    readonly flip: boolean;
    readonly width: number | "trigger";
    readonly duration: number;
    readonly raw: boolean;
    readonly placement: FollowerPlacement;
    readonly overlap: boolean;
    readonly keyField: string;
    readonly scrollable: boolean;
    readonly trigger: import("../../popover").PopoverTrigger;
    readonly labelField: string;
    readonly showArrow: boolean;
    readonly delay: number;
    readonly arrowPointToCenter: boolean;
    readonly displayDirective: "show" | "if";
    readonly keepAliveOnHover: boolean;
    readonly internalDeactivateImmediately: boolean;
    readonly animated: boolean;
    readonly internalTrapFocus: boolean;
    readonly defaultShow: boolean;
    readonly internalSyncTargetWithParent: boolean;
    readonly internalInheritedEventHandlers: import("../../popover/src/Popover").TriggerEventHandlers[];
    readonly internalExtraClass: string[];
    readonly arrow: boolean | undefined;
    readonly keyboard: boolean;
    readonly childrenField: string;
    readonly inverted: boolean;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;

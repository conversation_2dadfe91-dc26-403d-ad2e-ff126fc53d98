<template>
  <div class="hls-preview">
    <div class="preview-container">
      <!-- 双缓冲视频播放器 -->
      <video
        ref="videoElementA"
        class="preview-video"
        :class="{ active: activeVideo === 'A' && isStreamReady }"
        @loadedmetadata="handleVideoLoaded"
        @timeupdate="handleTimeUpdate"
        @ended="handleVideoEnded"
        @error="handleVideoError"
      >
        您的浏览器不支持视频播放
      </video>

      <video
        ref="videoElementB"
        class="preview-video"
        :class="{ active: activeVideo === 'B' && isStreamReady }"
        @loadedmetadata="handleVideoLoaded"
        @timeupdate="handleTimeUpdate"
        @ended="handleVideoEnded"
        @error="handleVideoError"
      >
        您的浏览器不支持视频播放
      </video>
      
      <!-- 加载状态覆盖层 -->
      <div v-if="!isStreamReady" class="loading-overlay">
        <div class="loading-content">
          <div class="loading-spinner">⏳</div>
          <div class="loading-text">{{ loadingText }}</div>
          <div class="loading-subtext">{{ loadingSubtext }}</div>
          
          <!-- 进度指示器 -->
          <div class="loading-progress">
            <div class="progress-bar">
              <div 
                class="progress-fill" 
                :style="{ width: loadingProgress + '%' }"
              ></div>
            </div>
            <div class="progress-text">{{ Math.round(loadingProgress) }}%</div>
          </div>
        </div>
      </div>
      
      <!-- 错误状态 -->
      <div v-if="hasError" class="error-overlay">
        <div class="error-content">
          <div class="error-icon">❌</div>
          <div class="error-text">流媒体加载失败</div>
          <div class="error-detail">{{ errorMessage }}</div>
          <button class="retry-btn" @click="retryStream">重试</button>
        </div>
      </div>
    </div>
    
    <!-- 流信息显示 -->
    <div v-if="streamInfo" class="stream-info">
      <div class="info-item">
        <span class="label">流ID:</span>
        <span class="value">{{ streamInfo.streamId?.substring(0, 8) }}...</span>
      </div>
      <div class="info-item">
        <span class="label">状态:</span>
        <span class="value" :class="statusClass">{{ statusText }}</span>
      </div>
      <div class="info-item">
        <span class="label">延迟:</span>
        <span class="value">~2-3秒</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import Hls from 'hls.js'

interface StreamInfo {
  streamId: string
  playlistUrl: string
  status: 'starting' | 'ready' | 'error'
  isReady: boolean
}

interface TimelineConfig {
  clips: Array<{
    id: string
    mediaId: string
    startTime: number
    duration: number
    trimStart: number
    trimEnd: number
  }>
}

const props = defineProps<{
  timelineConfig?: TimelineConfig
  autoStart?: boolean
}>()

const emit = defineEmits<{
  streamReady: [streamId: string]
  streamError: [error: string]
  timeUpdate: [currentTime: number]
}>()

// 响应式数据
const videoElementA = ref<HTMLVideoElement>()
const videoElementB = ref<HTMLVideoElement>()
const activeVideo = ref<'A' | 'B'>('A')
const hls = ref<Hls | null>(null)
const streamInfo = ref<StreamInfo | null>(null)
const isStreamReady = ref(false)
const hasError = ref(false)
const errorMessage = ref('')
const loadingProgress = ref(0)
const currentClipIndex = ref(0)
const clips = ref<any[]>([])

// 加载状态文本
const loadingText = computed(() => {
  if (loadingProgress.value < 20) return '正在准备视频...'
  if (loadingProgress.value < 50) return '正在处理视频片段...'
  if (loadingProgress.value < 80) return '正在生成播放文件...'
  return '即将完成...'
})

const loadingSubtext = computed(() => {
  return '正在准备无缝播放，请稍候'
})

const statusText = computed(() => {
  if (!streamInfo.value) return '未连接'
  switch (streamInfo.value.status) {
    case 'starting': return '启动中'
    case 'ready': return '就绪'
    case 'error': return '错误'
    default: return '未知'
  }
})

const statusClass = computed(() => {
  if (!streamInfo.value) return 'status-disconnected'
  switch (streamInfo.value.status) {
    case 'starting': return 'status-starting'
    case 'ready': return 'status-ready'
    case 'error': return 'status-error'
    default: return 'status-unknown'
  }
})

// 辅助函数
const getCurrentVideoElement = (): HTMLVideoElement | undefined => {
  return activeVideo.value === 'A' ? videoElementA.value : videoElementB.value
}

const getBackupVideoElement = (): HTMLVideoElement | undefined => {
  return activeVideo.value === 'A' ? videoElementB.value : videoElementA.value
}

// 方法
const startStream = async (timelineConfig: TimelineConfig) => {
  try {
    hasError.value = false
    isStreamReady.value = false
    loadingProgress.value = 0
    
    console.log('启动HLS流，时间轴配置:', timelineConfig)
    
    // 启动进度模拟
    const progressInterval = setInterval(() => {
      if (loadingProgress.value < 90) {
        loadingProgress.value += Math.random() * 10
      }
    }, 200)
    
    // 调用后端API启动流
    const response = await fetch('/api/stream/start', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(timelineConfig)
    })
    
    if (!response.ok) {
      throw new Error(`启动流失败: ${response.statusText}`)
    }
    
    const result = await response.json()
    streamInfo.value = result
    
    console.log('流启动响应:', result)
    
    // 等待流就绪
    await waitForStreamReady(result.streamId)
    
    // 初始化HLS播放器
    await initializeHLSPlayer(result.playlistUrl)
    
    clearInterval(progressInterval)
    loadingProgress.value = 100
    isStreamReady.value = true
    
    emit('streamReady', result.streamId)
    
  } catch (error) {
    console.error('启动流失败:', error)
    hasError.value = true
    errorMessage.value = error instanceof Error ? error.message : '未知错误'
    emit('streamError', errorMessage.value)
  }
}

const waitForStreamReady = async (streamId: string, maxAttempts = 30) => {
  for (let i = 0; i < maxAttempts; i++) {
    try {
      const response = await fetch(`/api/stream/${streamId}/status`)
      const status = await response.json()
      
      if (status.isReady) {
        console.log('流已就绪')
        return
      }
      
      // 更新进度
      loadingProgress.value = Math.min(80, 20 + (i / maxAttempts) * 60)
      
      // 等待1秒后重试
      await new Promise(resolve => setTimeout(resolve, 1000))
      
    } catch (error) {
      console.warn('检查流状态失败:', error)
    }
  }
  
  throw new Error('流启动超时')
}

const initializeHLSPlayer = async (playlistUrl: string) => {
  const currentVideo = getCurrentVideoElement()
  if (!currentVideo) {
    throw new Error('视频元素不存在')
  }

  // 从播放列表URL推断视频文件路径
  const streamId = playlistUrl.split('/')[3] // 提取stream ID
  const videoUrl = `/storage/streams/${streamId}/segment_0.mp4`

  console.log('直接播放视频文件:', videoUrl)

  // 直接设置视频源
  currentVideo.src = videoUrl

  // 监听视频事件
  currentVideo.addEventListener('loadedmetadata', () => {
    console.log('视频元数据加载完成')
  })

  currentVideo.addEventListener('error', (event) => {
    console.error('视频播放错误:', event)
    hasError.value = true
    errorMessage.value = '视频播放失败'
  })

  // 尝试播放
  try {
    await currentVideo.play()
    console.log('视频开始播放')
  } catch (error) {
    console.warn('自动播放失败，等待用户交互:', error)
  }
}

const stopStream = async () => {
  if (streamInfo.value) {
    try {
      await fetch(`/api/stream/${streamInfo.value.streamId}`, {
        method: 'DELETE'
      })
    } catch (error) {
      console.warn('停止流失败:', error)
    }
  }

  // 停止所有视频播放
  [videoElementA.value, videoElementB.value].forEach(video => {
    if (video) {
      video.pause()
      video.src = ''
    }
  })

  if (hls.value) {
    hls.value.destroy()
    hls.value = null
  }

  streamInfo.value = null
  isStreamReady.value = false
  hasError.value = false
}

const retryStream = () => {
  if (props.timelineConfig) {
    startStream(props.timelineConfig)
  }
}

// 事件处理
const handleVideoLoaded = () => {
  console.log('HLS视频加载完成')
}

const handleTimeUpdate = () => {
  const currentVideo = getCurrentVideoElement()
  if (currentVideo) {
    emit('timeUpdate', currentVideo.currentTime)
  }
}

const handleVideoEnded = () => {
  console.log('视频播放结束')
  // 这里可以添加切换到下一个片段的逻辑
}

const handleVideoError = (event: Event) => {
  console.error('视频播放错误:', event)
  hasError.value = true
  errorMessage.value = '视频播放失败'
}

// 监听时间轴配置变化
watch(() => props.timelineConfig, (newConfig) => {
  if (newConfig && props.autoStart) {
    startStream(newConfig)
  }
}, { deep: true })

// 组件挂载
onMounted(() => {
  if (props.timelineConfig && props.autoStart) {
    startStream(props.timelineConfig)
  }
})

// 组件卸载
onUnmounted(() => {
  stopStream()
})

// 暴露方法给父组件
defineExpose({
  startStream,
  stopStream,
  retryStream
})
</script>

<style scoped>
.hls-preview {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #000;
}

.preview-container {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  min-height: 300px;
  overflow: hidden;
}

.preview-video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  background-color: #000;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.preview-video.active {
  opacity: 1;
  pointer-events: auto;
}

.loading-overlay,
.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
}

.loading-content,
.error-content {
  text-align: center;
  max-width: 300px;
}

.loading-spinner {
  font-size: 48px;
  margin-bottom: 16px;
  animation: spin 2s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.loading-text {
  font-size: 18px;
  margin-bottom: 8px;
  font-weight: 500;
}

.loading-subtext {
  font-size: 14px;
  color: #ccc;
  margin-bottom: 24px;
}

.loading-progress {
  width: 100%;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background-color: #333;
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-fill {
  height: 100%;
  background-color: #007acc;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 12px;
  color: #ccc;
}

.error-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.error-text {
  font-size: 18px;
  margin-bottom: 8px;
  color: #ff6b6b;
}

.error-detail {
  font-size: 14px;
  color: #ccc;
  margin-bottom: 24px;
}

.retry-btn {
  background-color: #007acc;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s ease;
}

.retry-btn:hover {
  background-color: #0066aa;
}

.stream-info {
  padding: 8px 16px;
  background-color: rgba(0, 0, 0, 0.8);
  color: #fff;
  display: flex;
  gap: 16px;
  font-size: 12px;
  border-top: 1px solid #333;
}

.info-item {
  display: flex;
  gap: 4px;
}

.label {
  color: #ccc;
}

.value {
  font-weight: 500;
}

.status-disconnected { color: #666; }
.status-starting { color: #ffa500; }
.status-ready { color: #00ff00; }
.status-error { color: #ff6b6b; }
.status-unknown { color: #ccc; }
</style>

import type { MergedTheme } from '../../_mixins';
import type { ExtractPublicPropTypes, MaybeArray } from '../../_utils';
import type { StepsTheme } from '../styles';
import { type ExtractPropTypes, type PropType, type Ref, type SlotsType, type VNode } from 'vue';
export declare const stepsProps: {
    current: NumberConstructor;
    status: {
        type: PropType<"process" | "finish" | "error" | "wait">;
        default: string;
    };
    size: {
        type: PropType<"small" | "medium">;
        default: string;
    };
    vertical: BooleanConstructor;
    'onUpdate:current': PropType<MaybeArray<(current: number) => void>>;
    onUpdateCurrent: PropType<MaybeArray<(current: number) => void>>;
    theme: PropType<import("../../_mixins").Theme<"Steps", {
        stepHeaderFontWeight: string;
        indicatorTextColorProcess: string;
        indicatorTextColorWait: string;
        indicatorTextColorFinish: string;
        indicatorTextColorError: string;
        indicatorBorderColorProcess: string;
        indicatorBorderColorWait: string;
        indicatorBorderColorFinish: string;
        indicatorBorderColorError: string;
        indicatorColorProcess: string;
        indicatorColorWait: string;
        indicatorColorFinish: string;
        indicatorColorError: string;
        splitorColorProcess: string;
        splitorColorWait: string;
        splitorColorFinish: string;
        splitorColorError: string;
        headerTextColorProcess: string;
        headerTextColorWait: string;
        headerTextColorFinish: string;
        headerTextColorError: string;
        descriptionTextColorProcess: string;
        descriptionTextColorWait: string;
        descriptionTextColorFinish: string;
        descriptionTextColorError: string;
        stepHeaderFontSizeSmall: string;
        stepHeaderFontSizeMedium: string;
        indicatorIndexFontSizeSmall: string;
        indicatorIndexFontSizeMedium: string;
        indicatorSizeSmall: string;
        indicatorSizeMedium: string;
        indicatorIconSizeSmall: string;
        indicatorIconSizeMedium: string;
    }, any>>;
    themeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Steps", {
        stepHeaderFontWeight: string;
        indicatorTextColorProcess: string;
        indicatorTextColorWait: string;
        indicatorTextColorFinish: string;
        indicatorTextColorError: string;
        indicatorBorderColorProcess: string;
        indicatorBorderColorWait: string;
        indicatorBorderColorFinish: string;
        indicatorBorderColorError: string;
        indicatorColorProcess: string;
        indicatorColorWait: string;
        indicatorColorFinish: string;
        indicatorColorError: string;
        splitorColorProcess: string;
        splitorColorWait: string;
        splitorColorFinish: string;
        splitorColorError: string;
        headerTextColorProcess: string;
        headerTextColorWait: string;
        headerTextColorFinish: string;
        headerTextColorError: string;
        descriptionTextColorProcess: string;
        descriptionTextColorWait: string;
        descriptionTextColorFinish: string;
        descriptionTextColorError: string;
        stepHeaderFontSizeSmall: string;
        stepHeaderFontSizeMedium: string;
        indicatorIndexFontSizeSmall: string;
        indicatorIndexFontSizeMedium: string;
        indicatorSizeSmall: string;
        indicatorSizeMedium: string;
        indicatorIconSizeSmall: string;
        indicatorIconSizeMedium: string;
    }, any>>>;
    builtinThemeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Steps", {
        stepHeaderFontWeight: string;
        indicatorTextColorProcess: string;
        indicatorTextColorWait: string;
        indicatorTextColorFinish: string;
        indicatorTextColorError: string;
        indicatorBorderColorProcess: string;
        indicatorBorderColorWait: string;
        indicatorBorderColorFinish: string;
        indicatorBorderColorError: string;
        indicatorColorProcess: string;
        indicatorColorWait: string;
        indicatorColorFinish: string;
        indicatorColorError: string;
        splitorColorProcess: string;
        splitorColorWait: string;
        splitorColorFinish: string;
        splitorColorError: string;
        headerTextColorProcess: string;
        headerTextColorWait: string;
        headerTextColorFinish: string;
        headerTextColorError: string;
        descriptionTextColorProcess: string;
        descriptionTextColorWait: string;
        descriptionTextColorFinish: string;
        descriptionTextColorError: string;
        stepHeaderFontSizeSmall: string;
        stepHeaderFontSizeMedium: string;
        indicatorIndexFontSizeSmall: string;
        indicatorIndexFontSizeMedium: string;
        indicatorSizeSmall: string;
        indicatorSizeMedium: string;
        indicatorIconSizeSmall: string;
        indicatorIconSizeMedium: string;
    }, any>>>;
};
export interface StepsInjection {
    props: ExtractPropTypes<typeof stepsProps>;
    mergedClsPrefixRef: Ref<string>;
    mergedThemeRef: Ref<MergedTheme<StepsTheme>>;
    stepsSlots: StepsSlots;
}
export type StepsProps = ExtractPublicPropTypes<typeof stepsProps>;
export interface StepsSlots {
    default?: () => VNode[];
    'finish-icon'?: () => VNode[];
    'error-icon'?: () => VNode[];
}
export declare const stepsInjectionKey: import("vue").InjectionKey<StepsInjection>;
declare const _default: import("vue").DefineComponent<ExtractPropTypes<{
    current: NumberConstructor;
    status: {
        type: PropType<"process" | "finish" | "error" | "wait">;
        default: string;
    };
    size: {
        type: PropType<"small" | "medium">;
        default: string;
    };
    vertical: BooleanConstructor;
    'onUpdate:current': PropType<MaybeArray<(current: number) => void>>;
    onUpdateCurrent: PropType<MaybeArray<(current: number) => void>>;
    theme: PropType<import("../../_mixins").Theme<"Steps", {
        stepHeaderFontWeight: string;
        indicatorTextColorProcess: string;
        indicatorTextColorWait: string;
        indicatorTextColorFinish: string;
        indicatorTextColorError: string;
        indicatorBorderColorProcess: string;
        indicatorBorderColorWait: string;
        indicatorBorderColorFinish: string;
        indicatorBorderColorError: string;
        indicatorColorProcess: string;
        indicatorColorWait: string;
        indicatorColorFinish: string;
        indicatorColorError: string;
        splitorColorProcess: string;
        splitorColorWait: string;
        splitorColorFinish: string;
        splitorColorError: string;
        headerTextColorProcess: string;
        headerTextColorWait: string;
        headerTextColorFinish: string;
        headerTextColorError: string;
        descriptionTextColorProcess: string;
        descriptionTextColorWait: string;
        descriptionTextColorFinish: string;
        descriptionTextColorError: string;
        stepHeaderFontSizeSmall: string;
        stepHeaderFontSizeMedium: string;
        indicatorIndexFontSizeSmall: string;
        indicatorIndexFontSizeMedium: string;
        indicatorSizeSmall: string;
        indicatorSizeMedium: string;
        indicatorIconSizeSmall: string;
        indicatorIconSizeMedium: string;
    }, any>>;
    themeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Steps", {
        stepHeaderFontWeight: string;
        indicatorTextColorProcess: string;
        indicatorTextColorWait: string;
        indicatorTextColorFinish: string;
        indicatorTextColorError: string;
        indicatorBorderColorProcess: string;
        indicatorBorderColorWait: string;
        indicatorBorderColorFinish: string;
        indicatorBorderColorError: string;
        indicatorColorProcess: string;
        indicatorColorWait: string;
        indicatorColorFinish: string;
        indicatorColorError: string;
        splitorColorProcess: string;
        splitorColorWait: string;
        splitorColorFinish: string;
        splitorColorError: string;
        headerTextColorProcess: string;
        headerTextColorWait: string;
        headerTextColorFinish: string;
        headerTextColorError: string;
        descriptionTextColorProcess: string;
        descriptionTextColorWait: string;
        descriptionTextColorFinish: string;
        descriptionTextColorError: string;
        stepHeaderFontSizeSmall: string;
        stepHeaderFontSizeMedium: string;
        indicatorIndexFontSizeSmall: string;
        indicatorIndexFontSizeMedium: string;
        indicatorSizeSmall: string;
        indicatorSizeMedium: string;
        indicatorIconSizeSmall: string;
        indicatorIconSizeMedium: string;
    }, any>>>;
    builtinThemeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Steps", {
        stepHeaderFontWeight: string;
        indicatorTextColorProcess: string;
        indicatorTextColorWait: string;
        indicatorTextColorFinish: string;
        indicatorTextColorError: string;
        indicatorBorderColorProcess: string;
        indicatorBorderColorWait: string;
        indicatorBorderColorFinish: string;
        indicatorBorderColorError: string;
        indicatorColorProcess: string;
        indicatorColorWait: string;
        indicatorColorFinish: string;
        indicatorColorError: string;
        splitorColorProcess: string;
        splitorColorWait: string;
        splitorColorFinish: string;
        splitorColorError: string;
        headerTextColorProcess: string;
        headerTextColorWait: string;
        headerTextColorFinish: string;
        headerTextColorError: string;
        descriptionTextColorProcess: string;
        descriptionTextColorWait: string;
        descriptionTextColorFinish: string;
        descriptionTextColorError: string;
        stepHeaderFontSizeSmall: string;
        stepHeaderFontSizeMedium: string;
        indicatorIndexFontSizeSmall: string;
        indicatorIndexFontSizeMedium: string;
        indicatorSizeSmall: string;
        indicatorSizeMedium: string;
        indicatorIconSizeSmall: string;
        indicatorIconSizeMedium: string;
    }, any>>>;
}>, {
    mergedClsPrefix: Ref<string, string>;
    rtlEnabled: Ref<import("../../config-provider/src/internal-interface").RtlItem | undefined, import("../../config-provider/src/internal-interface").RtlItem | undefined> | undefined;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<ExtractPropTypes<{
    current: NumberConstructor;
    status: {
        type: PropType<"process" | "finish" | "error" | "wait">;
        default: string;
    };
    size: {
        type: PropType<"small" | "medium">;
        default: string;
    };
    vertical: BooleanConstructor;
    'onUpdate:current': PropType<MaybeArray<(current: number) => void>>;
    onUpdateCurrent: PropType<MaybeArray<(current: number) => void>>;
    theme: PropType<import("../../_mixins").Theme<"Steps", {
        stepHeaderFontWeight: string;
        indicatorTextColorProcess: string;
        indicatorTextColorWait: string;
        indicatorTextColorFinish: string;
        indicatorTextColorError: string;
        indicatorBorderColorProcess: string;
        indicatorBorderColorWait: string;
        indicatorBorderColorFinish: string;
        indicatorBorderColorError: string;
        indicatorColorProcess: string;
        indicatorColorWait: string;
        indicatorColorFinish: string;
        indicatorColorError: string;
        splitorColorProcess: string;
        splitorColorWait: string;
        splitorColorFinish: string;
        splitorColorError: string;
        headerTextColorProcess: string;
        headerTextColorWait: string;
        headerTextColorFinish: string;
        headerTextColorError: string;
        descriptionTextColorProcess: string;
        descriptionTextColorWait: string;
        descriptionTextColorFinish: string;
        descriptionTextColorError: string;
        stepHeaderFontSizeSmall: string;
        stepHeaderFontSizeMedium: string;
        indicatorIndexFontSizeSmall: string;
        indicatorIndexFontSizeMedium: string;
        indicatorSizeSmall: string;
        indicatorSizeMedium: string;
        indicatorIconSizeSmall: string;
        indicatorIconSizeMedium: string;
    }, any>>;
    themeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Steps", {
        stepHeaderFontWeight: string;
        indicatorTextColorProcess: string;
        indicatorTextColorWait: string;
        indicatorTextColorFinish: string;
        indicatorTextColorError: string;
        indicatorBorderColorProcess: string;
        indicatorBorderColorWait: string;
        indicatorBorderColorFinish: string;
        indicatorBorderColorError: string;
        indicatorColorProcess: string;
        indicatorColorWait: string;
        indicatorColorFinish: string;
        indicatorColorError: string;
        splitorColorProcess: string;
        splitorColorWait: string;
        splitorColorFinish: string;
        splitorColorError: string;
        headerTextColorProcess: string;
        headerTextColorWait: string;
        headerTextColorFinish: string;
        headerTextColorError: string;
        descriptionTextColorProcess: string;
        descriptionTextColorWait: string;
        descriptionTextColorFinish: string;
        descriptionTextColorError: string;
        stepHeaderFontSizeSmall: string;
        stepHeaderFontSizeMedium: string;
        indicatorIndexFontSizeSmall: string;
        indicatorIndexFontSizeMedium: string;
        indicatorSizeSmall: string;
        indicatorSizeMedium: string;
        indicatorIconSizeSmall: string;
        indicatorIconSizeMedium: string;
    }, any>>>;
    builtinThemeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Steps", {
        stepHeaderFontWeight: string;
        indicatorTextColorProcess: string;
        indicatorTextColorWait: string;
        indicatorTextColorFinish: string;
        indicatorTextColorError: string;
        indicatorBorderColorProcess: string;
        indicatorBorderColorWait: string;
        indicatorBorderColorFinish: string;
        indicatorBorderColorError: string;
        indicatorColorProcess: string;
        indicatorColorWait: string;
        indicatorColorFinish: string;
        indicatorColorError: string;
        splitorColorProcess: string;
        splitorColorWait: string;
        splitorColorFinish: string;
        splitorColorError: string;
        headerTextColorProcess: string;
        headerTextColorWait: string;
        headerTextColorFinish: string;
        headerTextColorError: string;
        descriptionTextColorProcess: string;
        descriptionTextColorWait: string;
        descriptionTextColorFinish: string;
        descriptionTextColorError: string;
        stepHeaderFontSizeSmall: string;
        stepHeaderFontSizeMedium: string;
        indicatorIndexFontSizeSmall: string;
        indicatorIndexFontSizeMedium: string;
        indicatorSizeSmall: string;
        indicatorSizeMedium: string;
        indicatorIconSizeSmall: string;
        indicatorIconSizeMedium: string;
    }, any>>>;
}>> & Readonly<{}>, {
    size: "small" | "medium";
    status: "error" | "wait" | "finish" | "process";
    vertical: boolean;
}, SlotsType<StepsSlots>, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;

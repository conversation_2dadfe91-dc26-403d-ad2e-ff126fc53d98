<template>
  <div class="timeline">
    <div class="timeline-header">
      <h3>时间轴</h3>
      <div class="timeline-controls">
        <button @click="zoomIn" class="zoom-btn">🔍+</button>
        <button @click="zoomOut" class="zoom-btn">🔍-</button>
      </div>
    </div>
    
    <!-- 时间标尺 -->
    <div class="time-ruler">
      <div 
        v-for="(mark, index) in timeMarks" 
        :key="index"
        class="time-mark"
        :style="{ left: mark.position + 'px' }"
      >
        <div class="time-tick"></div>
        <div class="time-label">{{ formatTime(mark.time) }}</div>
      </div>
    </div>
    
    <!-- 轨道区域 -->
    <div class="tracks-container">
      <!-- 视频轨道 -->
      <div 
        class="track video-track"
        @drop="handleTrackDrop"
        @dragover.prevent
        @dragenter.prevent
      >
        <div class="track-header">
          <span class="track-label">视频</span>
        </div>
        
        <div class="track-content" ref="trackContent">
          <!-- 时间轴片段 -->
          <div
            v-for="clip in videoTrack?.clips || []"
            :key="clip.id"
            class="timeline-clip"
            :class="{ selected: selectedClipId === clip.id }"
            :style="getClipStyle(clip)"
            @click="selectClip(clip.id)"
            @mousedown="startDragClip($event, clip)"
          >
            <div class="clip-content">
              <div class="clip-name">{{ getMediaName(clip.mediaId) }}</div>
              <div class="clip-duration">{{ formatTime(clip.duration) }}</div>
            </div>
          </div>
          
          <!-- 拖拽预览 -->
          <div
            v-if="dragPreview.visible"
            class="drag-preview"
            :style="getDragPreviewStyle()"
          >
            <div class="clip-content">
              <div class="clip-name">{{ dragPreview.mediaName }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 播放头 -->
    <div 
      class="playhead"
      :style="{ left: playheadPosition + 'px' }"
      @mousedown="startDragPlayhead"
    >
      <div class="playhead-line"></div>
      <div class="playhead-handle"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useEditorStore } from '@/stores/editor'
import { storeToRefs } from 'pinia'
import type { TimelineClip, MediaItem } from '@/types'

const editorStore = useEditorStore()
const { 
  videoTrack, 
  currentTime, 
  timelineDuration, 
  zoom, 
  selectedClipId,
  mediaLibrary 
} = storeToRefs(editorStore)

const trackContent = ref<HTMLElement>()

// 拖拽相关状态
const dragPreview = ref({
  visible: false,
  x: 0,
  mediaName: '',
  duration: 0
})

const isDraggingClip = ref(false)
const dragOffset = ref(0)

// 时间轴配置
const PIXELS_PER_SECOND = 50
const TRACK_HEIGHT = 60

// 计算时间标记
const timeMarks = computed(() => {
  const marks = []
  const duration = timelineDuration.value
  const step = zoom.value < 0.5 ? 10 : zoom.value < 1 ? 5 : 1
  
  for (let time = 0; time <= duration; time += step) {
    marks.push({
      time,
      position: time * PIXELS_PER_SECOND * zoom.value
    })
  }
  
  return marks
})

// 播放头位置
const playheadPosition = computed(() => {
  return currentTime.value * PIXELS_PER_SECOND * zoom.value
})

// 格式化时间显示
const formatTime = (seconds: number): string => {
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

// 获取媒体名称
const getMediaName = (mediaId: string): string => {
  const media = mediaLibrary.value.find(m => m.id === mediaId)
  return media?.name || 'Unknown'
}

// 获取片段样式
const getClipStyle = (clip: TimelineClip) => {
  return {
    left: clip.startTime * PIXELS_PER_SECOND * zoom.value + 'px',
    width: clip.duration * PIXELS_PER_SECOND * zoom.value + 'px'
  }
}

// 获取拖拽预览样式
const getDragPreviewStyle = () => {
  return {
    left: dragPreview.value.x + 'px',
    width: dragPreview.value.duration * PIXELS_PER_SECOND * zoom.value + 'px'
  }
}

// 处理轨道拖拽放置
const handleTrackDrop = (event: DragEvent) => {
  event.preventDefault()
  dragPreview.value.visible = false
  
  try {
    const data = JSON.parse(event.dataTransfer?.getData('application/json') || '{}')
    
    if (data.type === 'media') {
      const rect = trackContent.value?.getBoundingClientRect()
      if (!rect) return
      
      const x = event.clientX - rect.left
      const time = Math.max(0, x / (PIXELS_PER_SECOND * zoom.value))
      
      // 创建新片段
      const clip = editorStore.createClipFromMedia(data.mediaId, time)
      editorStore.addClipToTrack('video-track-1', clip)

      // 自动将播放头移动到新片段的开始位置
      editorStore.setCurrentTime(time)
    }
  } catch (error) {
    console.error('Failed to handle drop:', error)
  }
}

// 选择片段
const selectClip = (clipId: string) => {
  editorStore.setSelectedClip(clipId)
}

// 开始拖拽片段
const startDragClip = (event: MouseEvent, clip: TimelineClip) => {
  event.preventDefault()
  isDraggingClip.value = true
  
  const rect = trackContent.value?.getBoundingClientRect()
  if (!rect) return
  
  dragOffset.value = event.clientX - rect.left - (clip.startTime * PIXELS_PER_SECOND * zoom.value)
  
  const handleMouseMove = (e: MouseEvent) => {
    if (!isDraggingClip.value) return
    
    const rect = trackContent.value?.getBoundingClientRect()
    if (!rect) return
    
    const x = e.clientX - rect.left - dragOffset.value
    const newStartTime = Math.max(0, x / (PIXELS_PER_SECOND * zoom.value))
    
    editorStore.updateClip(clip.id, { startTime: newStartTime })
  }
  
  const handleMouseUp = () => {
    isDraggingClip.value = false
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
  }
  
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
}

// 开始拖拽播放头
const startDragPlayhead = (event: MouseEvent) => {
  event.preventDefault()
  
  const handleMouseMove = (e: MouseEvent) => {
    const rect = trackContent.value?.getBoundingClientRect()
    if (!rect) return
    
    const x = e.clientX - rect.left
    const time = Math.max(0, x / (PIXELS_PER_SECOND * zoom.value))
    editorStore.setCurrentTime(time)
  }
  
  const handleMouseUp = () => {
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
  }
  
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
}

// 缩放控制
const zoomIn = () => {
  editorStore.setZoom(zoom.value * 1.2)
}

const zoomOut = () => {
  editorStore.setZoom(zoom.value / 1.2)
}

// 监听拖拽预览
const handleDragOver = (event: DragEvent) => {
  event.preventDefault()
  
  try {
    const data = JSON.parse(event.dataTransfer?.getData('application/json') || '{}')
    
    if (data.type === 'media') {
      const rect = trackContent.value?.getBoundingClientRect()
      if (!rect) return
      
      const x = event.clientX - rect.left
      const media = data.data as MediaItem
      
      dragPreview.value = {
        visible: true,
        x: Math.max(0, x),
        mediaName: media.name,
        duration: media.duration
      }
    }
  } catch (error) {
    // Ignore parsing errors during drag
  }
}

onMounted(() => {
  trackContent.value?.addEventListener('dragover', handleDragOver)
})

onUnmounted(() => {
  trackContent.value?.removeEventListener('dragover', handleDragOver)
})
</script>

<style scoped>
.timeline {
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  background-color: #1e1e1e;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #333;
}

.timeline-header h3 {
  margin: 0;
  color: #ffffff;
  font-size: 16px;
}

.timeline-controls {
  display: flex;
  gap: 8px;
}

.zoom-btn {
  background: #333;
  border: 1px solid #555;
  color: #fff;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.zoom-btn:hover {
  background: #404040;
}

.time-ruler {
  height: 30px;
  background-color: #2a2a2a;
  border-bottom: 1px solid #333;
  position: relative;
  overflow: hidden;
}

.time-mark {
  position: absolute;
  top: 0;
  height: 100%;
}

.time-tick {
  width: 1px;
  height: 8px;
  background-color: #666;
  margin-top: 4px;
}

.time-label {
  font-size: 10px;
  color: #ccc;
  margin-top: 2px;
  margin-left: 2px;
}

.tracks-container {
  flex: 1;
  overflow: auto;
}

.track {
  display: flex;
  border-bottom: 1px solid #333;
  height: 60px;
}

.track-header {
  width: 80px;
  background-color: #2a2a2a;
  display: flex;
  align-items: center;
  justify-content: center;
  border-right: 1px solid #333;
}

.track-label {
  color: #ccc;
  font-size: 12px;
}

.track-content {
  flex: 1;
  position: relative;
  background-color: #1a1a1a;
}

.timeline-clip {
  position: absolute;
  top: 8px;
  height: 44px;
  background: linear-gradient(135deg, #007acc, #005a9e);
  border-radius: 4px;
  cursor: pointer;
  border: 2px solid transparent;
  transition: border-color 0.2s ease;
}

.timeline-clip:hover {
  border-color: #00a8ff;
}

.timeline-clip.selected {
  border-color: #ffaa00;
}

.clip-content {
  padding: 4px 8px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  overflow: hidden;
}

.clip-name {
  color: #fff;
  font-size: 11px;
  font-weight: bold;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.clip-duration {
  color: #ccc;
  font-size: 9px;
}

.drag-preview {
  position: absolute;
  top: 8px;
  height: 44px;
  background: rgba(0, 122, 204, 0.5);
  border: 2px dashed #007acc;
  border-radius: 4px;
  pointer-events: none;
}

.playhead {
  position: absolute;
  top: 30px;
  bottom: 0;
  width: 2px;
  z-index: 100;
  cursor: ew-resize;
}

.playhead-line {
  width: 2px;
  height: 100%;
  background-color: #ff4444;
}

.playhead-handle {
  position: absolute;
  top: -6px;
  left: -6px;
  width: 14px;
  height: 14px;
  background-color: #ff4444;
  border-radius: 50%;
  cursor: ew-resize;
}
</style>

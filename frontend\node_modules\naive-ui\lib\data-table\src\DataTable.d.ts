import type { CsvOptionsType, DataTableSlots, MainTableRef, RowKey } from './interface';
import { type SlotsType } from 'vue';
declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    readonly onUnstableColumnResize: import("vue").PropType<(resizedWidth: number, limitedWidth: number, column: import("./interface").TableBaseColumn, getColumnWidth: (key: import("./interface").ColumnKey) => number | undefined) => void>;
    readonly pagination: {
        readonly type: import("vue").PropType<false | import("../../pagination").PaginationProps>;
        readonly default: false;
    };
    readonly paginateSinglePage: {
        readonly type: BooleanConstructor;
        readonly default: true;
    };
    readonly minHeight: import("vue").PropType<string | number>;
    readonly maxHeight: import("vue").PropType<string | number>;
    readonly columns: {
        readonly type: import("vue").PropType<import("./interface").TableColumns<any>>;
        readonly default: () => never[];
    };
    readonly rowClassName: import("vue").PropType<string | import("./interface").CreateRowClassName<any>>;
    readonly rowProps: import("vue").PropType<import("./interface").CreateRowProps<any>>;
    readonly rowKey: import("vue").PropType<import("./interface").CreateRowKey<any>>;
    readonly summary: import("vue").PropType<import("./interface").CreateSummary<any>>;
    readonly data: {
        readonly type: import("vue").PropType<import("./interface").RowData[]>;
        readonly default: () => never[];
    };
    readonly loading: BooleanConstructor;
    readonly bordered: {
        readonly type: import("vue").PropType<boolean | undefined>;
        readonly default: undefined;
    };
    readonly bottomBordered: {
        readonly type: import("vue").PropType<boolean | undefined>;
        readonly default: undefined;
    };
    readonly striped: BooleanConstructor;
    readonly scrollX: import("vue").PropType<string | number>;
    readonly defaultCheckedRowKeys: {
        readonly type: import("vue").PropType<RowKey[]>;
        readonly default: () => never[];
    };
    readonly checkedRowKeys: import("vue").PropType<RowKey[]>;
    readonly singleLine: {
        readonly type: BooleanConstructor;
        readonly default: true;
    };
    readonly singleColumn: BooleanConstructor;
    readonly size: {
        readonly type: import("vue").PropType<"small" | "medium" | "large">;
        readonly default: "medium";
    };
    readonly remote: BooleanConstructor;
    readonly defaultExpandedRowKeys: {
        readonly type: import("vue").PropType<RowKey[]>;
        readonly default: readonly [];
    };
    readonly defaultExpandAll: BooleanConstructor;
    readonly expandedRowKeys: import("vue").PropType<RowKey[]>;
    readonly stickyExpandedRows: BooleanConstructor;
    readonly virtualScroll: BooleanConstructor;
    readonly virtualScrollX: BooleanConstructor;
    readonly virtualScrollHeader: BooleanConstructor;
    readonly headerHeight: {
        readonly type: NumberConstructor;
        readonly default: 28;
    };
    readonly heightForRow: import("vue").PropType<import("./interface").DataTableHeightForRow>;
    readonly minRowHeight: {
        readonly type: NumberConstructor;
        readonly default: 28;
    };
    readonly tableLayout: {
        readonly type: import("vue").PropType<"auto" | "fixed">;
        readonly default: "auto";
    };
    readonly allowCheckingNotLoaded: BooleanConstructor;
    readonly cascade: {
        readonly type: BooleanConstructor;
        readonly default: true;
    };
    readonly childrenKey: {
        readonly type: StringConstructor;
        readonly default: "children";
    };
    readonly indent: {
        readonly type: NumberConstructor;
        readonly default: 16;
    };
    readonly flexHeight: BooleanConstructor;
    readonly summaryPlacement: {
        readonly type: import("vue").PropType<"top" | "bottom">;
        readonly default: "bottom";
    };
    readonly paginationBehaviorOnFilter: {
        readonly type: import("vue").PropType<"first" | "current">;
        readonly default: "current";
    };
    readonly filterIconPopoverProps: import("vue").PropType<import("../..").PopoverProps>;
    readonly scrollbarProps: import("vue").PropType<import("../..").ScrollbarProps>;
    readonly renderCell: import("vue").PropType<(value: any, rowData: object, column: import("./interface").TableBaseColumn) => import("vue").VNodeChild>;
    readonly renderExpandIcon: import("vue").PropType<import("./interface").RenderExpandIcon>;
    readonly spinProps: {
        readonly type: import("vue").PropType<import("../../_internal").BaseLoadingExposedProps>;
        readonly default: {};
    };
    readonly getCsvCell: import("vue").PropType<import("./publicTypes").DataTableGetCsvCell>;
    readonly getCsvHeader: import("vue").PropType<import("./publicTypes").DataTableGetCsvHeader>;
    readonly onLoad: import("vue").PropType<import("./interface").DataTableOnLoad>;
    readonly 'onUpdate:page': import("vue").PropType<import("../../pagination").PaginationProps["onUpdate:page"]>;
    readonly onUpdatePage: import("vue").PropType<import("../../pagination").PaginationProps["onUpdate:page"]>;
    readonly 'onUpdate:pageSize': import("vue").PropType<import("../../pagination").PaginationProps["onUpdate:pageSize"]>;
    readonly onUpdatePageSize: import("vue").PropType<import("../../pagination").PaginationProps["onUpdate:pageSize"]>;
    readonly 'onUpdate:sorter': import("vue").PropType<import("../../_utils").MaybeArray<import("./interface").OnUpdateSorter>>;
    readonly onUpdateSorter: import("vue").PropType<import("../../_utils").MaybeArray<import("./interface").OnUpdateSorter>>;
    readonly 'onUpdate:filters': import("vue").PropType<import("../../_utils").MaybeArray<import("./interface").OnUpdateFilters>>;
    readonly onUpdateFilters: import("vue").PropType<import("../../_utils").MaybeArray<import("./interface").OnUpdateFilters>>;
    readonly 'onUpdate:checkedRowKeys': import("vue").PropType<import("../../_utils").MaybeArray<import("./interface").OnUpdateCheckedRowKeys>>;
    readonly onUpdateCheckedRowKeys: import("vue").PropType<import("../../_utils").MaybeArray<import("./interface").OnUpdateCheckedRowKeys>>;
    readonly 'onUpdate:expandedRowKeys': import("vue").PropType<import("../../_utils").MaybeArray<import("./interface").OnUpdateExpandedRowKeys>>;
    readonly onUpdateExpandedRowKeys: import("vue").PropType<import("../../_utils").MaybeArray<import("./interface").OnUpdateExpandedRowKeys>>;
    readonly onScroll: import("vue").PropType<(e: Event) => void>;
    readonly onPageChange: import("vue").PropType<import("../../pagination").PaginationProps["onUpdate:page"]>;
    readonly onPageSizeChange: import("vue").PropType<import("../../pagination").PaginationProps["onUpdate:pageSize"]>;
    readonly onSorterChange: import("vue").PropType<import("../../_utils").MaybeArray<import("./interface").OnUpdateSorter> | undefined>;
    readonly onFiltersChange: import("vue").PropType<import("../../_utils").MaybeArray<import("./interface").OnUpdateFilters> | undefined>;
    readonly onCheckedRowKeysChange: import("vue").PropType<import("../../_utils").MaybeArray<import("./interface").OnUpdateCheckedRowKeys> | undefined>;
    readonly theme: import("vue").PropType<import("../../_mixins").Theme<"DataTable", {
        actionDividerColor: string;
        lineHeight: string;
        borderRadius: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        borderColor: string;
        tdColorHover: string;
        tdColorSorting: string;
        tdColorStriped: string;
        thColor: string;
        thColorHover: string;
        thColorSorting: string;
        tdColor: string;
        tdTextColor: string;
        thTextColor: string;
        thFontWeight: string;
        thButtonColorHover: string;
        thIconColor: string;
        thIconColorActive: string;
        borderColorModal: string;
        tdColorHoverModal: string;
        tdColorSortingModal: string;
        tdColorStripedModal: string;
        thColorModal: string;
        thColorHoverModal: string;
        thColorSortingModal: string;
        tdColorModal: string;
        borderColorPopover: string;
        tdColorHoverPopover: string;
        tdColorSortingPopover: string;
        tdColorStripedPopover: string;
        thColorPopover: string;
        thColorHoverPopover: string;
        thColorSortingPopover: string;
        tdColorPopover: string;
        boxShadowBefore: string;
        boxShadowAfter: string;
        loadingColor: string;
        loadingSize: string;
        opacityLoading: string;
        thPaddingSmall: string;
        thPaddingMedium: string;
        thPaddingLarge: string;
        tdPaddingSmall: string;
        tdPaddingMedium: string;
        tdPaddingLarge: string;
        sorterSize: string;
        resizableContainerSize: string;
        resizableSize: string;
        filterSize: string;
        paginationMargin: string;
        emptyPadding: string;
        actionPadding: string;
        actionButtonMargin: string;
    }, {
        Button: import("../../_mixins").Theme<"Button", {
            heightTiny: string;
            heightSmall: string;
            heightMedium: string;
            heightLarge: string;
            borderRadiusTiny: string;
            borderRadiusSmall: string;
            borderRadiusMedium: string;
            borderRadiusLarge: string;
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            opacityDisabled: string;
            colorOpacitySecondary: string;
            colorOpacitySecondaryHover: string;
            colorOpacitySecondaryPressed: string;
            colorSecondary: string;
            colorSecondaryHover: string;
            colorSecondaryPressed: string;
            colorTertiary: string;
            colorTertiaryHover: string;
            colorTertiaryPressed: string;
            colorQuaternary: string;
            colorQuaternaryHover: string;
            colorQuaternaryPressed: string;
            color: string;
            colorHover: string;
            colorPressed: string;
            colorFocus: string;
            colorDisabled: string;
            textColor: string;
            textColorTertiary: string;
            textColorHover: string;
            textColorPressed: string;
            textColorFocus: string;
            textColorDisabled: string;
            textColorText: string;
            textColorTextHover: string;
            textColorTextPressed: string;
            textColorTextFocus: string;
            textColorTextDisabled: string;
            textColorGhost: string;
            textColorGhostHover: string;
            textColorGhostPressed: string;
            textColorGhostFocus: string;
            textColorGhostDisabled: string;
            border: string;
            borderHover: string;
            borderPressed: string;
            borderFocus: string;
            borderDisabled: string;
            rippleColor: string;
            colorPrimary: string;
            colorHoverPrimary: string;
            colorPressedPrimary: string;
            colorFocusPrimary: string;
            colorDisabledPrimary: string;
            textColorPrimary: string;
            textColorHoverPrimary: string;
            textColorPressedPrimary: string;
            textColorFocusPrimary: string;
            textColorDisabledPrimary: string;
            textColorTextPrimary: string;
            textColorTextHoverPrimary: string;
            textColorTextPressedPrimary: string;
            textColorTextFocusPrimary: string;
            textColorTextDisabledPrimary: string;
            textColorGhostPrimary: string;
            textColorGhostHoverPrimary: string;
            textColorGhostPressedPrimary: string;
            textColorGhostFocusPrimary: string;
            textColorGhostDisabledPrimary: string;
            borderPrimary: string;
            borderHoverPrimary: string;
            borderPressedPrimary: string;
            borderFocusPrimary: string;
            borderDisabledPrimary: string;
            rippleColorPrimary: string;
            colorInfo: string;
            colorHoverInfo: string;
            colorPressedInfo: string;
            colorFocusInfo: string;
            colorDisabledInfo: string;
            textColorInfo: string;
            textColorHoverInfo: string;
            textColorPressedInfo: string;
            textColorFocusInfo: string;
            textColorDisabledInfo: string;
            textColorTextInfo: string;
            textColorTextHoverInfo: string;
            textColorTextPressedInfo: string;
            textColorTextFocusInfo: string;
            textColorTextDisabledInfo: string;
            textColorGhostInfo: string;
            textColorGhostHoverInfo: string;
            textColorGhostPressedInfo: string;
            textColorGhostFocusInfo: string;
            textColorGhostDisabledInfo: string;
            borderInfo: string;
            borderHoverInfo: string;
            borderPressedInfo: string;
            borderFocusInfo: string;
            borderDisabledInfo: string;
            rippleColorInfo: string;
            colorSuccess: string;
            colorHoverSuccess: string;
            colorPressedSuccess: string;
            colorFocusSuccess: string;
            colorDisabledSuccess: string;
            textColorSuccess: string;
            textColorHoverSuccess: string;
            textColorPressedSuccess: string;
            textColorFocusSuccess: string;
            textColorDisabledSuccess: string;
            textColorTextSuccess: string;
            textColorTextHoverSuccess: string;
            textColorTextPressedSuccess: string;
            textColorTextFocusSuccess: string;
            textColorTextDisabledSuccess: string;
            textColorGhostSuccess: string;
            textColorGhostHoverSuccess: string;
            textColorGhostPressedSuccess: string;
            textColorGhostFocusSuccess: string;
            textColorGhostDisabledSuccess: string;
            borderSuccess: string;
            borderHoverSuccess: string;
            borderPressedSuccess: string;
            borderFocusSuccess: string;
            borderDisabledSuccess: string;
            rippleColorSuccess: string;
            colorWarning: string;
            colorHoverWarning: string;
            colorPressedWarning: string;
            colorFocusWarning: string;
            colorDisabledWarning: string;
            textColorWarning: string;
            textColorHoverWarning: string;
            textColorPressedWarning: string;
            textColorFocusWarning: string;
            textColorDisabledWarning: string;
            textColorTextWarning: string;
            textColorTextHoverWarning: string;
            textColorTextPressedWarning: string;
            textColorTextFocusWarning: string;
            textColorTextDisabledWarning: string;
            textColorGhostWarning: string;
            textColorGhostHoverWarning: string;
            textColorGhostPressedWarning: string;
            textColorGhostFocusWarning: string;
            textColorGhostDisabledWarning: string;
            borderWarning: string;
            borderHoverWarning: string;
            borderPressedWarning: string;
            borderFocusWarning: string;
            borderDisabledWarning: string;
            rippleColorWarning: string;
            colorError: string;
            colorHoverError: string;
            colorPressedError: string;
            colorFocusError: string;
            colorDisabledError: string;
            textColorError: string;
            textColorHoverError: string;
            textColorPressedError: string;
            textColorFocusError: string;
            textColorDisabledError: string;
            textColorTextError: string;
            textColorTextHoverError: string;
            textColorTextPressedError: string;
            textColorTextFocusError: string;
            textColorTextDisabledError: string;
            textColorGhostError: string;
            textColorGhostHoverError: string;
            textColorGhostPressedError: string;
            textColorGhostFocusError: string;
            textColorGhostDisabledError: string;
            borderError: string;
            borderHoverError: string;
            borderPressedError: string;
            borderFocusError: string;
            borderDisabledError: string;
            rippleColorError: string;
            waveOpacity: string;
            fontWeight: string;
            fontWeightStrong: string;
            paddingTiny: string;
            paddingSmall: string;
            paddingMedium: string;
            paddingLarge: string;
            paddingRoundTiny: string;
            paddingRoundSmall: string;
            paddingRoundMedium: string;
            paddingRoundLarge: string;
            iconMarginTiny: string;
            iconMarginSmall: string;
            iconMarginMedium: string;
            iconMarginLarge: string;
            iconSizeTiny: string;
            iconSizeSmall: string;
            iconSizeMedium: string;
            iconSizeLarge: string;
            rippleDuration: string;
        }, any>;
        Checkbox: import("../../_mixins").Theme<"Checkbox", {
            labelLineHeight: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            borderRadius: string;
            color: string;
            colorChecked: string;
            colorDisabled: string;
            colorDisabledChecked: string;
            colorTableHeader: string;
            colorTableHeaderModal: string;
            colorTableHeaderPopover: string;
            checkMarkColor: string;
            checkMarkColorDisabled: string;
            checkMarkColorDisabledChecked: string;
            border: string;
            borderDisabled: string;
            borderDisabledChecked: string;
            borderChecked: string;
            borderFocus: string;
            boxShadowFocus: string;
            textColor: string;
            textColorDisabled: string;
            sizeSmall: string;
            sizeMedium: string;
            sizeLarge: string;
            labelPadding: string;
            labelFontWeight: string;
        }, any>;
        Radio: import("../../_mixins").Theme<"Radio", {
            labelLineHeight: string;
            buttonHeightSmall: string;
            buttonHeightMedium: string;
            buttonHeightLarge: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            boxShadow: string;
            boxShadowActive: string;
            boxShadowFocus: string;
            boxShadowHover: string;
            boxShadowDisabled: string;
            color: string;
            colorDisabled: string;
            colorActive: string;
            textColor: string;
            textColorDisabled: string;
            dotColorActive: string;
            dotColorDisabled: string;
            buttonBorderColor: string;
            buttonBorderColorActive: string;
            buttonBorderColorHover: string;
            buttonColor: string;
            buttonColorActive: string;
            buttonTextColor: string;
            buttonTextColorActive: string;
            buttonTextColorHover: string;
            opacityDisabled: string;
            buttonBoxShadowFocus: string;
            buttonBoxShadowHover: string;
            buttonBoxShadow: string;
            buttonBorderRadius: string;
            radioSizeSmall: string;
            radioSizeMedium: string;
            radioSizeLarge: string;
            labelPadding: string;
            labelFontWeight: string;
        }, any>;
        Pagination: import("../../_mixins").Theme<"Pagination", {
            buttonColor: string;
            buttonColorHover: string;
            buttonColorPressed: string;
            buttonBorder: string;
            buttonBorderHover: string;
            buttonBorderPressed: string;
            buttonIconColor: string;
            buttonIconColorHover: string;
            buttonIconColorPressed: string;
            itemTextColor: string;
            itemTextColorHover: string;
            itemTextColorPressed: string;
            itemTextColorActive: string;
            itemTextColorDisabled: string;
            itemColor: string;
            itemColorHover: string;
            itemColorPressed: string;
            itemColorActive: string;
            itemColorActiveHover: string;
            itemColorDisabled: string;
            itemBorder: string;
            itemBorderHover: string;
            itemBorderPressed: string;
            itemBorderActive: string;
            itemBorderDisabled: string;
            itemBorderRadius: string;
            itemSizeSmall: string;
            itemSizeMedium: string;
            itemSizeLarge: string;
            itemFontSizeSmall: string;
            itemFontSizeMedium: string;
            itemFontSizeLarge: string;
            jumperFontSizeSmall: string;
            jumperFontSizeMedium: string;
            jumperFontSizeLarge: string;
            jumperTextColor: string;
            jumperTextColorDisabled: string;
            itemPaddingSmall: string;
            itemMarginSmall: string;
            itemMarginSmallRtl: string;
            itemPaddingMedium: string;
            itemMarginMedium: string;
            itemMarginMediumRtl: string;
            itemPaddingLarge: string;
            itemMarginLarge: string;
            itemMarginLargeRtl: string;
            buttonIconSizeSmall: string;
            buttonIconSizeMedium: string;
            buttonIconSizeLarge: string;
            inputWidthSmall: string;
            selectWidthSmall: string;
            inputMarginSmall: string;
            inputMarginSmallRtl: string;
            selectMarginSmall: string;
            prefixMarginSmall: string;
            suffixMarginSmall: string;
            inputWidthMedium: string;
            selectWidthMedium: string;
            inputMarginMedium: string;
            inputMarginMediumRtl: string;
            selectMarginMedium: string;
            prefixMarginMedium: string;
            suffixMarginMedium: string;
            inputWidthLarge: string;
            selectWidthLarge: string;
            inputMarginLarge: string;
            inputMarginLargeRtl: string;
            selectMarginLarge: string;
            prefixMarginLarge: string;
            suffixMarginLarge: string;
        }, {
            Select: import("../../_mixins").Theme<"Select", {
                menuBoxShadow: string;
            }, {
                InternalSelection: import("../../_mixins").Theme<"InternalSelection", {
                    fontSizeTiny: string;
                    fontSizeSmall: string;
                    fontSizeMedium: string;
                    fontSizeLarge: string;
                    heightTiny: string;
                    heightSmall: string;
                    heightMedium: string;
                    heightLarge: string;
                    borderRadius: string;
                    fontWeight: string;
                    textColor: string;
                    textColorDisabled: string;
                    placeholderColor: string;
                    placeholderColorDisabled: string;
                    color: string;
                    colorDisabled: string;
                    colorActive: string;
                    border: string;
                    borderHover: string;
                    borderActive: string;
                    borderFocus: string;
                    boxShadowHover: string;
                    boxShadowActive: string;
                    boxShadowFocus: string;
                    caretColor: string;
                    arrowColor: string;
                    arrowColorDisabled: string;
                    loadingColor: string;
                    borderWarning: string;
                    borderHoverWarning: string;
                    borderActiveWarning: string;
                    borderFocusWarning: string;
                    boxShadowHoverWarning: string;
                    boxShadowActiveWarning: string;
                    boxShadowFocusWarning: string;
                    colorActiveWarning: string;
                    caretColorWarning: string;
                    borderError: string;
                    borderHoverError: string;
                    borderActiveError: string;
                    borderFocusError: string;
                    boxShadowHoverError: string;
                    boxShadowActiveError: string;
                    boxShadowFocusError: string;
                    colorActiveError: string;
                    caretColorError: string;
                    clearColor: string;
                    clearColorHover: string;
                    clearColorPressed: string;
                    paddingSingle: string;
                    paddingMultiple: string;
                    clearSize: string;
                    arrowSize: string;
                }, {
                    Popover: import("../../_mixins").Theme<"Popover", {
                        fontSize: string;
                        borderRadius: string;
                        color: string;
                        dividerColor: string;
                        textColor: string;
                        boxShadow: string;
                        space: string;
                        spaceArrow: string;
                        arrowOffset: string;
                        arrowOffsetVertical: string;
                        arrowHeight: string;
                        padding: string;
                    }, any>;
                }>;
                InternalSelectMenu: import("../../_mixins").Theme<"InternalSelectMenu", {
                    optionFontSizeTiny: string;
                    optionFontSizeSmall: string;
                    optionFontSizeMedium: string;
                    optionFontSizeLarge: string;
                    optionFontSizeHuge: string;
                    optionHeightTiny: string;
                    optionHeightSmall: string;
                    optionHeightMedium: string;
                    optionHeightLarge: string;
                    optionHeightHuge: string;
                    borderRadius: string;
                    color: string;
                    groupHeaderTextColor: string;
                    actionDividerColor: string;
                    optionTextColor: string;
                    optionTextColorPressed: string;
                    optionTextColorDisabled: string;
                    optionTextColorActive: string;
                    optionOpacityDisabled: string;
                    optionCheckColor: string;
                    optionColorPending: string;
                    optionColorActive: string;
                    optionColorActivePending: string;
                    actionTextColor: string;
                    loadingColor: string;
                    height: string;
                    paddingTiny: string;
                    paddingSmall: string;
                    paddingMedium: string;
                    paddingLarge: string;
                    paddingHuge: string;
                    optionPaddingTiny: string;
                    optionPaddingSmall: string;
                    optionPaddingMedium: string;
                    optionPaddingLarge: string;
                    optionPaddingHuge: string;
                    loadingSize: string;
                }, {
                    Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                        height: string;
                        width: string;
                        borderRadius: string;
                        color: string;
                        colorHover: string;
                        railInsetHorizontalBottom: string;
                        railInsetHorizontalTop: string;
                        railInsetVerticalRight: string;
                        railInsetVerticalLeft: string;
                        railColor: string;
                    }, any>;
                    Empty: import("../../_mixins").Theme<"Empty", {
                        fontSizeTiny: string;
                        fontSizeSmall: string;
                        fontSizeMedium: string;
                        fontSizeLarge: string;
                        fontSizeHuge: string;
                        textColor: string;
                        iconColor: string;
                        extraTextColor: string;
                        iconSizeTiny: string;
                        iconSizeSmall: string;
                        iconSizeMedium: string;
                        iconSizeLarge: string;
                        iconSizeHuge: string;
                    }, any>;
                }>;
            }>;
            Input: import("../../_mixins").Theme<"Input", {
                fontWeight: string;
                countTextColorDisabled: string;
                countTextColor: string;
                heightTiny: string;
                heightSmall: string;
                heightMedium: string;
                heightLarge: string;
                fontSizeTiny: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                lineHeight: string;
                lineHeightTextarea: string;
                borderRadius: string;
                iconSize: string;
                groupLabelColor: string;
                groupLabelTextColor: string;
                textColor: string;
                textColorDisabled: string;
                textDecorationColor: string;
                caretColor: string;
                placeholderColor: string;
                placeholderColorDisabled: string;
                color: string;
                colorDisabled: string;
                colorFocus: string;
                groupLabelBorder: string;
                border: string;
                borderHover: string;
                borderDisabled: string;
                borderFocus: string;
                boxShadowFocus: string;
                loadingColor: string;
                loadingColorWarning: string;
                borderWarning: string;
                borderHoverWarning: string;
                colorFocusWarning: string;
                borderFocusWarning: string;
                boxShadowFocusWarning: string;
                caretColorWarning: string;
                loadingColorError: string;
                borderError: string;
                borderHoverError: string;
                colorFocusError: string;
                borderFocusError: string;
                boxShadowFocusError: string;
                caretColorError: string;
                clearColor: string;
                clearColorHover: string;
                clearColorPressed: string;
                iconColor: string;
                iconColorDisabled: string;
                iconColorHover: string;
                iconColorPressed: string;
                suffixTextColor: string;
                paddingTiny: string;
                paddingSmall: string;
                paddingMedium: string;
                paddingLarge: string;
                clearSize: string;
            }, any>;
            Popselect: import("../../_mixins").Theme<"Popselect", {
                menuBoxShadow: string;
            }, {
                Popover: import("../../_mixins").Theme<"Popover", {
                    fontSize: string;
                    borderRadius: string;
                    color: string;
                    dividerColor: string;
                    textColor: string;
                    boxShadow: string;
                    space: string;
                    spaceArrow: string;
                    arrowOffset: string;
                    arrowOffsetVertical: string;
                    arrowHeight: string;
                    padding: string;
                }, any>;
                InternalSelectMenu: import("../../_mixins").Theme<"InternalSelectMenu", {
                    optionFontSizeTiny: string;
                    optionFontSizeSmall: string;
                    optionFontSizeMedium: string;
                    optionFontSizeLarge: string;
                    optionFontSizeHuge: string;
                    optionHeightTiny: string;
                    optionHeightSmall: string;
                    optionHeightMedium: string;
                    optionHeightLarge: string;
                    optionHeightHuge: string;
                    borderRadius: string;
                    color: string;
                    groupHeaderTextColor: string;
                    actionDividerColor: string;
                    optionTextColor: string;
                    optionTextColorPressed: string;
                    optionTextColorDisabled: string;
                    optionTextColorActive: string;
                    optionOpacityDisabled: string;
                    optionCheckColor: string;
                    optionColorPending: string;
                    optionColorActive: string;
                    optionColorActivePending: string;
                    actionTextColor: string;
                    loadingColor: string;
                    height: string;
                    paddingTiny: string;
                    paddingSmall: string;
                    paddingMedium: string;
                    paddingLarge: string;
                    paddingHuge: string;
                    optionPaddingTiny: string;
                    optionPaddingSmall: string;
                    optionPaddingMedium: string;
                    optionPaddingLarge: string;
                    optionPaddingHuge: string;
                    loadingSize: string;
                }, {
                    Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                        height: string;
                        width: string;
                        borderRadius: string;
                        color: string;
                        colorHover: string;
                        railInsetHorizontalBottom: string;
                        railInsetHorizontalTop: string;
                        railInsetVerticalRight: string;
                        railInsetVerticalLeft: string;
                        railColor: string;
                    }, any>;
                    Empty: import("../../_mixins").Theme<"Empty", {
                        fontSizeTiny: string;
                        fontSizeSmall: string;
                        fontSizeMedium: string;
                        fontSizeLarge: string;
                        fontSizeHuge: string;
                        textColor: string;
                        iconColor: string;
                        extraTextColor: string;
                        iconSizeTiny: string;
                        iconSizeSmall: string;
                        iconSizeMedium: string;
                        iconSizeLarge: string;
                        iconSizeHuge: string;
                    }, any>;
                }>;
            }>;
        }>;
        Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
            height: string;
            width: string;
            borderRadius: string;
            color: string;
            colorHover: string;
            railInsetHorizontalBottom: string;
            railInsetHorizontalTop: string;
            railInsetVerticalRight: string;
            railInsetVerticalLeft: string;
            railColor: string;
        }, any>;
        Empty: import("../../_mixins").Theme<"Empty", {
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            fontSizeHuge: string;
            textColor: string;
            iconColor: string;
            extraTextColor: string;
            iconSizeTiny: string;
            iconSizeSmall: string;
            iconSizeMedium: string;
            iconSizeLarge: string;
            iconSizeHuge: string;
        }, any>;
        Popover: import("../../_mixins").Theme<"Popover", {
            fontSize: string;
            borderRadius: string;
            color: string;
            dividerColor: string;
            textColor: string;
            boxShadow: string;
            space: string;
            spaceArrow: string;
            arrowOffset: string;
            arrowOffsetVertical: string;
            arrowHeight: string;
            padding: string;
        }, any>;
        Ellipsis: import("../../_mixins").Theme<"Ellipsis", unknown, {
            Tooltip: import("../../_mixins").Theme<"Tooltip", {
                borderRadius: string;
                boxShadow: string;
                color: string;
                textColor: string;
                padding: string;
            }, {
                Popover: import("../../_mixins").Theme<"Popover", {
                    fontSize: string;
                    borderRadius: string;
                    color: string;
                    dividerColor: string;
                    textColor: string;
                    boxShadow: string;
                    space: string;
                    spaceArrow: string;
                    arrowOffset: string;
                    arrowOffsetVertical: string;
                    arrowHeight: string;
                    padding: string;
                }, any>;
            }>;
        }>;
        Dropdown: import("../../_mixins").Theme<"Dropdown", {
            optionHeightSmall: string;
            optionHeightMedium: string;
            optionHeightLarge: string;
            optionHeightHuge: string;
            borderRadius: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            fontSizeHuge: string;
            optionTextColor: string;
            optionTextColorHover: string;
            optionTextColorActive: string;
            optionTextColorChildActive: string;
            color: string;
            dividerColor: string;
            suffixColor: string;
            prefixColor: string;
            optionColorHover: string;
            optionColorActive: string;
            groupHeaderTextColor: string;
            optionTextColorInverted: string;
            optionTextColorHoverInverted: string;
            optionTextColorActiveInverted: string;
            optionTextColorChildActiveInverted: string;
            colorInverted: string;
            dividerColorInverted: string;
            suffixColorInverted: string;
            prefixColorInverted: string;
            optionColorHoverInverted: string;
            optionColorActiveInverted: string;
            groupHeaderTextColorInverted: string;
            optionOpacityDisabled: string;
            padding: string;
            optionIconSizeSmall: string;
            optionIconSizeMedium: string;
            optionIconSizeLarge: string;
            optionIconSizeHuge: string;
            optionSuffixWidthSmall: string;
            optionSuffixWidthMedium: string;
            optionSuffixWidthLarge: string;
            optionSuffixWidthHuge: string;
            optionIconSuffixWidthSmall: string;
            optionIconSuffixWidthMedium: string;
            optionIconSuffixWidthLarge: string;
            optionIconSuffixWidthHuge: string;
            optionPrefixWidthSmall: string;
            optionPrefixWidthMedium: string;
            optionPrefixWidthLarge: string;
            optionPrefixWidthHuge: string;
            optionIconPrefixWidthSmall: string;
            optionIconPrefixWidthMedium: string;
            optionIconPrefixWidthLarge: string;
            optionIconPrefixWidthHuge: string;
        }, {
            Popover: import("../../_mixins").Theme<"Popover", {
                fontSize: string;
                borderRadius: string;
                color: string;
                dividerColor: string;
                textColor: string;
                boxShadow: string;
                space: string;
                spaceArrow: string;
                arrowOffset: string;
                arrowOffsetVertical: string;
                arrowHeight: string;
                padding: string;
            }, any>;
        }>;
    }>>;
    readonly themeOverrides: import("vue").PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"DataTable", {
        actionDividerColor: string;
        lineHeight: string;
        borderRadius: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        borderColor: string;
        tdColorHover: string;
        tdColorSorting: string;
        tdColorStriped: string;
        thColor: string;
        thColorHover: string;
        thColorSorting: string;
        tdColor: string;
        tdTextColor: string;
        thTextColor: string;
        thFontWeight: string;
        thButtonColorHover: string;
        thIconColor: string;
        thIconColorActive: string;
        borderColorModal: string;
        tdColorHoverModal: string;
        tdColorSortingModal: string;
        tdColorStripedModal: string;
        thColorModal: string;
        thColorHoverModal: string;
        thColorSortingModal: string;
        tdColorModal: string;
        borderColorPopover: string;
        tdColorHoverPopover: string;
        tdColorSortingPopover: string;
        tdColorStripedPopover: string;
        thColorPopover: string;
        thColorHoverPopover: string;
        thColorSortingPopover: string;
        tdColorPopover: string;
        boxShadowBefore: string;
        boxShadowAfter: string;
        loadingColor: string;
        loadingSize: string;
        opacityLoading: string;
        thPaddingSmall: string;
        thPaddingMedium: string;
        thPaddingLarge: string;
        tdPaddingSmall: string;
        tdPaddingMedium: string;
        tdPaddingLarge: string;
        sorterSize: string;
        resizableContainerSize: string;
        resizableSize: string;
        filterSize: string;
        paginationMargin: string;
        emptyPadding: string;
        actionPadding: string;
        actionButtonMargin: string;
    }, {
        Button: import("../../_mixins").Theme<"Button", {
            heightTiny: string;
            heightSmall: string;
            heightMedium: string;
            heightLarge: string;
            borderRadiusTiny: string;
            borderRadiusSmall: string;
            borderRadiusMedium: string;
            borderRadiusLarge: string;
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            opacityDisabled: string;
            colorOpacitySecondary: string;
            colorOpacitySecondaryHover: string;
            colorOpacitySecondaryPressed: string;
            colorSecondary: string;
            colorSecondaryHover: string;
            colorSecondaryPressed: string;
            colorTertiary: string;
            colorTertiaryHover: string;
            colorTertiaryPressed: string;
            colorQuaternary: string;
            colorQuaternaryHover: string;
            colorQuaternaryPressed: string;
            color: string;
            colorHover: string;
            colorPressed: string;
            colorFocus: string;
            colorDisabled: string;
            textColor: string;
            textColorTertiary: string;
            textColorHover: string;
            textColorPressed: string;
            textColorFocus: string;
            textColorDisabled: string;
            textColorText: string;
            textColorTextHover: string;
            textColorTextPressed: string;
            textColorTextFocus: string;
            textColorTextDisabled: string;
            textColorGhost: string;
            textColorGhostHover: string;
            textColorGhostPressed: string;
            textColorGhostFocus: string;
            textColorGhostDisabled: string;
            border: string;
            borderHover: string;
            borderPressed: string;
            borderFocus: string;
            borderDisabled: string;
            rippleColor: string;
            colorPrimary: string;
            colorHoverPrimary: string;
            colorPressedPrimary: string;
            colorFocusPrimary: string;
            colorDisabledPrimary: string;
            textColorPrimary: string;
            textColorHoverPrimary: string;
            textColorPressedPrimary: string;
            textColorFocusPrimary: string;
            textColorDisabledPrimary: string;
            textColorTextPrimary: string;
            textColorTextHoverPrimary: string;
            textColorTextPressedPrimary: string;
            textColorTextFocusPrimary: string;
            textColorTextDisabledPrimary: string;
            textColorGhostPrimary: string;
            textColorGhostHoverPrimary: string;
            textColorGhostPressedPrimary: string;
            textColorGhostFocusPrimary: string;
            textColorGhostDisabledPrimary: string;
            borderPrimary: string;
            borderHoverPrimary: string;
            borderPressedPrimary: string;
            borderFocusPrimary: string;
            borderDisabledPrimary: string;
            rippleColorPrimary: string;
            colorInfo: string;
            colorHoverInfo: string;
            colorPressedInfo: string;
            colorFocusInfo: string;
            colorDisabledInfo: string;
            textColorInfo: string;
            textColorHoverInfo: string;
            textColorPressedInfo: string;
            textColorFocusInfo: string;
            textColorDisabledInfo: string;
            textColorTextInfo: string;
            textColorTextHoverInfo: string;
            textColorTextPressedInfo: string;
            textColorTextFocusInfo: string;
            textColorTextDisabledInfo: string;
            textColorGhostInfo: string;
            textColorGhostHoverInfo: string;
            textColorGhostPressedInfo: string;
            textColorGhostFocusInfo: string;
            textColorGhostDisabledInfo: string;
            borderInfo: string;
            borderHoverInfo: string;
            borderPressedInfo: string;
            borderFocusInfo: string;
            borderDisabledInfo: string;
            rippleColorInfo: string;
            colorSuccess: string;
            colorHoverSuccess: string;
            colorPressedSuccess: string;
            colorFocusSuccess: string;
            colorDisabledSuccess: string;
            textColorSuccess: string;
            textColorHoverSuccess: string;
            textColorPressedSuccess: string;
            textColorFocusSuccess: string;
            textColorDisabledSuccess: string;
            textColorTextSuccess: string;
            textColorTextHoverSuccess: string;
            textColorTextPressedSuccess: string;
            textColorTextFocusSuccess: string;
            textColorTextDisabledSuccess: string;
            textColorGhostSuccess: string;
            textColorGhostHoverSuccess: string;
            textColorGhostPressedSuccess: string;
            textColorGhostFocusSuccess: string;
            textColorGhostDisabledSuccess: string;
            borderSuccess: string;
            borderHoverSuccess: string;
            borderPressedSuccess: string;
            borderFocusSuccess: string;
            borderDisabledSuccess: string;
            rippleColorSuccess: string;
            colorWarning: string;
            colorHoverWarning: string;
            colorPressedWarning: string;
            colorFocusWarning: string;
            colorDisabledWarning: string;
            textColorWarning: string;
            textColorHoverWarning: string;
            textColorPressedWarning: string;
            textColorFocusWarning: string;
            textColorDisabledWarning: string;
            textColorTextWarning: string;
            textColorTextHoverWarning: string;
            textColorTextPressedWarning: string;
            textColorTextFocusWarning: string;
            textColorTextDisabledWarning: string;
            textColorGhostWarning: string;
            textColorGhostHoverWarning: string;
            textColorGhostPressedWarning: string;
            textColorGhostFocusWarning: string;
            textColorGhostDisabledWarning: string;
            borderWarning: string;
            borderHoverWarning: string;
            borderPressedWarning: string;
            borderFocusWarning: string;
            borderDisabledWarning: string;
            rippleColorWarning: string;
            colorError: string;
            colorHoverError: string;
            colorPressedError: string;
            colorFocusError: string;
            colorDisabledError: string;
            textColorError: string;
            textColorHoverError: string;
            textColorPressedError: string;
            textColorFocusError: string;
            textColorDisabledError: string;
            textColorTextError: string;
            textColorTextHoverError: string;
            textColorTextPressedError: string;
            textColorTextFocusError: string;
            textColorTextDisabledError: string;
            textColorGhostError: string;
            textColorGhostHoverError: string;
            textColorGhostPressedError: string;
            textColorGhostFocusError: string;
            textColorGhostDisabledError: string;
            borderError: string;
            borderHoverError: string;
            borderPressedError: string;
            borderFocusError: string;
            borderDisabledError: string;
            rippleColorError: string;
            waveOpacity: string;
            fontWeight: string;
            fontWeightStrong: string;
            paddingTiny: string;
            paddingSmall: string;
            paddingMedium: string;
            paddingLarge: string;
            paddingRoundTiny: string;
            paddingRoundSmall: string;
            paddingRoundMedium: string;
            paddingRoundLarge: string;
            iconMarginTiny: string;
            iconMarginSmall: string;
            iconMarginMedium: string;
            iconMarginLarge: string;
            iconSizeTiny: string;
            iconSizeSmall: string;
            iconSizeMedium: string;
            iconSizeLarge: string;
            rippleDuration: string;
        }, any>;
        Checkbox: import("../../_mixins").Theme<"Checkbox", {
            labelLineHeight: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            borderRadius: string;
            color: string;
            colorChecked: string;
            colorDisabled: string;
            colorDisabledChecked: string;
            colorTableHeader: string;
            colorTableHeaderModal: string;
            colorTableHeaderPopover: string;
            checkMarkColor: string;
            checkMarkColorDisabled: string;
            checkMarkColorDisabledChecked: string;
            border: string;
            borderDisabled: string;
            borderDisabledChecked: string;
            borderChecked: string;
            borderFocus: string;
            boxShadowFocus: string;
            textColor: string;
            textColorDisabled: string;
            sizeSmall: string;
            sizeMedium: string;
            sizeLarge: string;
            labelPadding: string;
            labelFontWeight: string;
        }, any>;
        Radio: import("../../_mixins").Theme<"Radio", {
            labelLineHeight: string;
            buttonHeightSmall: string;
            buttonHeightMedium: string;
            buttonHeightLarge: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            boxShadow: string;
            boxShadowActive: string;
            boxShadowFocus: string;
            boxShadowHover: string;
            boxShadowDisabled: string;
            color: string;
            colorDisabled: string;
            colorActive: string;
            textColor: string;
            textColorDisabled: string;
            dotColorActive: string;
            dotColorDisabled: string;
            buttonBorderColor: string;
            buttonBorderColorActive: string;
            buttonBorderColorHover: string;
            buttonColor: string;
            buttonColorActive: string;
            buttonTextColor: string;
            buttonTextColorActive: string;
            buttonTextColorHover: string;
            opacityDisabled: string;
            buttonBoxShadowFocus: string;
            buttonBoxShadowHover: string;
            buttonBoxShadow: string;
            buttonBorderRadius: string;
            radioSizeSmall: string;
            radioSizeMedium: string;
            radioSizeLarge: string;
            labelPadding: string;
            labelFontWeight: string;
        }, any>;
        Pagination: import("../../_mixins").Theme<"Pagination", {
            buttonColor: string;
            buttonColorHover: string;
            buttonColorPressed: string;
            buttonBorder: string;
            buttonBorderHover: string;
            buttonBorderPressed: string;
            buttonIconColor: string;
            buttonIconColorHover: string;
            buttonIconColorPressed: string;
            itemTextColor: string;
            itemTextColorHover: string;
            itemTextColorPressed: string;
            itemTextColorActive: string;
            itemTextColorDisabled: string;
            itemColor: string;
            itemColorHover: string;
            itemColorPressed: string;
            itemColorActive: string;
            itemColorActiveHover: string;
            itemColorDisabled: string;
            itemBorder: string;
            itemBorderHover: string;
            itemBorderPressed: string;
            itemBorderActive: string;
            itemBorderDisabled: string;
            itemBorderRadius: string;
            itemSizeSmall: string;
            itemSizeMedium: string;
            itemSizeLarge: string;
            itemFontSizeSmall: string;
            itemFontSizeMedium: string;
            itemFontSizeLarge: string;
            jumperFontSizeSmall: string;
            jumperFontSizeMedium: string;
            jumperFontSizeLarge: string;
            jumperTextColor: string;
            jumperTextColorDisabled: string;
            itemPaddingSmall: string;
            itemMarginSmall: string;
            itemMarginSmallRtl: string;
            itemPaddingMedium: string;
            itemMarginMedium: string;
            itemMarginMediumRtl: string;
            itemPaddingLarge: string;
            itemMarginLarge: string;
            itemMarginLargeRtl: string;
            buttonIconSizeSmall: string;
            buttonIconSizeMedium: string;
            buttonIconSizeLarge: string;
            inputWidthSmall: string;
            selectWidthSmall: string;
            inputMarginSmall: string;
            inputMarginSmallRtl: string;
            selectMarginSmall: string;
            prefixMarginSmall: string;
            suffixMarginSmall: string;
            inputWidthMedium: string;
            selectWidthMedium: string;
            inputMarginMedium: string;
            inputMarginMediumRtl: string;
            selectMarginMedium: string;
            prefixMarginMedium: string;
            suffixMarginMedium: string;
            inputWidthLarge: string;
            selectWidthLarge: string;
            inputMarginLarge: string;
            inputMarginLargeRtl: string;
            selectMarginLarge: string;
            prefixMarginLarge: string;
            suffixMarginLarge: string;
        }, {
            Select: import("../../_mixins").Theme<"Select", {
                menuBoxShadow: string;
            }, {
                InternalSelection: import("../../_mixins").Theme<"InternalSelection", {
                    fontSizeTiny: string;
                    fontSizeSmall: string;
                    fontSizeMedium: string;
                    fontSizeLarge: string;
                    heightTiny: string;
                    heightSmall: string;
                    heightMedium: string;
                    heightLarge: string;
                    borderRadius: string;
                    fontWeight: string;
                    textColor: string;
                    textColorDisabled: string;
                    placeholderColor: string;
                    placeholderColorDisabled: string;
                    color: string;
                    colorDisabled: string;
                    colorActive: string;
                    border: string;
                    borderHover: string;
                    borderActive: string;
                    borderFocus: string;
                    boxShadowHover: string;
                    boxShadowActive: string;
                    boxShadowFocus: string;
                    caretColor: string;
                    arrowColor: string;
                    arrowColorDisabled: string;
                    loadingColor: string;
                    borderWarning: string;
                    borderHoverWarning: string;
                    borderActiveWarning: string;
                    borderFocusWarning: string;
                    boxShadowHoverWarning: string;
                    boxShadowActiveWarning: string;
                    boxShadowFocusWarning: string;
                    colorActiveWarning: string;
                    caretColorWarning: string;
                    borderError: string;
                    borderHoverError: string;
                    borderActiveError: string;
                    borderFocusError: string;
                    boxShadowHoverError: string;
                    boxShadowActiveError: string;
                    boxShadowFocusError: string;
                    colorActiveError: string;
                    caretColorError: string;
                    clearColor: string;
                    clearColorHover: string;
                    clearColorPressed: string;
                    paddingSingle: string;
                    paddingMultiple: string;
                    clearSize: string;
                    arrowSize: string;
                }, {
                    Popover: import("../../_mixins").Theme<"Popover", {
                        fontSize: string;
                        borderRadius: string;
                        color: string;
                        dividerColor: string;
                        textColor: string;
                        boxShadow: string;
                        space: string;
                        spaceArrow: string;
                        arrowOffset: string;
                        arrowOffsetVertical: string;
                        arrowHeight: string;
                        padding: string;
                    }, any>;
                }>;
                InternalSelectMenu: import("../../_mixins").Theme<"InternalSelectMenu", {
                    optionFontSizeTiny: string;
                    optionFontSizeSmall: string;
                    optionFontSizeMedium: string;
                    optionFontSizeLarge: string;
                    optionFontSizeHuge: string;
                    optionHeightTiny: string;
                    optionHeightSmall: string;
                    optionHeightMedium: string;
                    optionHeightLarge: string;
                    optionHeightHuge: string;
                    borderRadius: string;
                    color: string;
                    groupHeaderTextColor: string;
                    actionDividerColor: string;
                    optionTextColor: string;
                    optionTextColorPressed: string;
                    optionTextColorDisabled: string;
                    optionTextColorActive: string;
                    optionOpacityDisabled: string;
                    optionCheckColor: string;
                    optionColorPending: string;
                    optionColorActive: string;
                    optionColorActivePending: string;
                    actionTextColor: string;
                    loadingColor: string;
                    height: string;
                    paddingTiny: string;
                    paddingSmall: string;
                    paddingMedium: string;
                    paddingLarge: string;
                    paddingHuge: string;
                    optionPaddingTiny: string;
                    optionPaddingSmall: string;
                    optionPaddingMedium: string;
                    optionPaddingLarge: string;
                    optionPaddingHuge: string;
                    loadingSize: string;
                }, {
                    Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                        height: string;
                        width: string;
                        borderRadius: string;
                        color: string;
                        colorHover: string;
                        railInsetHorizontalBottom: string;
                        railInsetHorizontalTop: string;
                        railInsetVerticalRight: string;
                        railInsetVerticalLeft: string;
                        railColor: string;
                    }, any>;
                    Empty: import("../../_mixins").Theme<"Empty", {
                        fontSizeTiny: string;
                        fontSizeSmall: string;
                        fontSizeMedium: string;
                        fontSizeLarge: string;
                        fontSizeHuge: string;
                        textColor: string;
                        iconColor: string;
                        extraTextColor: string;
                        iconSizeTiny: string;
                        iconSizeSmall: string;
                        iconSizeMedium: string;
                        iconSizeLarge: string;
                        iconSizeHuge: string;
                    }, any>;
                }>;
            }>;
            Input: import("../../_mixins").Theme<"Input", {
                fontWeight: string;
                countTextColorDisabled: string;
                countTextColor: string;
                heightTiny: string;
                heightSmall: string;
                heightMedium: string;
                heightLarge: string;
                fontSizeTiny: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                lineHeight: string;
                lineHeightTextarea: string;
                borderRadius: string;
                iconSize: string;
                groupLabelColor: string;
                groupLabelTextColor: string;
                textColor: string;
                textColorDisabled: string;
                textDecorationColor: string;
                caretColor: string;
                placeholderColor: string;
                placeholderColorDisabled: string;
                color: string;
                colorDisabled: string;
                colorFocus: string;
                groupLabelBorder: string;
                border: string;
                borderHover: string;
                borderDisabled: string;
                borderFocus: string;
                boxShadowFocus: string;
                loadingColor: string;
                loadingColorWarning: string;
                borderWarning: string;
                borderHoverWarning: string;
                colorFocusWarning: string;
                borderFocusWarning: string;
                boxShadowFocusWarning: string;
                caretColorWarning: string;
                loadingColorError: string;
                borderError: string;
                borderHoverError: string;
                colorFocusError: string;
                borderFocusError: string;
                boxShadowFocusError: string;
                caretColorError: string;
                clearColor: string;
                clearColorHover: string;
                clearColorPressed: string;
                iconColor: string;
                iconColorDisabled: string;
                iconColorHover: string;
                iconColorPressed: string;
                suffixTextColor: string;
                paddingTiny: string;
                paddingSmall: string;
                paddingMedium: string;
                paddingLarge: string;
                clearSize: string;
            }, any>;
            Popselect: import("../../_mixins").Theme<"Popselect", {
                menuBoxShadow: string;
            }, {
                Popover: import("../../_mixins").Theme<"Popover", {
                    fontSize: string;
                    borderRadius: string;
                    color: string;
                    dividerColor: string;
                    textColor: string;
                    boxShadow: string;
                    space: string;
                    spaceArrow: string;
                    arrowOffset: string;
                    arrowOffsetVertical: string;
                    arrowHeight: string;
                    padding: string;
                }, any>;
                InternalSelectMenu: import("../../_mixins").Theme<"InternalSelectMenu", {
                    optionFontSizeTiny: string;
                    optionFontSizeSmall: string;
                    optionFontSizeMedium: string;
                    optionFontSizeLarge: string;
                    optionFontSizeHuge: string;
                    optionHeightTiny: string;
                    optionHeightSmall: string;
                    optionHeightMedium: string;
                    optionHeightLarge: string;
                    optionHeightHuge: string;
                    borderRadius: string;
                    color: string;
                    groupHeaderTextColor: string;
                    actionDividerColor: string;
                    optionTextColor: string;
                    optionTextColorPressed: string;
                    optionTextColorDisabled: string;
                    optionTextColorActive: string;
                    optionOpacityDisabled: string;
                    optionCheckColor: string;
                    optionColorPending: string;
                    optionColorActive: string;
                    optionColorActivePending: string;
                    actionTextColor: string;
                    loadingColor: string;
                    height: string;
                    paddingTiny: string;
                    paddingSmall: string;
                    paddingMedium: string;
                    paddingLarge: string;
                    paddingHuge: string;
                    optionPaddingTiny: string;
                    optionPaddingSmall: string;
                    optionPaddingMedium: string;
                    optionPaddingLarge: string;
                    optionPaddingHuge: string;
                    loadingSize: string;
                }, {
                    Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                        height: string;
                        width: string;
                        borderRadius: string;
                        color: string;
                        colorHover: string;
                        railInsetHorizontalBottom: string;
                        railInsetHorizontalTop: string;
                        railInsetVerticalRight: string;
                        railInsetVerticalLeft: string;
                        railColor: string;
                    }, any>;
                    Empty: import("../../_mixins").Theme<"Empty", {
                        fontSizeTiny: string;
                        fontSizeSmall: string;
                        fontSizeMedium: string;
                        fontSizeLarge: string;
                        fontSizeHuge: string;
                        textColor: string;
                        iconColor: string;
                        extraTextColor: string;
                        iconSizeTiny: string;
                        iconSizeSmall: string;
                        iconSizeMedium: string;
                        iconSizeLarge: string;
                        iconSizeHuge: string;
                    }, any>;
                }>;
            }>;
        }>;
        Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
            height: string;
            width: string;
            borderRadius: string;
            color: string;
            colorHover: string;
            railInsetHorizontalBottom: string;
            railInsetHorizontalTop: string;
            railInsetVerticalRight: string;
            railInsetVerticalLeft: string;
            railColor: string;
        }, any>;
        Empty: import("../../_mixins").Theme<"Empty", {
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            fontSizeHuge: string;
            textColor: string;
            iconColor: string;
            extraTextColor: string;
            iconSizeTiny: string;
            iconSizeSmall: string;
            iconSizeMedium: string;
            iconSizeLarge: string;
            iconSizeHuge: string;
        }, any>;
        Popover: import("../../_mixins").Theme<"Popover", {
            fontSize: string;
            borderRadius: string;
            color: string;
            dividerColor: string;
            textColor: string;
            boxShadow: string;
            space: string;
            spaceArrow: string;
            arrowOffset: string;
            arrowOffsetVertical: string;
            arrowHeight: string;
            padding: string;
        }, any>;
        Ellipsis: import("../../_mixins").Theme<"Ellipsis", unknown, {
            Tooltip: import("../../_mixins").Theme<"Tooltip", {
                borderRadius: string;
                boxShadow: string;
                color: string;
                textColor: string;
                padding: string;
            }, {
                Popover: import("../../_mixins").Theme<"Popover", {
                    fontSize: string;
                    borderRadius: string;
                    color: string;
                    dividerColor: string;
                    textColor: string;
                    boxShadow: string;
                    space: string;
                    spaceArrow: string;
                    arrowOffset: string;
                    arrowOffsetVertical: string;
                    arrowHeight: string;
                    padding: string;
                }, any>;
            }>;
        }>;
        Dropdown: import("../../_mixins").Theme<"Dropdown", {
            optionHeightSmall: string;
            optionHeightMedium: string;
            optionHeightLarge: string;
            optionHeightHuge: string;
            borderRadius: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            fontSizeHuge: string;
            optionTextColor: string;
            optionTextColorHover: string;
            optionTextColorActive: string;
            optionTextColorChildActive: string;
            color: string;
            dividerColor: string;
            suffixColor: string;
            prefixColor: string;
            optionColorHover: string;
            optionColorActive: string;
            groupHeaderTextColor: string;
            optionTextColorInverted: string;
            optionTextColorHoverInverted: string;
            optionTextColorActiveInverted: string;
            optionTextColorChildActiveInverted: string;
            colorInverted: string;
            dividerColorInverted: string;
            suffixColorInverted: string;
            prefixColorInverted: string;
            optionColorHoverInverted: string;
            optionColorActiveInverted: string;
            groupHeaderTextColorInverted: string;
            optionOpacityDisabled: string;
            padding: string;
            optionIconSizeSmall: string;
            optionIconSizeMedium: string;
            optionIconSizeLarge: string;
            optionIconSizeHuge: string;
            optionSuffixWidthSmall: string;
            optionSuffixWidthMedium: string;
            optionSuffixWidthLarge: string;
            optionSuffixWidthHuge: string;
            optionIconSuffixWidthSmall: string;
            optionIconSuffixWidthMedium: string;
            optionIconSuffixWidthLarge: string;
            optionIconSuffixWidthHuge: string;
            optionPrefixWidthSmall: string;
            optionPrefixWidthMedium: string;
            optionPrefixWidthLarge: string;
            optionPrefixWidthHuge: string;
            optionIconPrefixWidthSmall: string;
            optionIconPrefixWidthMedium: string;
            optionIconPrefixWidthLarge: string;
            optionIconPrefixWidthHuge: string;
        }, {
            Popover: import("../../_mixins").Theme<"Popover", {
                fontSize: string;
                borderRadius: string;
                color: string;
                dividerColor: string;
                textColor: string;
                boxShadow: string;
                space: string;
                spaceArrow: string;
                arrowOffset: string;
                arrowOffsetVertical: string;
                arrowHeight: string;
                padding: string;
            }, any>;
        }>;
    }>>>;
    readonly builtinThemeOverrides: import("vue").PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"DataTable", {
        actionDividerColor: string;
        lineHeight: string;
        borderRadius: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        borderColor: string;
        tdColorHover: string;
        tdColorSorting: string;
        tdColorStriped: string;
        thColor: string;
        thColorHover: string;
        thColorSorting: string;
        tdColor: string;
        tdTextColor: string;
        thTextColor: string;
        thFontWeight: string;
        thButtonColorHover: string;
        thIconColor: string;
        thIconColorActive: string;
        borderColorModal: string;
        tdColorHoverModal: string;
        tdColorSortingModal: string;
        tdColorStripedModal: string;
        thColorModal: string;
        thColorHoverModal: string;
        thColorSortingModal: string;
        tdColorModal: string;
        borderColorPopover: string;
        tdColorHoverPopover: string;
        tdColorSortingPopover: string;
        tdColorStripedPopover: string;
        thColorPopover: string;
        thColorHoverPopover: string;
        thColorSortingPopover: string;
        tdColorPopover: string;
        boxShadowBefore: string;
        boxShadowAfter: string;
        loadingColor: string;
        loadingSize: string;
        opacityLoading: string;
        thPaddingSmall: string;
        thPaddingMedium: string;
        thPaddingLarge: string;
        tdPaddingSmall: string;
        tdPaddingMedium: string;
        tdPaddingLarge: string;
        sorterSize: string;
        resizableContainerSize: string;
        resizableSize: string;
        filterSize: string;
        paginationMargin: string;
        emptyPadding: string;
        actionPadding: string;
        actionButtonMargin: string;
    }, {
        Button: import("../../_mixins").Theme<"Button", {
            heightTiny: string;
            heightSmall: string;
            heightMedium: string;
            heightLarge: string;
            borderRadiusTiny: string;
            borderRadiusSmall: string;
            borderRadiusMedium: string;
            borderRadiusLarge: string;
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            opacityDisabled: string;
            colorOpacitySecondary: string;
            colorOpacitySecondaryHover: string;
            colorOpacitySecondaryPressed: string;
            colorSecondary: string;
            colorSecondaryHover: string;
            colorSecondaryPressed: string;
            colorTertiary: string;
            colorTertiaryHover: string;
            colorTertiaryPressed: string;
            colorQuaternary: string;
            colorQuaternaryHover: string;
            colorQuaternaryPressed: string;
            color: string;
            colorHover: string;
            colorPressed: string;
            colorFocus: string;
            colorDisabled: string;
            textColor: string;
            textColorTertiary: string;
            textColorHover: string;
            textColorPressed: string;
            textColorFocus: string;
            textColorDisabled: string;
            textColorText: string;
            textColorTextHover: string;
            textColorTextPressed: string;
            textColorTextFocus: string;
            textColorTextDisabled: string;
            textColorGhost: string;
            textColorGhostHover: string;
            textColorGhostPressed: string;
            textColorGhostFocus: string;
            textColorGhostDisabled: string;
            border: string;
            borderHover: string;
            borderPressed: string;
            borderFocus: string;
            borderDisabled: string;
            rippleColor: string;
            colorPrimary: string;
            colorHoverPrimary: string;
            colorPressedPrimary: string;
            colorFocusPrimary: string;
            colorDisabledPrimary: string;
            textColorPrimary: string;
            textColorHoverPrimary: string;
            textColorPressedPrimary: string;
            textColorFocusPrimary: string;
            textColorDisabledPrimary: string;
            textColorTextPrimary: string;
            textColorTextHoverPrimary: string;
            textColorTextPressedPrimary: string;
            textColorTextFocusPrimary: string;
            textColorTextDisabledPrimary: string;
            textColorGhostPrimary: string;
            textColorGhostHoverPrimary: string;
            textColorGhostPressedPrimary: string;
            textColorGhostFocusPrimary: string;
            textColorGhostDisabledPrimary: string;
            borderPrimary: string;
            borderHoverPrimary: string;
            borderPressedPrimary: string;
            borderFocusPrimary: string;
            borderDisabledPrimary: string;
            rippleColorPrimary: string;
            colorInfo: string;
            colorHoverInfo: string;
            colorPressedInfo: string;
            colorFocusInfo: string;
            colorDisabledInfo: string;
            textColorInfo: string;
            textColorHoverInfo: string;
            textColorPressedInfo: string;
            textColorFocusInfo: string;
            textColorDisabledInfo: string;
            textColorTextInfo: string;
            textColorTextHoverInfo: string;
            textColorTextPressedInfo: string;
            textColorTextFocusInfo: string;
            textColorTextDisabledInfo: string;
            textColorGhostInfo: string;
            textColorGhostHoverInfo: string;
            textColorGhostPressedInfo: string;
            textColorGhostFocusInfo: string;
            textColorGhostDisabledInfo: string;
            borderInfo: string;
            borderHoverInfo: string;
            borderPressedInfo: string;
            borderFocusInfo: string;
            borderDisabledInfo: string;
            rippleColorInfo: string;
            colorSuccess: string;
            colorHoverSuccess: string;
            colorPressedSuccess: string;
            colorFocusSuccess: string;
            colorDisabledSuccess: string;
            textColorSuccess: string;
            textColorHoverSuccess: string;
            textColorPressedSuccess: string;
            textColorFocusSuccess: string;
            textColorDisabledSuccess: string;
            textColorTextSuccess: string;
            textColorTextHoverSuccess: string;
            textColorTextPressedSuccess: string;
            textColorTextFocusSuccess: string;
            textColorTextDisabledSuccess: string;
            textColorGhostSuccess: string;
            textColorGhostHoverSuccess: string;
            textColorGhostPressedSuccess: string;
            textColorGhostFocusSuccess: string;
            textColorGhostDisabledSuccess: string;
            borderSuccess: string;
            borderHoverSuccess: string;
            borderPressedSuccess: string;
            borderFocusSuccess: string;
            borderDisabledSuccess: string;
            rippleColorSuccess: string;
            colorWarning: string;
            colorHoverWarning: string;
            colorPressedWarning: string;
            colorFocusWarning: string;
            colorDisabledWarning: string;
            textColorWarning: string;
            textColorHoverWarning: string;
            textColorPressedWarning: string;
            textColorFocusWarning: string;
            textColorDisabledWarning: string;
            textColorTextWarning: string;
            textColorTextHoverWarning: string;
            textColorTextPressedWarning: string;
            textColorTextFocusWarning: string;
            textColorTextDisabledWarning: string;
            textColorGhostWarning: string;
            textColorGhostHoverWarning: string;
            textColorGhostPressedWarning: string;
            textColorGhostFocusWarning: string;
            textColorGhostDisabledWarning: string;
            borderWarning: string;
            borderHoverWarning: string;
            borderPressedWarning: string;
            borderFocusWarning: string;
            borderDisabledWarning: string;
            rippleColorWarning: string;
            colorError: string;
            colorHoverError: string;
            colorPressedError: string;
            colorFocusError: string;
            colorDisabledError: string;
            textColorError: string;
            textColorHoverError: string;
            textColorPressedError: string;
            textColorFocusError: string;
            textColorDisabledError: string;
            textColorTextError: string;
            textColorTextHoverError: string;
            textColorTextPressedError: string;
            textColorTextFocusError: string;
            textColorTextDisabledError: string;
            textColorGhostError: string;
            textColorGhostHoverError: string;
            textColorGhostPressedError: string;
            textColorGhostFocusError: string;
            textColorGhostDisabledError: string;
            borderError: string;
            borderHoverError: string;
            borderPressedError: string;
            borderFocusError: string;
            borderDisabledError: string;
            rippleColorError: string;
            waveOpacity: string;
            fontWeight: string;
            fontWeightStrong: string;
            paddingTiny: string;
            paddingSmall: string;
            paddingMedium: string;
            paddingLarge: string;
            paddingRoundTiny: string;
            paddingRoundSmall: string;
            paddingRoundMedium: string;
            paddingRoundLarge: string;
            iconMarginTiny: string;
            iconMarginSmall: string;
            iconMarginMedium: string;
            iconMarginLarge: string;
            iconSizeTiny: string;
            iconSizeSmall: string;
            iconSizeMedium: string;
            iconSizeLarge: string;
            rippleDuration: string;
        }, any>;
        Checkbox: import("../../_mixins").Theme<"Checkbox", {
            labelLineHeight: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            borderRadius: string;
            color: string;
            colorChecked: string;
            colorDisabled: string;
            colorDisabledChecked: string;
            colorTableHeader: string;
            colorTableHeaderModal: string;
            colorTableHeaderPopover: string;
            checkMarkColor: string;
            checkMarkColorDisabled: string;
            checkMarkColorDisabledChecked: string;
            border: string;
            borderDisabled: string;
            borderDisabledChecked: string;
            borderChecked: string;
            borderFocus: string;
            boxShadowFocus: string;
            textColor: string;
            textColorDisabled: string;
            sizeSmall: string;
            sizeMedium: string;
            sizeLarge: string;
            labelPadding: string;
            labelFontWeight: string;
        }, any>;
        Radio: import("../../_mixins").Theme<"Radio", {
            labelLineHeight: string;
            buttonHeightSmall: string;
            buttonHeightMedium: string;
            buttonHeightLarge: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            boxShadow: string;
            boxShadowActive: string;
            boxShadowFocus: string;
            boxShadowHover: string;
            boxShadowDisabled: string;
            color: string;
            colorDisabled: string;
            colorActive: string;
            textColor: string;
            textColorDisabled: string;
            dotColorActive: string;
            dotColorDisabled: string;
            buttonBorderColor: string;
            buttonBorderColorActive: string;
            buttonBorderColorHover: string;
            buttonColor: string;
            buttonColorActive: string;
            buttonTextColor: string;
            buttonTextColorActive: string;
            buttonTextColorHover: string;
            opacityDisabled: string;
            buttonBoxShadowFocus: string;
            buttonBoxShadowHover: string;
            buttonBoxShadow: string;
            buttonBorderRadius: string;
            radioSizeSmall: string;
            radioSizeMedium: string;
            radioSizeLarge: string;
            labelPadding: string;
            labelFontWeight: string;
        }, any>;
        Pagination: import("../../_mixins").Theme<"Pagination", {
            buttonColor: string;
            buttonColorHover: string;
            buttonColorPressed: string;
            buttonBorder: string;
            buttonBorderHover: string;
            buttonBorderPressed: string;
            buttonIconColor: string;
            buttonIconColorHover: string;
            buttonIconColorPressed: string;
            itemTextColor: string;
            itemTextColorHover: string;
            itemTextColorPressed: string;
            itemTextColorActive: string;
            itemTextColorDisabled: string;
            itemColor: string;
            itemColorHover: string;
            itemColorPressed: string;
            itemColorActive: string;
            itemColorActiveHover: string;
            itemColorDisabled: string;
            itemBorder: string;
            itemBorderHover: string;
            itemBorderPressed: string;
            itemBorderActive: string;
            itemBorderDisabled: string;
            itemBorderRadius: string;
            itemSizeSmall: string;
            itemSizeMedium: string;
            itemSizeLarge: string;
            itemFontSizeSmall: string;
            itemFontSizeMedium: string;
            itemFontSizeLarge: string;
            jumperFontSizeSmall: string;
            jumperFontSizeMedium: string;
            jumperFontSizeLarge: string;
            jumperTextColor: string;
            jumperTextColorDisabled: string;
            itemPaddingSmall: string;
            itemMarginSmall: string;
            itemMarginSmallRtl: string;
            itemPaddingMedium: string;
            itemMarginMedium: string;
            itemMarginMediumRtl: string;
            itemPaddingLarge: string;
            itemMarginLarge: string;
            itemMarginLargeRtl: string;
            buttonIconSizeSmall: string;
            buttonIconSizeMedium: string;
            buttonIconSizeLarge: string;
            inputWidthSmall: string;
            selectWidthSmall: string;
            inputMarginSmall: string;
            inputMarginSmallRtl: string;
            selectMarginSmall: string;
            prefixMarginSmall: string;
            suffixMarginSmall: string;
            inputWidthMedium: string;
            selectWidthMedium: string;
            inputMarginMedium: string;
            inputMarginMediumRtl: string;
            selectMarginMedium: string;
            prefixMarginMedium: string;
            suffixMarginMedium: string;
            inputWidthLarge: string;
            selectWidthLarge: string;
            inputMarginLarge: string;
            inputMarginLargeRtl: string;
            selectMarginLarge: string;
            prefixMarginLarge: string;
            suffixMarginLarge: string;
        }, {
            Select: import("../../_mixins").Theme<"Select", {
                menuBoxShadow: string;
            }, {
                InternalSelection: import("../../_mixins").Theme<"InternalSelection", {
                    fontSizeTiny: string;
                    fontSizeSmall: string;
                    fontSizeMedium: string;
                    fontSizeLarge: string;
                    heightTiny: string;
                    heightSmall: string;
                    heightMedium: string;
                    heightLarge: string;
                    borderRadius: string;
                    fontWeight: string;
                    textColor: string;
                    textColorDisabled: string;
                    placeholderColor: string;
                    placeholderColorDisabled: string;
                    color: string;
                    colorDisabled: string;
                    colorActive: string;
                    border: string;
                    borderHover: string;
                    borderActive: string;
                    borderFocus: string;
                    boxShadowHover: string;
                    boxShadowActive: string;
                    boxShadowFocus: string;
                    caretColor: string;
                    arrowColor: string;
                    arrowColorDisabled: string;
                    loadingColor: string;
                    borderWarning: string;
                    borderHoverWarning: string;
                    borderActiveWarning: string;
                    borderFocusWarning: string;
                    boxShadowHoverWarning: string;
                    boxShadowActiveWarning: string;
                    boxShadowFocusWarning: string;
                    colorActiveWarning: string;
                    caretColorWarning: string;
                    borderError: string;
                    borderHoverError: string;
                    borderActiveError: string;
                    borderFocusError: string;
                    boxShadowHoverError: string;
                    boxShadowActiveError: string;
                    boxShadowFocusError: string;
                    colorActiveError: string;
                    caretColorError: string;
                    clearColor: string;
                    clearColorHover: string;
                    clearColorPressed: string;
                    paddingSingle: string;
                    paddingMultiple: string;
                    clearSize: string;
                    arrowSize: string;
                }, {
                    Popover: import("../../_mixins").Theme<"Popover", {
                        fontSize: string;
                        borderRadius: string;
                        color: string;
                        dividerColor: string;
                        textColor: string;
                        boxShadow: string;
                        space: string;
                        spaceArrow: string;
                        arrowOffset: string;
                        arrowOffsetVertical: string;
                        arrowHeight: string;
                        padding: string;
                    }, any>;
                }>;
                InternalSelectMenu: import("../../_mixins").Theme<"InternalSelectMenu", {
                    optionFontSizeTiny: string;
                    optionFontSizeSmall: string;
                    optionFontSizeMedium: string;
                    optionFontSizeLarge: string;
                    optionFontSizeHuge: string;
                    optionHeightTiny: string;
                    optionHeightSmall: string;
                    optionHeightMedium: string;
                    optionHeightLarge: string;
                    optionHeightHuge: string;
                    borderRadius: string;
                    color: string;
                    groupHeaderTextColor: string;
                    actionDividerColor: string;
                    optionTextColor: string;
                    optionTextColorPressed: string;
                    optionTextColorDisabled: string;
                    optionTextColorActive: string;
                    optionOpacityDisabled: string;
                    optionCheckColor: string;
                    optionColorPending: string;
                    optionColorActive: string;
                    optionColorActivePending: string;
                    actionTextColor: string;
                    loadingColor: string;
                    height: string;
                    paddingTiny: string;
                    paddingSmall: string;
                    paddingMedium: string;
                    paddingLarge: string;
                    paddingHuge: string;
                    optionPaddingTiny: string;
                    optionPaddingSmall: string;
                    optionPaddingMedium: string;
                    optionPaddingLarge: string;
                    optionPaddingHuge: string;
                    loadingSize: string;
                }, {
                    Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                        height: string;
                        width: string;
                        borderRadius: string;
                        color: string;
                        colorHover: string;
                        railInsetHorizontalBottom: string;
                        railInsetHorizontalTop: string;
                        railInsetVerticalRight: string;
                        railInsetVerticalLeft: string;
                        railColor: string;
                    }, any>;
                    Empty: import("../../_mixins").Theme<"Empty", {
                        fontSizeTiny: string;
                        fontSizeSmall: string;
                        fontSizeMedium: string;
                        fontSizeLarge: string;
                        fontSizeHuge: string;
                        textColor: string;
                        iconColor: string;
                        extraTextColor: string;
                        iconSizeTiny: string;
                        iconSizeSmall: string;
                        iconSizeMedium: string;
                        iconSizeLarge: string;
                        iconSizeHuge: string;
                    }, any>;
                }>;
            }>;
            Input: import("../../_mixins").Theme<"Input", {
                fontWeight: string;
                countTextColorDisabled: string;
                countTextColor: string;
                heightTiny: string;
                heightSmall: string;
                heightMedium: string;
                heightLarge: string;
                fontSizeTiny: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                lineHeight: string;
                lineHeightTextarea: string;
                borderRadius: string;
                iconSize: string;
                groupLabelColor: string;
                groupLabelTextColor: string;
                textColor: string;
                textColorDisabled: string;
                textDecorationColor: string;
                caretColor: string;
                placeholderColor: string;
                placeholderColorDisabled: string;
                color: string;
                colorDisabled: string;
                colorFocus: string;
                groupLabelBorder: string;
                border: string;
                borderHover: string;
                borderDisabled: string;
                borderFocus: string;
                boxShadowFocus: string;
                loadingColor: string;
                loadingColorWarning: string;
                borderWarning: string;
                borderHoverWarning: string;
                colorFocusWarning: string;
                borderFocusWarning: string;
                boxShadowFocusWarning: string;
                caretColorWarning: string;
                loadingColorError: string;
                borderError: string;
                borderHoverError: string;
                colorFocusError: string;
                borderFocusError: string;
                boxShadowFocusError: string;
                caretColorError: string;
                clearColor: string;
                clearColorHover: string;
                clearColorPressed: string;
                iconColor: string;
                iconColorDisabled: string;
                iconColorHover: string;
                iconColorPressed: string;
                suffixTextColor: string;
                paddingTiny: string;
                paddingSmall: string;
                paddingMedium: string;
                paddingLarge: string;
                clearSize: string;
            }, any>;
            Popselect: import("../../_mixins").Theme<"Popselect", {
                menuBoxShadow: string;
            }, {
                Popover: import("../../_mixins").Theme<"Popover", {
                    fontSize: string;
                    borderRadius: string;
                    color: string;
                    dividerColor: string;
                    textColor: string;
                    boxShadow: string;
                    space: string;
                    spaceArrow: string;
                    arrowOffset: string;
                    arrowOffsetVertical: string;
                    arrowHeight: string;
                    padding: string;
                }, any>;
                InternalSelectMenu: import("../../_mixins").Theme<"InternalSelectMenu", {
                    optionFontSizeTiny: string;
                    optionFontSizeSmall: string;
                    optionFontSizeMedium: string;
                    optionFontSizeLarge: string;
                    optionFontSizeHuge: string;
                    optionHeightTiny: string;
                    optionHeightSmall: string;
                    optionHeightMedium: string;
                    optionHeightLarge: string;
                    optionHeightHuge: string;
                    borderRadius: string;
                    color: string;
                    groupHeaderTextColor: string;
                    actionDividerColor: string;
                    optionTextColor: string;
                    optionTextColorPressed: string;
                    optionTextColorDisabled: string;
                    optionTextColorActive: string;
                    optionOpacityDisabled: string;
                    optionCheckColor: string;
                    optionColorPending: string;
                    optionColorActive: string;
                    optionColorActivePending: string;
                    actionTextColor: string;
                    loadingColor: string;
                    height: string;
                    paddingTiny: string;
                    paddingSmall: string;
                    paddingMedium: string;
                    paddingLarge: string;
                    paddingHuge: string;
                    optionPaddingTiny: string;
                    optionPaddingSmall: string;
                    optionPaddingMedium: string;
                    optionPaddingLarge: string;
                    optionPaddingHuge: string;
                    loadingSize: string;
                }, {
                    Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                        height: string;
                        width: string;
                        borderRadius: string;
                        color: string;
                        colorHover: string;
                        railInsetHorizontalBottom: string;
                        railInsetHorizontalTop: string;
                        railInsetVerticalRight: string;
                        railInsetVerticalLeft: string;
                        railColor: string;
                    }, any>;
                    Empty: import("../../_mixins").Theme<"Empty", {
                        fontSizeTiny: string;
                        fontSizeSmall: string;
                        fontSizeMedium: string;
                        fontSizeLarge: string;
                        fontSizeHuge: string;
                        textColor: string;
                        iconColor: string;
                        extraTextColor: string;
                        iconSizeTiny: string;
                        iconSizeSmall: string;
                        iconSizeMedium: string;
                        iconSizeLarge: string;
                        iconSizeHuge: string;
                    }, any>;
                }>;
            }>;
        }>;
        Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
            height: string;
            width: string;
            borderRadius: string;
            color: string;
            colorHover: string;
            railInsetHorizontalBottom: string;
            railInsetHorizontalTop: string;
            railInsetVerticalRight: string;
            railInsetVerticalLeft: string;
            railColor: string;
        }, any>;
        Empty: import("../../_mixins").Theme<"Empty", {
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            fontSizeHuge: string;
            textColor: string;
            iconColor: string;
            extraTextColor: string;
            iconSizeTiny: string;
            iconSizeSmall: string;
            iconSizeMedium: string;
            iconSizeLarge: string;
            iconSizeHuge: string;
        }, any>;
        Popover: import("../../_mixins").Theme<"Popover", {
            fontSize: string;
            borderRadius: string;
            color: string;
            dividerColor: string;
            textColor: string;
            boxShadow: string;
            space: string;
            spaceArrow: string;
            arrowOffset: string;
            arrowOffsetVertical: string;
            arrowHeight: string;
            padding: string;
        }, any>;
        Ellipsis: import("../../_mixins").Theme<"Ellipsis", unknown, {
            Tooltip: import("../../_mixins").Theme<"Tooltip", {
                borderRadius: string;
                boxShadow: string;
                color: string;
                textColor: string;
                padding: string;
            }, {
                Popover: import("../../_mixins").Theme<"Popover", {
                    fontSize: string;
                    borderRadius: string;
                    color: string;
                    dividerColor: string;
                    textColor: string;
                    boxShadow: string;
                    space: string;
                    spaceArrow: string;
                    arrowOffset: string;
                    arrowOffsetVertical: string;
                    arrowHeight: string;
                    padding: string;
                }, any>;
            }>;
        }>;
        Dropdown: import("../../_mixins").Theme<"Dropdown", {
            optionHeightSmall: string;
            optionHeightMedium: string;
            optionHeightLarge: string;
            optionHeightHuge: string;
            borderRadius: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            fontSizeHuge: string;
            optionTextColor: string;
            optionTextColorHover: string;
            optionTextColorActive: string;
            optionTextColorChildActive: string;
            color: string;
            dividerColor: string;
            suffixColor: string;
            prefixColor: string;
            optionColorHover: string;
            optionColorActive: string;
            groupHeaderTextColor: string;
            optionTextColorInverted: string;
            optionTextColorHoverInverted: string;
            optionTextColorActiveInverted: string;
            optionTextColorChildActiveInverted: string;
            colorInverted: string;
            dividerColorInverted: string;
            suffixColorInverted: string;
            prefixColorInverted: string;
            optionColorHoverInverted: string;
            optionColorActiveInverted: string;
            groupHeaderTextColorInverted: string;
            optionOpacityDisabled: string;
            padding: string;
            optionIconSizeSmall: string;
            optionIconSizeMedium: string;
            optionIconSizeLarge: string;
            optionIconSizeHuge: string;
            optionSuffixWidthSmall: string;
            optionSuffixWidthMedium: string;
            optionSuffixWidthLarge: string;
            optionSuffixWidthHuge: string;
            optionIconSuffixWidthSmall: string;
            optionIconSuffixWidthMedium: string;
            optionIconSuffixWidthLarge: string;
            optionIconSuffixWidthHuge: string;
            optionPrefixWidthSmall: string;
            optionPrefixWidthMedium: string;
            optionPrefixWidthLarge: string;
            optionPrefixWidthHuge: string;
            optionIconPrefixWidthSmall: string;
            optionIconPrefixWidthMedium: string;
            optionIconPrefixWidthLarge: string;
            optionIconPrefixWidthHuge: string;
        }, {
            Popover: import("../../_mixins").Theme<"Popover", {
                fontSize: string;
                borderRadius: string;
                color: string;
                dividerColor: string;
                textColor: string;
                boxShadow: string;
                space: string;
                spaceArrow: string;
                arrowOffset: string;
                arrowOffsetVertical: string;
                arrowHeight: string;
                padding: string;
            }, any>;
        }>;
    }>>>;
}>, {
    filter: (filters: import("./interface").FilterState | null) => void;
    filters: (filters: import("./interface").FilterState | null) => void;
    clearFilters: () => void;
    clearSorter: () => void;
    page: (page: number) => void;
    sort: (columnKey: import("./interface").ColumnKey, order: import("./interface").SortOrder) => void;
    scrollTo: import("../../scrollbar/src/Scrollbar").ScrollTo;
    downloadCsv: (options?: CsvOptionsType) => void;
    clearFilter: () => void;
    mainTableInstRef: import("vue").Ref<{
        getHeaderElement: () => HTMLElement | null;
        getBodyElement: () => HTMLElement | null;
        scrollTo: import("../../scrollbar/src/Scrollbar").ScrollTo;
    } | null, MainTableRef | {
        getHeaderElement: () => HTMLElement | null;
        getBodyElement: () => HTMLElement | null;
        scrollTo: import("../../scrollbar/src/Scrollbar").ScrollTo;
    } | null>;
    mergedClsPrefix: import("vue").Ref<string, string>;
    rtlEnabled: import("vue").Ref<import("../../config-provider/src/internal-interface").RtlItem | undefined, import("../../config-provider/src/internal-interface").RtlItem | undefined> | undefined;
    mergedTheme: import("vue").ComputedRef<{
        common: import("../..").ThemeCommonVars;
        self: {
            actionDividerColor: string;
            lineHeight: string;
            borderRadius: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            borderColor: string;
            tdColorHover: string;
            tdColorSorting: string;
            tdColorStriped: string;
            thColor: string;
            thColorHover: string;
            thColorSorting: string;
            tdColor: string;
            tdTextColor: string;
            thTextColor: string;
            thFontWeight: string;
            thButtonColorHover: string;
            thIconColor: string;
            thIconColorActive: string;
            borderColorModal: string;
            tdColorHoverModal: string;
            tdColorSortingModal: string;
            tdColorStripedModal: string;
            thColorModal: string;
            thColorHoverModal: string;
            thColorSortingModal: string;
            tdColorModal: string;
            borderColorPopover: string;
            tdColorHoverPopover: string;
            tdColorSortingPopover: string;
            tdColorStripedPopover: string;
            thColorPopover: string;
            thColorHoverPopover: string;
            thColorSortingPopover: string;
            tdColorPopover: string;
            boxShadowBefore: string;
            boxShadowAfter: string;
            loadingColor: string;
            loadingSize: string;
            opacityLoading: string;
            thPaddingSmall: string;
            thPaddingMedium: string;
            thPaddingLarge: string;
            tdPaddingSmall: string;
            tdPaddingMedium: string;
            tdPaddingLarge: string;
            sorterSize: string;
            resizableContainerSize: string;
            resizableSize: string;
            filterSize: string;
            paginationMargin: string;
            emptyPadding: string;
            actionPadding: string;
            actionButtonMargin: string;
        };
        peers: {
            Button: import("../../_mixins").Theme<"Button", {
                heightTiny: string;
                heightSmall: string;
                heightMedium: string;
                heightLarge: string;
                borderRadiusTiny: string;
                borderRadiusSmall: string;
                borderRadiusMedium: string;
                borderRadiusLarge: string;
                fontSizeTiny: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                opacityDisabled: string;
                colorOpacitySecondary: string;
                colorOpacitySecondaryHover: string;
                colorOpacitySecondaryPressed: string;
                colorSecondary: string;
                colorSecondaryHover: string;
                colorSecondaryPressed: string;
                colorTertiary: string;
                colorTertiaryHover: string;
                colorTertiaryPressed: string;
                colorQuaternary: string;
                colorQuaternaryHover: string;
                colorQuaternaryPressed: string;
                color: string;
                colorHover: string;
                colorPressed: string;
                colorFocus: string;
                colorDisabled: string;
                textColor: string;
                textColorTertiary: string;
                textColorHover: string;
                textColorPressed: string;
                textColorFocus: string;
                textColorDisabled: string;
                textColorText: string;
                textColorTextHover: string;
                textColorTextPressed: string;
                textColorTextFocus: string;
                textColorTextDisabled: string;
                textColorGhost: string;
                textColorGhostHover: string;
                textColorGhostPressed: string;
                textColorGhostFocus: string;
                textColorGhostDisabled: string;
                border: string;
                borderHover: string;
                borderPressed: string;
                borderFocus: string;
                borderDisabled: string;
                rippleColor: string;
                colorPrimary: string;
                colorHoverPrimary: string;
                colorPressedPrimary: string;
                colorFocusPrimary: string;
                colorDisabledPrimary: string;
                textColorPrimary: string;
                textColorHoverPrimary: string;
                textColorPressedPrimary: string;
                textColorFocusPrimary: string;
                textColorDisabledPrimary: string;
                textColorTextPrimary: string;
                textColorTextHoverPrimary: string;
                textColorTextPressedPrimary: string;
                textColorTextFocusPrimary: string;
                textColorTextDisabledPrimary: string;
                textColorGhostPrimary: string;
                textColorGhostHoverPrimary: string;
                textColorGhostPressedPrimary: string;
                textColorGhostFocusPrimary: string;
                textColorGhostDisabledPrimary: string;
                borderPrimary: string;
                borderHoverPrimary: string;
                borderPressedPrimary: string;
                borderFocusPrimary: string;
                borderDisabledPrimary: string;
                rippleColorPrimary: string;
                colorInfo: string;
                colorHoverInfo: string;
                colorPressedInfo: string;
                colorFocusInfo: string;
                colorDisabledInfo: string;
                textColorInfo: string;
                textColorHoverInfo: string;
                textColorPressedInfo: string;
                textColorFocusInfo: string;
                textColorDisabledInfo: string;
                textColorTextInfo: string;
                textColorTextHoverInfo: string;
                textColorTextPressedInfo: string;
                textColorTextFocusInfo: string;
                textColorTextDisabledInfo: string;
                textColorGhostInfo: string;
                textColorGhostHoverInfo: string;
                textColorGhostPressedInfo: string;
                textColorGhostFocusInfo: string;
                textColorGhostDisabledInfo: string;
                borderInfo: string;
                borderHoverInfo: string;
                borderPressedInfo: string;
                borderFocusInfo: string;
                borderDisabledInfo: string;
                rippleColorInfo: string;
                colorSuccess: string;
                colorHoverSuccess: string;
                colorPressedSuccess: string;
                colorFocusSuccess: string;
                colorDisabledSuccess: string;
                textColorSuccess: string;
                textColorHoverSuccess: string;
                textColorPressedSuccess: string;
                textColorFocusSuccess: string;
                textColorDisabledSuccess: string;
                textColorTextSuccess: string;
                textColorTextHoverSuccess: string;
                textColorTextPressedSuccess: string;
                textColorTextFocusSuccess: string;
                textColorTextDisabledSuccess: string;
                textColorGhostSuccess: string;
                textColorGhostHoverSuccess: string;
                textColorGhostPressedSuccess: string;
                textColorGhostFocusSuccess: string;
                textColorGhostDisabledSuccess: string;
                borderSuccess: string;
                borderHoverSuccess: string;
                borderPressedSuccess: string;
                borderFocusSuccess: string;
                borderDisabledSuccess: string;
                rippleColorSuccess: string;
                colorWarning: string;
                colorHoverWarning: string;
                colorPressedWarning: string;
                colorFocusWarning: string;
                colorDisabledWarning: string;
                textColorWarning: string;
                textColorHoverWarning: string;
                textColorPressedWarning: string;
                textColorFocusWarning: string;
                textColorDisabledWarning: string;
                textColorTextWarning: string;
                textColorTextHoverWarning: string;
                textColorTextPressedWarning: string;
                textColorTextFocusWarning: string;
                textColorTextDisabledWarning: string;
                textColorGhostWarning: string;
                textColorGhostHoverWarning: string;
                textColorGhostPressedWarning: string;
                textColorGhostFocusWarning: string;
                textColorGhostDisabledWarning: string;
                borderWarning: string;
                borderHoverWarning: string;
                borderPressedWarning: string;
                borderFocusWarning: string;
                borderDisabledWarning: string;
                rippleColorWarning: string;
                colorError: string;
                colorHoverError: string;
                colorPressedError: string;
                colorFocusError: string;
                colorDisabledError: string;
                textColorError: string;
                textColorHoverError: string;
                textColorPressedError: string;
                textColorFocusError: string;
                textColorDisabledError: string;
                textColorTextError: string;
                textColorTextHoverError: string;
                textColorTextPressedError: string;
                textColorTextFocusError: string;
                textColorTextDisabledError: string;
                textColorGhostError: string;
                textColorGhostHoverError: string;
                textColorGhostPressedError: string;
                textColorGhostFocusError: string;
                textColorGhostDisabledError: string;
                borderError: string;
                borderHoverError: string;
                borderPressedError: string;
                borderFocusError: string;
                borderDisabledError: string;
                rippleColorError: string;
                waveOpacity: string;
                fontWeight: string;
                fontWeightStrong: string;
                paddingTiny: string;
                paddingSmall: string;
                paddingMedium: string;
                paddingLarge: string;
                paddingRoundTiny: string;
                paddingRoundSmall: string;
                paddingRoundMedium: string;
                paddingRoundLarge: string;
                iconMarginTiny: string;
                iconMarginSmall: string;
                iconMarginMedium: string;
                iconMarginLarge: string;
                iconSizeTiny: string;
                iconSizeSmall: string;
                iconSizeMedium: string;
                iconSizeLarge: string;
                rippleDuration: string;
            }, any>;
            Checkbox: import("../../_mixins").Theme<"Checkbox", {
                labelLineHeight: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                borderRadius: string;
                color: string;
                colorChecked: string;
                colorDisabled: string;
                colorDisabledChecked: string;
                colorTableHeader: string;
                colorTableHeaderModal: string;
                colorTableHeaderPopover: string;
                checkMarkColor: string;
                checkMarkColorDisabled: string;
                checkMarkColorDisabledChecked: string;
                border: string;
                borderDisabled: string;
                borderDisabledChecked: string;
                borderChecked: string;
                borderFocus: string;
                boxShadowFocus: string;
                textColor: string;
                textColorDisabled: string;
                sizeSmall: string;
                sizeMedium: string;
                sizeLarge: string;
                labelPadding: string;
                labelFontWeight: string;
            }, any>;
            Radio: import("../../_mixins").Theme<"Radio", {
                labelLineHeight: string;
                buttonHeightSmall: string;
                buttonHeightMedium: string;
                buttonHeightLarge: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                boxShadow: string;
                boxShadowActive: string;
                boxShadowFocus: string;
                boxShadowHover: string;
                boxShadowDisabled: string;
                color: string;
                colorDisabled: string;
                colorActive: string;
                textColor: string;
                textColorDisabled: string;
                dotColorActive: string;
                dotColorDisabled: string;
                buttonBorderColor: string;
                buttonBorderColorActive: string;
                buttonBorderColorHover: string;
                buttonColor: string;
                buttonColorActive: string;
                buttonTextColor: string;
                buttonTextColorActive: string;
                buttonTextColorHover: string;
                opacityDisabled: string;
                buttonBoxShadowFocus: string;
                buttonBoxShadowHover: string;
                buttonBoxShadow: string;
                buttonBorderRadius: string;
                radioSizeSmall: string;
                radioSizeMedium: string;
                radioSizeLarge: string;
                labelPadding: string;
                labelFontWeight: string;
            }, any>;
            Pagination: import("../../_mixins").Theme<"Pagination", {
                buttonColor: string;
                buttonColorHover: string;
                buttonColorPressed: string;
                buttonBorder: string;
                buttonBorderHover: string;
                buttonBorderPressed: string;
                buttonIconColor: string;
                buttonIconColorHover: string;
                buttonIconColorPressed: string;
                itemTextColor: string;
                itemTextColorHover: string;
                itemTextColorPressed: string;
                itemTextColorActive: string;
                itemTextColorDisabled: string;
                itemColor: string;
                itemColorHover: string;
                itemColorPressed: string;
                itemColorActive: string;
                itemColorActiveHover: string;
                itemColorDisabled: string;
                itemBorder: string;
                itemBorderHover: string;
                itemBorderPressed: string;
                itemBorderActive: string;
                itemBorderDisabled: string;
                itemBorderRadius: string;
                itemSizeSmall: string;
                itemSizeMedium: string;
                itemSizeLarge: string;
                itemFontSizeSmall: string;
                itemFontSizeMedium: string;
                itemFontSizeLarge: string;
                jumperFontSizeSmall: string;
                jumperFontSizeMedium: string;
                jumperFontSizeLarge: string;
                jumperTextColor: string;
                jumperTextColorDisabled: string;
                itemPaddingSmall: string;
                itemMarginSmall: string;
                itemMarginSmallRtl: string;
                itemPaddingMedium: string;
                itemMarginMedium: string;
                itemMarginMediumRtl: string;
                itemPaddingLarge: string;
                itemMarginLarge: string;
                itemMarginLargeRtl: string;
                buttonIconSizeSmall: string;
                buttonIconSizeMedium: string;
                buttonIconSizeLarge: string;
                inputWidthSmall: string;
                selectWidthSmall: string;
                inputMarginSmall: string;
                inputMarginSmallRtl: string;
                selectMarginSmall: string;
                prefixMarginSmall: string;
                suffixMarginSmall: string;
                inputWidthMedium: string;
                selectWidthMedium: string;
                inputMarginMedium: string;
                inputMarginMediumRtl: string;
                selectMarginMedium: string;
                prefixMarginMedium: string;
                suffixMarginMedium: string;
                inputWidthLarge: string;
                selectWidthLarge: string;
                inputMarginLarge: string;
                inputMarginLargeRtl: string;
                selectMarginLarge: string;
                prefixMarginLarge: string;
                suffixMarginLarge: string;
            }, {
                Select: import("../../_mixins").Theme<"Select", {
                    menuBoxShadow: string;
                }, {
                    InternalSelection: import("../../_mixins").Theme<"InternalSelection", {
                        fontSizeTiny: string;
                        fontSizeSmall: string;
                        fontSizeMedium: string;
                        fontSizeLarge: string;
                        heightTiny: string;
                        heightSmall: string;
                        heightMedium: string;
                        heightLarge: string;
                        borderRadius: string;
                        fontWeight: string;
                        textColor: string;
                        textColorDisabled: string;
                        placeholderColor: string;
                        placeholderColorDisabled: string;
                        color: string;
                        colorDisabled: string;
                        colorActive: string;
                        border: string;
                        borderHover: string;
                        borderActive: string;
                        borderFocus: string;
                        boxShadowHover: string;
                        boxShadowActive: string;
                        boxShadowFocus: string;
                        caretColor: string;
                        arrowColor: string;
                        arrowColorDisabled: string;
                        loadingColor: string;
                        borderWarning: string;
                        borderHoverWarning: string;
                        borderActiveWarning: string;
                        borderFocusWarning: string;
                        boxShadowHoverWarning: string;
                        boxShadowActiveWarning: string;
                        boxShadowFocusWarning: string;
                        colorActiveWarning: string;
                        caretColorWarning: string;
                        borderError: string;
                        borderHoverError: string;
                        borderActiveError: string;
                        borderFocusError: string;
                        boxShadowHoverError: string;
                        boxShadowActiveError: string;
                        boxShadowFocusError: string;
                        colorActiveError: string;
                        caretColorError: string;
                        clearColor: string;
                        clearColorHover: string;
                        clearColorPressed: string;
                        paddingSingle: string;
                        paddingMultiple: string;
                        clearSize: string;
                        arrowSize: string;
                    }, {
                        Popover: import("../../_mixins").Theme<"Popover", {
                            fontSize: string;
                            borderRadius: string;
                            color: string;
                            dividerColor: string;
                            textColor: string;
                            boxShadow: string;
                            space: string;
                            spaceArrow: string;
                            arrowOffset: string;
                            arrowOffsetVertical: string;
                            arrowHeight: string;
                            padding: string;
                        }, any>;
                    }>;
                    InternalSelectMenu: import("../../_mixins").Theme<"InternalSelectMenu", {
                        optionFontSizeTiny: string;
                        optionFontSizeSmall: string;
                        optionFontSizeMedium: string;
                        optionFontSizeLarge: string;
                        optionFontSizeHuge: string;
                        optionHeightTiny: string;
                        optionHeightSmall: string;
                        optionHeightMedium: string;
                        optionHeightLarge: string;
                        optionHeightHuge: string;
                        borderRadius: string;
                        color: string;
                        groupHeaderTextColor: string;
                        actionDividerColor: string;
                        optionTextColor: string;
                        optionTextColorPressed: string;
                        optionTextColorDisabled: string;
                        optionTextColorActive: string;
                        optionOpacityDisabled: string;
                        optionCheckColor: string;
                        optionColorPending: string;
                        optionColorActive: string;
                        optionColorActivePending: string;
                        actionTextColor: string;
                        loadingColor: string;
                        height: string;
                        paddingTiny: string;
                        paddingSmall: string;
                        paddingMedium: string;
                        paddingLarge: string;
                        paddingHuge: string;
                        optionPaddingTiny: string;
                        optionPaddingSmall: string;
                        optionPaddingMedium: string;
                        optionPaddingLarge: string;
                        optionPaddingHuge: string;
                        loadingSize: string;
                    }, {
                        Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                            height: string;
                            width: string;
                            borderRadius: string;
                            color: string;
                            colorHover: string;
                            railInsetHorizontalBottom: string;
                            railInsetHorizontalTop: string;
                            railInsetVerticalRight: string;
                            railInsetVerticalLeft: string;
                            railColor: string;
                        }, any>;
                        Empty: import("../../_mixins").Theme<"Empty", {
                            fontSizeTiny: string;
                            fontSizeSmall: string;
                            fontSizeMedium: string;
                            fontSizeLarge: string;
                            fontSizeHuge: string;
                            textColor: string;
                            iconColor: string;
                            extraTextColor: string;
                            iconSizeTiny: string;
                            iconSizeSmall: string;
                            iconSizeMedium: string;
                            iconSizeLarge: string;
                            iconSizeHuge: string;
                        }, any>;
                    }>;
                }>;
                Input: import("../../_mixins").Theme<"Input", {
                    fontWeight: string;
                    countTextColorDisabled: string;
                    countTextColor: string;
                    heightTiny: string;
                    heightSmall: string;
                    heightMedium: string;
                    heightLarge: string;
                    fontSizeTiny: string;
                    fontSizeSmall: string;
                    fontSizeMedium: string;
                    fontSizeLarge: string;
                    lineHeight: string;
                    lineHeightTextarea: string;
                    borderRadius: string;
                    iconSize: string;
                    groupLabelColor: string;
                    groupLabelTextColor: string;
                    textColor: string;
                    textColorDisabled: string;
                    textDecorationColor: string;
                    caretColor: string;
                    placeholderColor: string;
                    placeholderColorDisabled: string;
                    color: string;
                    colorDisabled: string;
                    colorFocus: string;
                    groupLabelBorder: string;
                    border: string;
                    borderHover: string;
                    borderDisabled: string;
                    borderFocus: string;
                    boxShadowFocus: string;
                    loadingColor: string;
                    loadingColorWarning: string;
                    borderWarning: string;
                    borderHoverWarning: string;
                    colorFocusWarning: string;
                    borderFocusWarning: string;
                    boxShadowFocusWarning: string;
                    caretColorWarning: string;
                    loadingColorError: string;
                    borderError: string;
                    borderHoverError: string;
                    colorFocusError: string;
                    borderFocusError: string;
                    boxShadowFocusError: string;
                    caretColorError: string;
                    clearColor: string;
                    clearColorHover: string;
                    clearColorPressed: string;
                    iconColor: string;
                    iconColorDisabled: string;
                    iconColorHover: string;
                    iconColorPressed: string;
                    suffixTextColor: string;
                    paddingTiny: string;
                    paddingSmall: string;
                    paddingMedium: string;
                    paddingLarge: string;
                    clearSize: string;
                }, any>;
                Popselect: import("../../_mixins").Theme<"Popselect", {
                    menuBoxShadow: string;
                }, {
                    Popover: import("../../_mixins").Theme<"Popover", {
                        fontSize: string;
                        borderRadius: string;
                        color: string;
                        dividerColor: string;
                        textColor: string;
                        boxShadow: string;
                        space: string;
                        spaceArrow: string;
                        arrowOffset: string;
                        arrowOffsetVertical: string;
                        arrowHeight: string;
                        padding: string;
                    }, any>;
                    InternalSelectMenu: import("../../_mixins").Theme<"InternalSelectMenu", {
                        optionFontSizeTiny: string;
                        optionFontSizeSmall: string;
                        optionFontSizeMedium: string;
                        optionFontSizeLarge: string;
                        optionFontSizeHuge: string;
                        optionHeightTiny: string;
                        optionHeightSmall: string;
                        optionHeightMedium: string;
                        optionHeightLarge: string;
                        optionHeightHuge: string;
                        borderRadius: string;
                        color: string;
                        groupHeaderTextColor: string;
                        actionDividerColor: string;
                        optionTextColor: string;
                        optionTextColorPressed: string;
                        optionTextColorDisabled: string;
                        optionTextColorActive: string;
                        optionOpacityDisabled: string;
                        optionCheckColor: string;
                        optionColorPending: string;
                        optionColorActive: string;
                        optionColorActivePending: string;
                        actionTextColor: string;
                        loadingColor: string;
                        height: string;
                        paddingTiny: string;
                        paddingSmall: string;
                        paddingMedium: string;
                        paddingLarge: string;
                        paddingHuge: string;
                        optionPaddingTiny: string;
                        optionPaddingSmall: string;
                        optionPaddingMedium: string;
                        optionPaddingLarge: string;
                        optionPaddingHuge: string;
                        loadingSize: string;
                    }, {
                        Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                            height: string;
                            width: string;
                            borderRadius: string;
                            color: string;
                            colorHover: string;
                            railInsetHorizontalBottom: string;
                            railInsetHorizontalTop: string;
                            railInsetVerticalRight: string;
                            railInsetVerticalLeft: string;
                            railColor: string;
                        }, any>;
                        Empty: import("../../_mixins").Theme<"Empty", {
                            fontSizeTiny: string;
                            fontSizeSmall: string;
                            fontSizeMedium: string;
                            fontSizeLarge: string;
                            fontSizeHuge: string;
                            textColor: string;
                            iconColor: string;
                            extraTextColor: string;
                            iconSizeTiny: string;
                            iconSizeSmall: string;
                            iconSizeMedium: string;
                            iconSizeLarge: string;
                            iconSizeHuge: string;
                        }, any>;
                    }>;
                }>;
            }>;
            Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                height: string;
                width: string;
                borderRadius: string;
                color: string;
                colorHover: string;
                railInsetHorizontalBottom: string;
                railInsetHorizontalTop: string;
                railInsetVerticalRight: string;
                railInsetVerticalLeft: string;
                railColor: string;
            }, any>;
            Empty: import("../../_mixins").Theme<"Empty", {
                fontSizeTiny: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                fontSizeHuge: string;
                textColor: string;
                iconColor: string;
                extraTextColor: string;
                iconSizeTiny: string;
                iconSizeSmall: string;
                iconSizeMedium: string;
                iconSizeLarge: string;
                iconSizeHuge: string;
            }, any>;
            Popover: import("../../_mixins").Theme<"Popover", {
                fontSize: string;
                borderRadius: string;
                color: string;
                dividerColor: string;
                textColor: string;
                boxShadow: string;
                space: string;
                spaceArrow: string;
                arrowOffset: string;
                arrowOffsetVertical: string;
                arrowHeight: string;
                padding: string;
            }, any>;
            Ellipsis: import("../../_mixins").Theme<"Ellipsis", unknown, {
                Tooltip: import("../../_mixins").Theme<"Tooltip", {
                    borderRadius: string;
                    boxShadow: string;
                    color: string;
                    textColor: string;
                    padding: string;
                }, {
                    Popover: import("../../_mixins").Theme<"Popover", {
                        fontSize: string;
                        borderRadius: string;
                        color: string;
                        dividerColor: string;
                        textColor: string;
                        boxShadow: string;
                        space: string;
                        spaceArrow: string;
                        arrowOffset: string;
                        arrowOffsetVertical: string;
                        arrowHeight: string;
                        padding: string;
                    }, any>;
                }>;
            }>;
            Dropdown: import("../../_mixins").Theme<"Dropdown", {
                optionHeightSmall: string;
                optionHeightMedium: string;
                optionHeightLarge: string;
                optionHeightHuge: string;
                borderRadius: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                fontSizeHuge: string;
                optionTextColor: string;
                optionTextColorHover: string;
                optionTextColorActive: string;
                optionTextColorChildActive: string;
                color: string;
                dividerColor: string;
                suffixColor: string;
                prefixColor: string;
                optionColorHover: string;
                optionColorActive: string;
                groupHeaderTextColor: string;
                optionTextColorInverted: string;
                optionTextColorHoverInverted: string;
                optionTextColorActiveInverted: string;
                optionTextColorChildActiveInverted: string;
                colorInverted: string;
                dividerColorInverted: string;
                suffixColorInverted: string;
                prefixColorInverted: string;
                optionColorHoverInverted: string;
                optionColorActiveInverted: string;
                groupHeaderTextColorInverted: string;
                optionOpacityDisabled: string;
                padding: string;
                optionIconSizeSmall: string;
                optionIconSizeMedium: string;
                optionIconSizeLarge: string;
                optionIconSizeHuge: string;
                optionSuffixWidthSmall: string;
                optionSuffixWidthMedium: string;
                optionSuffixWidthLarge: string;
                optionSuffixWidthHuge: string;
                optionIconSuffixWidthSmall: string;
                optionIconSuffixWidthMedium: string;
                optionIconSuffixWidthLarge: string;
                optionIconSuffixWidthHuge: string;
                optionPrefixWidthSmall: string;
                optionPrefixWidthMedium: string;
                optionPrefixWidthLarge: string;
                optionPrefixWidthHuge: string;
                optionIconPrefixWidthSmall: string;
                optionIconPrefixWidthMedium: string;
                optionIconPrefixWidthLarge: string;
                optionIconPrefixWidthHuge: string;
            }, {
                Popover: import("../../_mixins").Theme<"Popover", {
                    fontSize: string;
                    borderRadius: string;
                    color: string;
                    dividerColor: string;
                    textColor: string;
                    boxShadow: string;
                    space: string;
                    spaceArrow: string;
                    arrowOffset: string;
                    arrowOffsetVertical: string;
                    arrowHeight: string;
                    padding: string;
                }, any>;
            }>;
        };
        peerOverrides: {
            Button?: {
                peers?: {
                    [x: string]: any;
                } | undefined;
            } | undefined;
            Checkbox?: {
                peers?: {
                    [x: string]: any;
                } | undefined;
            } | undefined;
            Radio?: {
                peers?: {
                    [x: string]: any;
                } | undefined;
            } | undefined;
            Pagination?: {
                peers?: {
                    Select?: import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Select", {
                        menuBoxShadow: string;
                    }, {
                        InternalSelection: import("../../_mixins").Theme<"InternalSelection", {
                            fontSizeTiny: string;
                            fontSizeSmall: string;
                            fontSizeMedium: string;
                            fontSizeLarge: string;
                            heightTiny: string;
                            heightSmall: string;
                            heightMedium: string;
                            heightLarge: string;
                            borderRadius: string;
                            fontWeight: string;
                            textColor: string;
                            textColorDisabled: string;
                            placeholderColor: string;
                            placeholderColorDisabled: string;
                            color: string;
                            colorDisabled: string;
                            colorActive: string;
                            border: string;
                            borderHover: string;
                            borderActive: string;
                            borderFocus: string;
                            boxShadowHover: string;
                            boxShadowActive: string;
                            boxShadowFocus: string;
                            caretColor: string;
                            arrowColor: string;
                            arrowColorDisabled: string;
                            loadingColor: string;
                            borderWarning: string;
                            borderHoverWarning: string;
                            borderActiveWarning: string;
                            borderFocusWarning: string;
                            boxShadowHoverWarning: string;
                            boxShadowActiveWarning: string;
                            boxShadowFocusWarning: string;
                            colorActiveWarning: string;
                            caretColorWarning: string;
                            borderError: string;
                            borderHoverError: string;
                            borderActiveError: string;
                            borderFocusError: string;
                            boxShadowHoverError: string;
                            boxShadowActiveError: string;
                            boxShadowFocusError: string;
                            colorActiveError: string;
                            caretColorError: string;
                            clearColor: string;
                            clearColorHover: string;
                            clearColorPressed: string;
                            paddingSingle: string;
                            paddingMultiple: string;
                            clearSize: string;
                            arrowSize: string;
                        }, {
                            Popover: import("../../_mixins").Theme<"Popover", {
                                fontSize: string;
                                borderRadius: string;
                                color: string;
                                dividerColor: string;
                                textColor: string;
                                boxShadow: string;
                                space: string;
                                spaceArrow: string;
                                arrowOffset: string;
                                arrowOffsetVertical: string;
                                arrowHeight: string;
                                padding: string;
                            }, any>;
                        }>;
                        InternalSelectMenu: import("../../_mixins").Theme<"InternalSelectMenu", {
                            optionFontSizeTiny: string;
                            optionFontSizeSmall: string;
                            optionFontSizeMedium: string;
                            optionFontSizeLarge: string;
                            optionFontSizeHuge: string;
                            optionHeightTiny: string;
                            optionHeightSmall: string;
                            optionHeightMedium: string;
                            optionHeightLarge: string;
                            optionHeightHuge: string;
                            borderRadius: string;
                            color: string;
                            groupHeaderTextColor: string;
                            actionDividerColor: string;
                            optionTextColor: string;
                            optionTextColorPressed: string;
                            optionTextColorDisabled: string;
                            optionTextColorActive: string;
                            optionOpacityDisabled: string;
                            optionCheckColor: string;
                            optionColorPending: string;
                            optionColorActive: string;
                            optionColorActivePending: string;
                            actionTextColor: string;
                            loadingColor: string;
                            height: string;
                            paddingTiny: string;
                            paddingSmall: string;
                            paddingMedium: string;
                            paddingLarge: string;
                            paddingHuge: string;
                            optionPaddingTiny: string;
                            optionPaddingSmall: string;
                            optionPaddingMedium: string;
                            optionPaddingLarge: string;
                            optionPaddingHuge: string;
                            loadingSize: string;
                        }, {
                            Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                                height: string;
                                width: string;
                                borderRadius: string;
                                color: string;
                                colorHover: string;
                                railInsetHorizontalBottom: string;
                                railInsetHorizontalTop: string;
                                railInsetVerticalRight: string;
                                railInsetVerticalLeft: string;
                                railColor: string;
                            }, any>;
                            Empty: import("../../_mixins").Theme<"Empty", {
                                fontSizeTiny: string;
                                fontSizeSmall: string;
                                fontSizeMedium: string;
                                fontSizeLarge: string;
                                fontSizeHuge: string;
                                textColor: string;
                                iconColor: string;
                                extraTextColor: string;
                                iconSizeTiny: string;
                                iconSizeSmall: string;
                                iconSizeMedium: string;
                                iconSizeLarge: string;
                                iconSizeHuge: string;
                            }, any>;
                        }>;
                    }>> | undefined;
                    Input?: import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Input", {
                        fontWeight: string;
                        countTextColorDisabled: string;
                        countTextColor: string;
                        heightTiny: string;
                        heightSmall: string;
                        heightMedium: string;
                        heightLarge: string;
                        fontSizeTiny: string;
                        fontSizeSmall: string;
                        fontSizeMedium: string;
                        fontSizeLarge: string;
                        lineHeight: string;
                        lineHeightTextarea: string;
                        borderRadius: string;
                        iconSize: string;
                        groupLabelColor: string;
                        groupLabelTextColor: string;
                        textColor: string;
                        textColorDisabled: string;
                        textDecorationColor: string;
                        caretColor: string;
                        placeholderColor: string;
                        placeholderColorDisabled: string;
                        color: string;
                        colorDisabled: string;
                        colorFocus: string;
                        groupLabelBorder: string;
                        border: string;
                        borderHover: string;
                        borderDisabled: string;
                        borderFocus: string;
                        boxShadowFocus: string;
                        loadingColor: string;
                        loadingColorWarning: string;
                        borderWarning: string;
                        borderHoverWarning: string;
                        colorFocusWarning: string;
                        borderFocusWarning: string;
                        boxShadowFocusWarning: string;
                        caretColorWarning: string;
                        loadingColorError: string;
                        borderError: string;
                        borderHoverError: string;
                        colorFocusError: string;
                        borderFocusError: string;
                        boxShadowFocusError: string;
                        caretColorError: string;
                        clearColor: string;
                        clearColorHover: string;
                        clearColorPressed: string;
                        iconColor: string;
                        iconColorDisabled: string;
                        iconColorHover: string;
                        iconColorPressed: string;
                        suffixTextColor: string;
                        paddingTiny: string;
                        paddingSmall: string;
                        paddingMedium: string;
                        paddingLarge: string;
                        clearSize: string;
                    }, any>> | undefined;
                    Popselect?: import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Popselect", {
                        menuBoxShadow: string;
                    }, {
                        Popover: import("../../_mixins").Theme<"Popover", {
                            fontSize: string;
                            borderRadius: string;
                            color: string;
                            dividerColor: string;
                            textColor: string;
                            boxShadow: string;
                            space: string;
                            spaceArrow: string;
                            arrowOffset: string;
                            arrowOffsetVertical: string;
                            arrowHeight: string;
                            padding: string;
                        }, any>;
                        InternalSelectMenu: import("../../_mixins").Theme<"InternalSelectMenu", {
                            optionFontSizeTiny: string;
                            optionFontSizeSmall: string;
                            optionFontSizeMedium: string;
                            optionFontSizeLarge: string;
                            optionFontSizeHuge: string;
                            optionHeightTiny: string;
                            optionHeightSmall: string;
                            optionHeightMedium: string;
                            optionHeightLarge: string;
                            optionHeightHuge: string;
                            borderRadius: string;
                            color: string;
                            groupHeaderTextColor: string;
                            actionDividerColor: string;
                            optionTextColor: string;
                            optionTextColorPressed: string;
                            optionTextColorDisabled: string;
                            optionTextColorActive: string;
                            optionOpacityDisabled: string;
                            optionCheckColor: string;
                            optionColorPending: string;
                            optionColorActive: string;
                            optionColorActivePending: string;
                            actionTextColor: string;
                            loadingColor: string;
                            height: string;
                            paddingTiny: string;
                            paddingSmall: string;
                            paddingMedium: string;
                            paddingLarge: string;
                            paddingHuge: string;
                            optionPaddingTiny: string;
                            optionPaddingSmall: string;
                            optionPaddingMedium: string;
                            optionPaddingLarge: string;
                            optionPaddingHuge: string;
                            loadingSize: string;
                        }, {
                            Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                                height: string;
                                width: string;
                                borderRadius: string;
                                color: string;
                                colorHover: string;
                                railInsetHorizontalBottom: string;
                                railInsetHorizontalTop: string;
                                railInsetVerticalRight: string;
                                railInsetVerticalLeft: string;
                                railColor: string;
                            }, any>;
                            Empty: import("../../_mixins").Theme<"Empty", {
                                fontSizeTiny: string;
                                fontSizeSmall: string;
                                fontSizeMedium: string;
                                fontSizeLarge: string;
                                fontSizeHuge: string;
                                textColor: string;
                                iconColor: string;
                                extraTextColor: string;
                                iconSizeTiny: string;
                                iconSizeSmall: string;
                                iconSizeMedium: string;
                                iconSizeLarge: string;
                                iconSizeHuge: string;
                            }, any>;
                        }>;
                    }>> | undefined;
                } | undefined;
            } | undefined;
            Scrollbar?: {
                peers?: {
                    [x: string]: any;
                } | undefined;
            } | undefined;
            Empty?: {
                peers?: {
                    [x: string]: any;
                } | undefined;
            } | undefined;
            Popover?: {
                peers?: {
                    [x: string]: any;
                } | undefined;
            } | undefined;
            Ellipsis?: {
                peers?: {
                    Tooltip?: import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Tooltip", {
                        borderRadius: string;
                        boxShadow: string;
                        color: string;
                        textColor: string;
                        padding: string;
                    }, {
                        Popover: import("../../_mixins").Theme<"Popover", {
                            fontSize: string;
                            borderRadius: string;
                            color: string;
                            dividerColor: string;
                            textColor: string;
                            boxShadow: string;
                            space: string;
                            spaceArrow: string;
                            arrowOffset: string;
                            arrowOffsetVertical: string;
                            arrowHeight: string;
                            padding: string;
                        }, any>;
                    }>> | undefined;
                } | undefined;
            } | undefined;
            Dropdown?: {
                peers?: {
                    Popover?: import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Popover", {
                        fontSize: string;
                        borderRadius: string;
                        color: string;
                        dividerColor: string;
                        textColor: string;
                        boxShadow: string;
                        space: string;
                        spaceArrow: string;
                        arrowOffset: string;
                        arrowOffsetVertical: string;
                        arrowHeight: string;
                        padding: string;
                    }, any>> | undefined;
                } | undefined;
            } | undefined;
        };
    }>;
    paginatedData: import("vue").ComputedRef<import("./interface").TmNode[]>;
    mergedBordered: import("vue").ComputedRef<boolean>;
    mergedBottomBordered: import("vue").ComputedRef<boolean>;
    mergedPagination: import("vue").ComputedRef<import("../../pagination").PaginationProps>;
    mergedShowPagination: import("vue").ComputedRef<boolean | 0 | undefined>;
    cssVars: import("vue").ComputedRef<{
        '--n-font-size': string;
        '--n-th-padding': string;
        '--n-td-padding': string;
        '--n-bezier': string;
        '--n-border-radius': string;
        '--n-line-height': string;
        '--n-border-color': string;
        '--n-border-color-modal': string;
        '--n-border-color-popover': string;
        '--n-th-color': string;
        '--n-th-color-hover': string;
        '--n-th-color-modal': string;
        '--n-th-color-hover-modal': string;
        '--n-th-color-popover': string;
        '--n-th-color-hover-popover': string;
        '--n-td-color': string;
        '--n-td-color-hover': string;
        '--n-td-color-modal': string;
        '--n-td-color-hover-modal': string;
        '--n-td-color-popover': string;
        '--n-td-color-hover-popover': string;
        '--n-th-text-color': string;
        '--n-td-text-color': string;
        '--n-th-font-weight': string;
        '--n-th-button-color-hover': string;
        '--n-th-icon-color': string;
        '--n-th-icon-color-active': string;
        '--n-filter-size': string;
        '--n-pagination-margin': string;
        '--n-empty-padding': string;
        '--n-box-shadow-before': string;
        '--n-box-shadow-after': string;
        '--n-sorter-size': string;
        '--n-resizable-container-size': string;
        '--n-resizable-size': string;
        '--n-loading-size': string;
        '--n-loading-color': string;
        '--n-opacity-loading': string;
        '--n-td-color-striped': string;
        '--n-td-color-striped-modal': string;
        '--n-td-color-striped-popover': string;
        'n-td-color-sorting': string;
        'n-td-color-sorting-modal': string;
        'n-td-color-sorting-popover': string;
        'n-th-color-sorting': string;
        'n-th-color-sorting-modal': string;
        'n-th-color-sorting-popover': string;
    }> | undefined;
    themeClass: import("vue").Ref<string, string> | undefined;
    onRender: (() => void) | undefined;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    readonly onUnstableColumnResize: import("vue").PropType<(resizedWidth: number, limitedWidth: number, column: import("./interface").TableBaseColumn, getColumnWidth: (key: import("./interface").ColumnKey) => number | undefined) => void>;
    readonly pagination: {
        readonly type: import("vue").PropType<false | import("../../pagination").PaginationProps>;
        readonly default: false;
    };
    readonly paginateSinglePage: {
        readonly type: BooleanConstructor;
        readonly default: true;
    };
    readonly minHeight: import("vue").PropType<string | number>;
    readonly maxHeight: import("vue").PropType<string | number>;
    readonly columns: {
        readonly type: import("vue").PropType<import("./interface").TableColumns<any>>;
        readonly default: () => never[];
    };
    readonly rowClassName: import("vue").PropType<string | import("./interface").CreateRowClassName<any>>;
    readonly rowProps: import("vue").PropType<import("./interface").CreateRowProps<any>>;
    readonly rowKey: import("vue").PropType<import("./interface").CreateRowKey<any>>;
    readonly summary: import("vue").PropType<import("./interface").CreateSummary<any>>;
    readonly data: {
        readonly type: import("vue").PropType<import("./interface").RowData[]>;
        readonly default: () => never[];
    };
    readonly loading: BooleanConstructor;
    readonly bordered: {
        readonly type: import("vue").PropType<boolean | undefined>;
        readonly default: undefined;
    };
    readonly bottomBordered: {
        readonly type: import("vue").PropType<boolean | undefined>;
        readonly default: undefined;
    };
    readonly striped: BooleanConstructor;
    readonly scrollX: import("vue").PropType<string | number>;
    readonly defaultCheckedRowKeys: {
        readonly type: import("vue").PropType<RowKey[]>;
        readonly default: () => never[];
    };
    readonly checkedRowKeys: import("vue").PropType<RowKey[]>;
    readonly singleLine: {
        readonly type: BooleanConstructor;
        readonly default: true;
    };
    readonly singleColumn: BooleanConstructor;
    readonly size: {
        readonly type: import("vue").PropType<"small" | "medium" | "large">;
        readonly default: "medium";
    };
    readonly remote: BooleanConstructor;
    readonly defaultExpandedRowKeys: {
        readonly type: import("vue").PropType<RowKey[]>;
        readonly default: readonly [];
    };
    readonly defaultExpandAll: BooleanConstructor;
    readonly expandedRowKeys: import("vue").PropType<RowKey[]>;
    readonly stickyExpandedRows: BooleanConstructor;
    readonly virtualScroll: BooleanConstructor;
    readonly virtualScrollX: BooleanConstructor;
    readonly virtualScrollHeader: BooleanConstructor;
    readonly headerHeight: {
        readonly type: NumberConstructor;
        readonly default: 28;
    };
    readonly heightForRow: import("vue").PropType<import("./interface").DataTableHeightForRow>;
    readonly minRowHeight: {
        readonly type: NumberConstructor;
        readonly default: 28;
    };
    readonly tableLayout: {
        readonly type: import("vue").PropType<"auto" | "fixed">;
        readonly default: "auto";
    };
    readonly allowCheckingNotLoaded: BooleanConstructor;
    readonly cascade: {
        readonly type: BooleanConstructor;
        readonly default: true;
    };
    readonly childrenKey: {
        readonly type: StringConstructor;
        readonly default: "children";
    };
    readonly indent: {
        readonly type: NumberConstructor;
        readonly default: 16;
    };
    readonly flexHeight: BooleanConstructor;
    readonly summaryPlacement: {
        readonly type: import("vue").PropType<"top" | "bottom">;
        readonly default: "bottom";
    };
    readonly paginationBehaviorOnFilter: {
        readonly type: import("vue").PropType<"first" | "current">;
        readonly default: "current";
    };
    readonly filterIconPopoverProps: import("vue").PropType<import("../..").PopoverProps>;
    readonly scrollbarProps: import("vue").PropType<import("../..").ScrollbarProps>;
    readonly renderCell: import("vue").PropType<(value: any, rowData: object, column: import("./interface").TableBaseColumn) => import("vue").VNodeChild>;
    readonly renderExpandIcon: import("vue").PropType<import("./interface").RenderExpandIcon>;
    readonly spinProps: {
        readonly type: import("vue").PropType<import("../../_internal").BaseLoadingExposedProps>;
        readonly default: {};
    };
    readonly getCsvCell: import("vue").PropType<import("./publicTypes").DataTableGetCsvCell>;
    readonly getCsvHeader: import("vue").PropType<import("./publicTypes").DataTableGetCsvHeader>;
    readonly onLoad: import("vue").PropType<import("./interface").DataTableOnLoad>;
    readonly 'onUpdate:page': import("vue").PropType<import("../../pagination").PaginationProps["onUpdate:page"]>;
    readonly onUpdatePage: import("vue").PropType<import("../../pagination").PaginationProps["onUpdate:page"]>;
    readonly 'onUpdate:pageSize': import("vue").PropType<import("../../pagination").PaginationProps["onUpdate:pageSize"]>;
    readonly onUpdatePageSize: import("vue").PropType<import("../../pagination").PaginationProps["onUpdate:pageSize"]>;
    readonly 'onUpdate:sorter': import("vue").PropType<import("../../_utils").MaybeArray<import("./interface").OnUpdateSorter>>;
    readonly onUpdateSorter: import("vue").PropType<import("../../_utils").MaybeArray<import("./interface").OnUpdateSorter>>;
    readonly 'onUpdate:filters': import("vue").PropType<import("../../_utils").MaybeArray<import("./interface").OnUpdateFilters>>;
    readonly onUpdateFilters: import("vue").PropType<import("../../_utils").MaybeArray<import("./interface").OnUpdateFilters>>;
    readonly 'onUpdate:checkedRowKeys': import("vue").PropType<import("../../_utils").MaybeArray<import("./interface").OnUpdateCheckedRowKeys>>;
    readonly onUpdateCheckedRowKeys: import("vue").PropType<import("../../_utils").MaybeArray<import("./interface").OnUpdateCheckedRowKeys>>;
    readonly 'onUpdate:expandedRowKeys': import("vue").PropType<import("../../_utils").MaybeArray<import("./interface").OnUpdateExpandedRowKeys>>;
    readonly onUpdateExpandedRowKeys: import("vue").PropType<import("../../_utils").MaybeArray<import("./interface").OnUpdateExpandedRowKeys>>;
    readonly onScroll: import("vue").PropType<(e: Event) => void>;
    readonly onPageChange: import("vue").PropType<import("../../pagination").PaginationProps["onUpdate:page"]>;
    readonly onPageSizeChange: import("vue").PropType<import("../../pagination").PaginationProps["onUpdate:pageSize"]>;
    readonly onSorterChange: import("vue").PropType<import("../../_utils").MaybeArray<import("./interface").OnUpdateSorter> | undefined>;
    readonly onFiltersChange: import("vue").PropType<import("../../_utils").MaybeArray<import("./interface").OnUpdateFilters> | undefined>;
    readonly onCheckedRowKeysChange: import("vue").PropType<import("../../_utils").MaybeArray<import("./interface").OnUpdateCheckedRowKeys> | undefined>;
    readonly theme: import("vue").PropType<import("../../_mixins").Theme<"DataTable", {
        actionDividerColor: string;
        lineHeight: string;
        borderRadius: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        borderColor: string;
        tdColorHover: string;
        tdColorSorting: string;
        tdColorStriped: string;
        thColor: string;
        thColorHover: string;
        thColorSorting: string;
        tdColor: string;
        tdTextColor: string;
        thTextColor: string;
        thFontWeight: string;
        thButtonColorHover: string;
        thIconColor: string;
        thIconColorActive: string;
        borderColorModal: string;
        tdColorHoverModal: string;
        tdColorSortingModal: string;
        tdColorStripedModal: string;
        thColorModal: string;
        thColorHoverModal: string;
        thColorSortingModal: string;
        tdColorModal: string;
        borderColorPopover: string;
        tdColorHoverPopover: string;
        tdColorSortingPopover: string;
        tdColorStripedPopover: string;
        thColorPopover: string;
        thColorHoverPopover: string;
        thColorSortingPopover: string;
        tdColorPopover: string;
        boxShadowBefore: string;
        boxShadowAfter: string;
        loadingColor: string;
        loadingSize: string;
        opacityLoading: string;
        thPaddingSmall: string;
        thPaddingMedium: string;
        thPaddingLarge: string;
        tdPaddingSmall: string;
        tdPaddingMedium: string;
        tdPaddingLarge: string;
        sorterSize: string;
        resizableContainerSize: string;
        resizableSize: string;
        filterSize: string;
        paginationMargin: string;
        emptyPadding: string;
        actionPadding: string;
        actionButtonMargin: string;
    }, {
        Button: import("../../_mixins").Theme<"Button", {
            heightTiny: string;
            heightSmall: string;
            heightMedium: string;
            heightLarge: string;
            borderRadiusTiny: string;
            borderRadiusSmall: string;
            borderRadiusMedium: string;
            borderRadiusLarge: string;
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            opacityDisabled: string;
            colorOpacitySecondary: string;
            colorOpacitySecondaryHover: string;
            colorOpacitySecondaryPressed: string;
            colorSecondary: string;
            colorSecondaryHover: string;
            colorSecondaryPressed: string;
            colorTertiary: string;
            colorTertiaryHover: string;
            colorTertiaryPressed: string;
            colorQuaternary: string;
            colorQuaternaryHover: string;
            colorQuaternaryPressed: string;
            color: string;
            colorHover: string;
            colorPressed: string;
            colorFocus: string;
            colorDisabled: string;
            textColor: string;
            textColorTertiary: string;
            textColorHover: string;
            textColorPressed: string;
            textColorFocus: string;
            textColorDisabled: string;
            textColorText: string;
            textColorTextHover: string;
            textColorTextPressed: string;
            textColorTextFocus: string;
            textColorTextDisabled: string;
            textColorGhost: string;
            textColorGhostHover: string;
            textColorGhostPressed: string;
            textColorGhostFocus: string;
            textColorGhostDisabled: string;
            border: string;
            borderHover: string;
            borderPressed: string;
            borderFocus: string;
            borderDisabled: string;
            rippleColor: string;
            colorPrimary: string;
            colorHoverPrimary: string;
            colorPressedPrimary: string;
            colorFocusPrimary: string;
            colorDisabledPrimary: string;
            textColorPrimary: string;
            textColorHoverPrimary: string;
            textColorPressedPrimary: string;
            textColorFocusPrimary: string;
            textColorDisabledPrimary: string;
            textColorTextPrimary: string;
            textColorTextHoverPrimary: string;
            textColorTextPressedPrimary: string;
            textColorTextFocusPrimary: string;
            textColorTextDisabledPrimary: string;
            textColorGhostPrimary: string;
            textColorGhostHoverPrimary: string;
            textColorGhostPressedPrimary: string;
            textColorGhostFocusPrimary: string;
            textColorGhostDisabledPrimary: string;
            borderPrimary: string;
            borderHoverPrimary: string;
            borderPressedPrimary: string;
            borderFocusPrimary: string;
            borderDisabledPrimary: string;
            rippleColorPrimary: string;
            colorInfo: string;
            colorHoverInfo: string;
            colorPressedInfo: string;
            colorFocusInfo: string;
            colorDisabledInfo: string;
            textColorInfo: string;
            textColorHoverInfo: string;
            textColorPressedInfo: string;
            textColorFocusInfo: string;
            textColorDisabledInfo: string;
            textColorTextInfo: string;
            textColorTextHoverInfo: string;
            textColorTextPressedInfo: string;
            textColorTextFocusInfo: string;
            textColorTextDisabledInfo: string;
            textColorGhostInfo: string;
            textColorGhostHoverInfo: string;
            textColorGhostPressedInfo: string;
            textColorGhostFocusInfo: string;
            textColorGhostDisabledInfo: string;
            borderInfo: string;
            borderHoverInfo: string;
            borderPressedInfo: string;
            borderFocusInfo: string;
            borderDisabledInfo: string;
            rippleColorInfo: string;
            colorSuccess: string;
            colorHoverSuccess: string;
            colorPressedSuccess: string;
            colorFocusSuccess: string;
            colorDisabledSuccess: string;
            textColorSuccess: string;
            textColorHoverSuccess: string;
            textColorPressedSuccess: string;
            textColorFocusSuccess: string;
            textColorDisabledSuccess: string;
            textColorTextSuccess: string;
            textColorTextHoverSuccess: string;
            textColorTextPressedSuccess: string;
            textColorTextFocusSuccess: string;
            textColorTextDisabledSuccess: string;
            textColorGhostSuccess: string;
            textColorGhostHoverSuccess: string;
            textColorGhostPressedSuccess: string;
            textColorGhostFocusSuccess: string;
            textColorGhostDisabledSuccess: string;
            borderSuccess: string;
            borderHoverSuccess: string;
            borderPressedSuccess: string;
            borderFocusSuccess: string;
            borderDisabledSuccess: string;
            rippleColorSuccess: string;
            colorWarning: string;
            colorHoverWarning: string;
            colorPressedWarning: string;
            colorFocusWarning: string;
            colorDisabledWarning: string;
            textColorWarning: string;
            textColorHoverWarning: string;
            textColorPressedWarning: string;
            textColorFocusWarning: string;
            textColorDisabledWarning: string;
            textColorTextWarning: string;
            textColorTextHoverWarning: string;
            textColorTextPressedWarning: string;
            textColorTextFocusWarning: string;
            textColorTextDisabledWarning: string;
            textColorGhostWarning: string;
            textColorGhostHoverWarning: string;
            textColorGhostPressedWarning: string;
            textColorGhostFocusWarning: string;
            textColorGhostDisabledWarning: string;
            borderWarning: string;
            borderHoverWarning: string;
            borderPressedWarning: string;
            borderFocusWarning: string;
            borderDisabledWarning: string;
            rippleColorWarning: string;
            colorError: string;
            colorHoverError: string;
            colorPressedError: string;
            colorFocusError: string;
            colorDisabledError: string;
            textColorError: string;
            textColorHoverError: string;
            textColorPressedError: string;
            textColorFocusError: string;
            textColorDisabledError: string;
            textColorTextError: string;
            textColorTextHoverError: string;
            textColorTextPressedError: string;
            textColorTextFocusError: string;
            textColorTextDisabledError: string;
            textColorGhostError: string;
            textColorGhostHoverError: string;
            textColorGhostPressedError: string;
            textColorGhostFocusError: string;
            textColorGhostDisabledError: string;
            borderError: string;
            borderHoverError: string;
            borderPressedError: string;
            borderFocusError: string;
            borderDisabledError: string;
            rippleColorError: string;
            waveOpacity: string;
            fontWeight: string;
            fontWeightStrong: string;
            paddingTiny: string;
            paddingSmall: string;
            paddingMedium: string;
            paddingLarge: string;
            paddingRoundTiny: string;
            paddingRoundSmall: string;
            paddingRoundMedium: string;
            paddingRoundLarge: string;
            iconMarginTiny: string;
            iconMarginSmall: string;
            iconMarginMedium: string;
            iconMarginLarge: string;
            iconSizeTiny: string;
            iconSizeSmall: string;
            iconSizeMedium: string;
            iconSizeLarge: string;
            rippleDuration: string;
        }, any>;
        Checkbox: import("../../_mixins").Theme<"Checkbox", {
            labelLineHeight: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            borderRadius: string;
            color: string;
            colorChecked: string;
            colorDisabled: string;
            colorDisabledChecked: string;
            colorTableHeader: string;
            colorTableHeaderModal: string;
            colorTableHeaderPopover: string;
            checkMarkColor: string;
            checkMarkColorDisabled: string;
            checkMarkColorDisabledChecked: string;
            border: string;
            borderDisabled: string;
            borderDisabledChecked: string;
            borderChecked: string;
            borderFocus: string;
            boxShadowFocus: string;
            textColor: string;
            textColorDisabled: string;
            sizeSmall: string;
            sizeMedium: string;
            sizeLarge: string;
            labelPadding: string;
            labelFontWeight: string;
        }, any>;
        Radio: import("../../_mixins").Theme<"Radio", {
            labelLineHeight: string;
            buttonHeightSmall: string;
            buttonHeightMedium: string;
            buttonHeightLarge: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            boxShadow: string;
            boxShadowActive: string;
            boxShadowFocus: string;
            boxShadowHover: string;
            boxShadowDisabled: string;
            color: string;
            colorDisabled: string;
            colorActive: string;
            textColor: string;
            textColorDisabled: string;
            dotColorActive: string;
            dotColorDisabled: string;
            buttonBorderColor: string;
            buttonBorderColorActive: string;
            buttonBorderColorHover: string;
            buttonColor: string;
            buttonColorActive: string;
            buttonTextColor: string;
            buttonTextColorActive: string;
            buttonTextColorHover: string;
            opacityDisabled: string;
            buttonBoxShadowFocus: string;
            buttonBoxShadowHover: string;
            buttonBoxShadow: string;
            buttonBorderRadius: string;
            radioSizeSmall: string;
            radioSizeMedium: string;
            radioSizeLarge: string;
            labelPadding: string;
            labelFontWeight: string;
        }, any>;
        Pagination: import("../../_mixins").Theme<"Pagination", {
            buttonColor: string;
            buttonColorHover: string;
            buttonColorPressed: string;
            buttonBorder: string;
            buttonBorderHover: string;
            buttonBorderPressed: string;
            buttonIconColor: string;
            buttonIconColorHover: string;
            buttonIconColorPressed: string;
            itemTextColor: string;
            itemTextColorHover: string;
            itemTextColorPressed: string;
            itemTextColorActive: string;
            itemTextColorDisabled: string;
            itemColor: string;
            itemColorHover: string;
            itemColorPressed: string;
            itemColorActive: string;
            itemColorActiveHover: string;
            itemColorDisabled: string;
            itemBorder: string;
            itemBorderHover: string;
            itemBorderPressed: string;
            itemBorderActive: string;
            itemBorderDisabled: string;
            itemBorderRadius: string;
            itemSizeSmall: string;
            itemSizeMedium: string;
            itemSizeLarge: string;
            itemFontSizeSmall: string;
            itemFontSizeMedium: string;
            itemFontSizeLarge: string;
            jumperFontSizeSmall: string;
            jumperFontSizeMedium: string;
            jumperFontSizeLarge: string;
            jumperTextColor: string;
            jumperTextColorDisabled: string;
            itemPaddingSmall: string;
            itemMarginSmall: string;
            itemMarginSmallRtl: string;
            itemPaddingMedium: string;
            itemMarginMedium: string;
            itemMarginMediumRtl: string;
            itemPaddingLarge: string;
            itemMarginLarge: string;
            itemMarginLargeRtl: string;
            buttonIconSizeSmall: string;
            buttonIconSizeMedium: string;
            buttonIconSizeLarge: string;
            inputWidthSmall: string;
            selectWidthSmall: string;
            inputMarginSmall: string;
            inputMarginSmallRtl: string;
            selectMarginSmall: string;
            prefixMarginSmall: string;
            suffixMarginSmall: string;
            inputWidthMedium: string;
            selectWidthMedium: string;
            inputMarginMedium: string;
            inputMarginMediumRtl: string;
            selectMarginMedium: string;
            prefixMarginMedium: string;
            suffixMarginMedium: string;
            inputWidthLarge: string;
            selectWidthLarge: string;
            inputMarginLarge: string;
            inputMarginLargeRtl: string;
            selectMarginLarge: string;
            prefixMarginLarge: string;
            suffixMarginLarge: string;
        }, {
            Select: import("../../_mixins").Theme<"Select", {
                menuBoxShadow: string;
            }, {
                InternalSelection: import("../../_mixins").Theme<"InternalSelection", {
                    fontSizeTiny: string;
                    fontSizeSmall: string;
                    fontSizeMedium: string;
                    fontSizeLarge: string;
                    heightTiny: string;
                    heightSmall: string;
                    heightMedium: string;
                    heightLarge: string;
                    borderRadius: string;
                    fontWeight: string;
                    textColor: string;
                    textColorDisabled: string;
                    placeholderColor: string;
                    placeholderColorDisabled: string;
                    color: string;
                    colorDisabled: string;
                    colorActive: string;
                    border: string;
                    borderHover: string;
                    borderActive: string;
                    borderFocus: string;
                    boxShadowHover: string;
                    boxShadowActive: string;
                    boxShadowFocus: string;
                    caretColor: string;
                    arrowColor: string;
                    arrowColorDisabled: string;
                    loadingColor: string;
                    borderWarning: string;
                    borderHoverWarning: string;
                    borderActiveWarning: string;
                    borderFocusWarning: string;
                    boxShadowHoverWarning: string;
                    boxShadowActiveWarning: string;
                    boxShadowFocusWarning: string;
                    colorActiveWarning: string;
                    caretColorWarning: string;
                    borderError: string;
                    borderHoverError: string;
                    borderActiveError: string;
                    borderFocusError: string;
                    boxShadowHoverError: string;
                    boxShadowActiveError: string;
                    boxShadowFocusError: string;
                    colorActiveError: string;
                    caretColorError: string;
                    clearColor: string;
                    clearColorHover: string;
                    clearColorPressed: string;
                    paddingSingle: string;
                    paddingMultiple: string;
                    clearSize: string;
                    arrowSize: string;
                }, {
                    Popover: import("../../_mixins").Theme<"Popover", {
                        fontSize: string;
                        borderRadius: string;
                        color: string;
                        dividerColor: string;
                        textColor: string;
                        boxShadow: string;
                        space: string;
                        spaceArrow: string;
                        arrowOffset: string;
                        arrowOffsetVertical: string;
                        arrowHeight: string;
                        padding: string;
                    }, any>;
                }>;
                InternalSelectMenu: import("../../_mixins").Theme<"InternalSelectMenu", {
                    optionFontSizeTiny: string;
                    optionFontSizeSmall: string;
                    optionFontSizeMedium: string;
                    optionFontSizeLarge: string;
                    optionFontSizeHuge: string;
                    optionHeightTiny: string;
                    optionHeightSmall: string;
                    optionHeightMedium: string;
                    optionHeightLarge: string;
                    optionHeightHuge: string;
                    borderRadius: string;
                    color: string;
                    groupHeaderTextColor: string;
                    actionDividerColor: string;
                    optionTextColor: string;
                    optionTextColorPressed: string;
                    optionTextColorDisabled: string;
                    optionTextColorActive: string;
                    optionOpacityDisabled: string;
                    optionCheckColor: string;
                    optionColorPending: string;
                    optionColorActive: string;
                    optionColorActivePending: string;
                    actionTextColor: string;
                    loadingColor: string;
                    height: string;
                    paddingTiny: string;
                    paddingSmall: string;
                    paddingMedium: string;
                    paddingLarge: string;
                    paddingHuge: string;
                    optionPaddingTiny: string;
                    optionPaddingSmall: string;
                    optionPaddingMedium: string;
                    optionPaddingLarge: string;
                    optionPaddingHuge: string;
                    loadingSize: string;
                }, {
                    Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                        height: string;
                        width: string;
                        borderRadius: string;
                        color: string;
                        colorHover: string;
                        railInsetHorizontalBottom: string;
                        railInsetHorizontalTop: string;
                        railInsetVerticalRight: string;
                        railInsetVerticalLeft: string;
                        railColor: string;
                    }, any>;
                    Empty: import("../../_mixins").Theme<"Empty", {
                        fontSizeTiny: string;
                        fontSizeSmall: string;
                        fontSizeMedium: string;
                        fontSizeLarge: string;
                        fontSizeHuge: string;
                        textColor: string;
                        iconColor: string;
                        extraTextColor: string;
                        iconSizeTiny: string;
                        iconSizeSmall: string;
                        iconSizeMedium: string;
                        iconSizeLarge: string;
                        iconSizeHuge: string;
                    }, any>;
                }>;
            }>;
            Input: import("../../_mixins").Theme<"Input", {
                fontWeight: string;
                countTextColorDisabled: string;
                countTextColor: string;
                heightTiny: string;
                heightSmall: string;
                heightMedium: string;
                heightLarge: string;
                fontSizeTiny: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                lineHeight: string;
                lineHeightTextarea: string;
                borderRadius: string;
                iconSize: string;
                groupLabelColor: string;
                groupLabelTextColor: string;
                textColor: string;
                textColorDisabled: string;
                textDecorationColor: string;
                caretColor: string;
                placeholderColor: string;
                placeholderColorDisabled: string;
                color: string;
                colorDisabled: string;
                colorFocus: string;
                groupLabelBorder: string;
                border: string;
                borderHover: string;
                borderDisabled: string;
                borderFocus: string;
                boxShadowFocus: string;
                loadingColor: string;
                loadingColorWarning: string;
                borderWarning: string;
                borderHoverWarning: string;
                colorFocusWarning: string;
                borderFocusWarning: string;
                boxShadowFocusWarning: string;
                caretColorWarning: string;
                loadingColorError: string;
                borderError: string;
                borderHoverError: string;
                colorFocusError: string;
                borderFocusError: string;
                boxShadowFocusError: string;
                caretColorError: string;
                clearColor: string;
                clearColorHover: string;
                clearColorPressed: string;
                iconColor: string;
                iconColorDisabled: string;
                iconColorHover: string;
                iconColorPressed: string;
                suffixTextColor: string;
                paddingTiny: string;
                paddingSmall: string;
                paddingMedium: string;
                paddingLarge: string;
                clearSize: string;
            }, any>;
            Popselect: import("../../_mixins").Theme<"Popselect", {
                menuBoxShadow: string;
            }, {
                Popover: import("../../_mixins").Theme<"Popover", {
                    fontSize: string;
                    borderRadius: string;
                    color: string;
                    dividerColor: string;
                    textColor: string;
                    boxShadow: string;
                    space: string;
                    spaceArrow: string;
                    arrowOffset: string;
                    arrowOffsetVertical: string;
                    arrowHeight: string;
                    padding: string;
                }, any>;
                InternalSelectMenu: import("../../_mixins").Theme<"InternalSelectMenu", {
                    optionFontSizeTiny: string;
                    optionFontSizeSmall: string;
                    optionFontSizeMedium: string;
                    optionFontSizeLarge: string;
                    optionFontSizeHuge: string;
                    optionHeightTiny: string;
                    optionHeightSmall: string;
                    optionHeightMedium: string;
                    optionHeightLarge: string;
                    optionHeightHuge: string;
                    borderRadius: string;
                    color: string;
                    groupHeaderTextColor: string;
                    actionDividerColor: string;
                    optionTextColor: string;
                    optionTextColorPressed: string;
                    optionTextColorDisabled: string;
                    optionTextColorActive: string;
                    optionOpacityDisabled: string;
                    optionCheckColor: string;
                    optionColorPending: string;
                    optionColorActive: string;
                    optionColorActivePending: string;
                    actionTextColor: string;
                    loadingColor: string;
                    height: string;
                    paddingTiny: string;
                    paddingSmall: string;
                    paddingMedium: string;
                    paddingLarge: string;
                    paddingHuge: string;
                    optionPaddingTiny: string;
                    optionPaddingSmall: string;
                    optionPaddingMedium: string;
                    optionPaddingLarge: string;
                    optionPaddingHuge: string;
                    loadingSize: string;
                }, {
                    Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                        height: string;
                        width: string;
                        borderRadius: string;
                        color: string;
                        colorHover: string;
                        railInsetHorizontalBottom: string;
                        railInsetHorizontalTop: string;
                        railInsetVerticalRight: string;
                        railInsetVerticalLeft: string;
                        railColor: string;
                    }, any>;
                    Empty: import("../../_mixins").Theme<"Empty", {
                        fontSizeTiny: string;
                        fontSizeSmall: string;
                        fontSizeMedium: string;
                        fontSizeLarge: string;
                        fontSizeHuge: string;
                        textColor: string;
                        iconColor: string;
                        extraTextColor: string;
                        iconSizeTiny: string;
                        iconSizeSmall: string;
                        iconSizeMedium: string;
                        iconSizeLarge: string;
                        iconSizeHuge: string;
                    }, any>;
                }>;
            }>;
        }>;
        Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
            height: string;
            width: string;
            borderRadius: string;
            color: string;
            colorHover: string;
            railInsetHorizontalBottom: string;
            railInsetHorizontalTop: string;
            railInsetVerticalRight: string;
            railInsetVerticalLeft: string;
            railColor: string;
        }, any>;
        Empty: import("../../_mixins").Theme<"Empty", {
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            fontSizeHuge: string;
            textColor: string;
            iconColor: string;
            extraTextColor: string;
            iconSizeTiny: string;
            iconSizeSmall: string;
            iconSizeMedium: string;
            iconSizeLarge: string;
            iconSizeHuge: string;
        }, any>;
        Popover: import("../../_mixins").Theme<"Popover", {
            fontSize: string;
            borderRadius: string;
            color: string;
            dividerColor: string;
            textColor: string;
            boxShadow: string;
            space: string;
            spaceArrow: string;
            arrowOffset: string;
            arrowOffsetVertical: string;
            arrowHeight: string;
            padding: string;
        }, any>;
        Ellipsis: import("../../_mixins").Theme<"Ellipsis", unknown, {
            Tooltip: import("../../_mixins").Theme<"Tooltip", {
                borderRadius: string;
                boxShadow: string;
                color: string;
                textColor: string;
                padding: string;
            }, {
                Popover: import("../../_mixins").Theme<"Popover", {
                    fontSize: string;
                    borderRadius: string;
                    color: string;
                    dividerColor: string;
                    textColor: string;
                    boxShadow: string;
                    space: string;
                    spaceArrow: string;
                    arrowOffset: string;
                    arrowOffsetVertical: string;
                    arrowHeight: string;
                    padding: string;
                }, any>;
            }>;
        }>;
        Dropdown: import("../../_mixins").Theme<"Dropdown", {
            optionHeightSmall: string;
            optionHeightMedium: string;
            optionHeightLarge: string;
            optionHeightHuge: string;
            borderRadius: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            fontSizeHuge: string;
            optionTextColor: string;
            optionTextColorHover: string;
            optionTextColorActive: string;
            optionTextColorChildActive: string;
            color: string;
            dividerColor: string;
            suffixColor: string;
            prefixColor: string;
            optionColorHover: string;
            optionColorActive: string;
            groupHeaderTextColor: string;
            optionTextColorInverted: string;
            optionTextColorHoverInverted: string;
            optionTextColorActiveInverted: string;
            optionTextColorChildActiveInverted: string;
            colorInverted: string;
            dividerColorInverted: string;
            suffixColorInverted: string;
            prefixColorInverted: string;
            optionColorHoverInverted: string;
            optionColorActiveInverted: string;
            groupHeaderTextColorInverted: string;
            optionOpacityDisabled: string;
            padding: string;
            optionIconSizeSmall: string;
            optionIconSizeMedium: string;
            optionIconSizeLarge: string;
            optionIconSizeHuge: string;
            optionSuffixWidthSmall: string;
            optionSuffixWidthMedium: string;
            optionSuffixWidthLarge: string;
            optionSuffixWidthHuge: string;
            optionIconSuffixWidthSmall: string;
            optionIconSuffixWidthMedium: string;
            optionIconSuffixWidthLarge: string;
            optionIconSuffixWidthHuge: string;
            optionPrefixWidthSmall: string;
            optionPrefixWidthMedium: string;
            optionPrefixWidthLarge: string;
            optionPrefixWidthHuge: string;
            optionIconPrefixWidthSmall: string;
            optionIconPrefixWidthMedium: string;
            optionIconPrefixWidthLarge: string;
            optionIconPrefixWidthHuge: string;
        }, {
            Popover: import("../../_mixins").Theme<"Popover", {
                fontSize: string;
                borderRadius: string;
                color: string;
                dividerColor: string;
                textColor: string;
                boxShadow: string;
                space: string;
                spaceArrow: string;
                arrowOffset: string;
                arrowOffsetVertical: string;
                arrowHeight: string;
                padding: string;
            }, any>;
        }>;
    }>>;
    readonly themeOverrides: import("vue").PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"DataTable", {
        actionDividerColor: string;
        lineHeight: string;
        borderRadius: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        borderColor: string;
        tdColorHover: string;
        tdColorSorting: string;
        tdColorStriped: string;
        thColor: string;
        thColorHover: string;
        thColorSorting: string;
        tdColor: string;
        tdTextColor: string;
        thTextColor: string;
        thFontWeight: string;
        thButtonColorHover: string;
        thIconColor: string;
        thIconColorActive: string;
        borderColorModal: string;
        tdColorHoverModal: string;
        tdColorSortingModal: string;
        tdColorStripedModal: string;
        thColorModal: string;
        thColorHoverModal: string;
        thColorSortingModal: string;
        tdColorModal: string;
        borderColorPopover: string;
        tdColorHoverPopover: string;
        tdColorSortingPopover: string;
        tdColorStripedPopover: string;
        thColorPopover: string;
        thColorHoverPopover: string;
        thColorSortingPopover: string;
        tdColorPopover: string;
        boxShadowBefore: string;
        boxShadowAfter: string;
        loadingColor: string;
        loadingSize: string;
        opacityLoading: string;
        thPaddingSmall: string;
        thPaddingMedium: string;
        thPaddingLarge: string;
        tdPaddingSmall: string;
        tdPaddingMedium: string;
        tdPaddingLarge: string;
        sorterSize: string;
        resizableContainerSize: string;
        resizableSize: string;
        filterSize: string;
        paginationMargin: string;
        emptyPadding: string;
        actionPadding: string;
        actionButtonMargin: string;
    }, {
        Button: import("../../_mixins").Theme<"Button", {
            heightTiny: string;
            heightSmall: string;
            heightMedium: string;
            heightLarge: string;
            borderRadiusTiny: string;
            borderRadiusSmall: string;
            borderRadiusMedium: string;
            borderRadiusLarge: string;
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            opacityDisabled: string;
            colorOpacitySecondary: string;
            colorOpacitySecondaryHover: string;
            colorOpacitySecondaryPressed: string;
            colorSecondary: string;
            colorSecondaryHover: string;
            colorSecondaryPressed: string;
            colorTertiary: string;
            colorTertiaryHover: string;
            colorTertiaryPressed: string;
            colorQuaternary: string;
            colorQuaternaryHover: string;
            colorQuaternaryPressed: string;
            color: string;
            colorHover: string;
            colorPressed: string;
            colorFocus: string;
            colorDisabled: string;
            textColor: string;
            textColorTertiary: string;
            textColorHover: string;
            textColorPressed: string;
            textColorFocus: string;
            textColorDisabled: string;
            textColorText: string;
            textColorTextHover: string;
            textColorTextPressed: string;
            textColorTextFocus: string;
            textColorTextDisabled: string;
            textColorGhost: string;
            textColorGhostHover: string;
            textColorGhostPressed: string;
            textColorGhostFocus: string;
            textColorGhostDisabled: string;
            border: string;
            borderHover: string;
            borderPressed: string;
            borderFocus: string;
            borderDisabled: string;
            rippleColor: string;
            colorPrimary: string;
            colorHoverPrimary: string;
            colorPressedPrimary: string;
            colorFocusPrimary: string;
            colorDisabledPrimary: string;
            textColorPrimary: string;
            textColorHoverPrimary: string;
            textColorPressedPrimary: string;
            textColorFocusPrimary: string;
            textColorDisabledPrimary: string;
            textColorTextPrimary: string;
            textColorTextHoverPrimary: string;
            textColorTextPressedPrimary: string;
            textColorTextFocusPrimary: string;
            textColorTextDisabledPrimary: string;
            textColorGhostPrimary: string;
            textColorGhostHoverPrimary: string;
            textColorGhostPressedPrimary: string;
            textColorGhostFocusPrimary: string;
            textColorGhostDisabledPrimary: string;
            borderPrimary: string;
            borderHoverPrimary: string;
            borderPressedPrimary: string;
            borderFocusPrimary: string;
            borderDisabledPrimary: string;
            rippleColorPrimary: string;
            colorInfo: string;
            colorHoverInfo: string;
            colorPressedInfo: string;
            colorFocusInfo: string;
            colorDisabledInfo: string;
            textColorInfo: string;
            textColorHoverInfo: string;
            textColorPressedInfo: string;
            textColorFocusInfo: string;
            textColorDisabledInfo: string;
            textColorTextInfo: string;
            textColorTextHoverInfo: string;
            textColorTextPressedInfo: string;
            textColorTextFocusInfo: string;
            textColorTextDisabledInfo: string;
            textColorGhostInfo: string;
            textColorGhostHoverInfo: string;
            textColorGhostPressedInfo: string;
            textColorGhostFocusInfo: string;
            textColorGhostDisabledInfo: string;
            borderInfo: string;
            borderHoverInfo: string;
            borderPressedInfo: string;
            borderFocusInfo: string;
            borderDisabledInfo: string;
            rippleColorInfo: string;
            colorSuccess: string;
            colorHoverSuccess: string;
            colorPressedSuccess: string;
            colorFocusSuccess: string;
            colorDisabledSuccess: string;
            textColorSuccess: string;
            textColorHoverSuccess: string;
            textColorPressedSuccess: string;
            textColorFocusSuccess: string;
            textColorDisabledSuccess: string;
            textColorTextSuccess: string;
            textColorTextHoverSuccess: string;
            textColorTextPressedSuccess: string;
            textColorTextFocusSuccess: string;
            textColorTextDisabledSuccess: string;
            textColorGhostSuccess: string;
            textColorGhostHoverSuccess: string;
            textColorGhostPressedSuccess: string;
            textColorGhostFocusSuccess: string;
            textColorGhostDisabledSuccess: string;
            borderSuccess: string;
            borderHoverSuccess: string;
            borderPressedSuccess: string;
            borderFocusSuccess: string;
            borderDisabledSuccess: string;
            rippleColorSuccess: string;
            colorWarning: string;
            colorHoverWarning: string;
            colorPressedWarning: string;
            colorFocusWarning: string;
            colorDisabledWarning: string;
            textColorWarning: string;
            textColorHoverWarning: string;
            textColorPressedWarning: string;
            textColorFocusWarning: string;
            textColorDisabledWarning: string;
            textColorTextWarning: string;
            textColorTextHoverWarning: string;
            textColorTextPressedWarning: string;
            textColorTextFocusWarning: string;
            textColorTextDisabledWarning: string;
            textColorGhostWarning: string;
            textColorGhostHoverWarning: string;
            textColorGhostPressedWarning: string;
            textColorGhostFocusWarning: string;
            textColorGhostDisabledWarning: string;
            borderWarning: string;
            borderHoverWarning: string;
            borderPressedWarning: string;
            borderFocusWarning: string;
            borderDisabledWarning: string;
            rippleColorWarning: string;
            colorError: string;
            colorHoverError: string;
            colorPressedError: string;
            colorFocusError: string;
            colorDisabledError: string;
            textColorError: string;
            textColorHoverError: string;
            textColorPressedError: string;
            textColorFocusError: string;
            textColorDisabledError: string;
            textColorTextError: string;
            textColorTextHoverError: string;
            textColorTextPressedError: string;
            textColorTextFocusError: string;
            textColorTextDisabledError: string;
            textColorGhostError: string;
            textColorGhostHoverError: string;
            textColorGhostPressedError: string;
            textColorGhostFocusError: string;
            textColorGhostDisabledError: string;
            borderError: string;
            borderHoverError: string;
            borderPressedError: string;
            borderFocusError: string;
            borderDisabledError: string;
            rippleColorError: string;
            waveOpacity: string;
            fontWeight: string;
            fontWeightStrong: string;
            paddingTiny: string;
            paddingSmall: string;
            paddingMedium: string;
            paddingLarge: string;
            paddingRoundTiny: string;
            paddingRoundSmall: string;
            paddingRoundMedium: string;
            paddingRoundLarge: string;
            iconMarginTiny: string;
            iconMarginSmall: string;
            iconMarginMedium: string;
            iconMarginLarge: string;
            iconSizeTiny: string;
            iconSizeSmall: string;
            iconSizeMedium: string;
            iconSizeLarge: string;
            rippleDuration: string;
        }, any>;
        Checkbox: import("../../_mixins").Theme<"Checkbox", {
            labelLineHeight: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            borderRadius: string;
            color: string;
            colorChecked: string;
            colorDisabled: string;
            colorDisabledChecked: string;
            colorTableHeader: string;
            colorTableHeaderModal: string;
            colorTableHeaderPopover: string;
            checkMarkColor: string;
            checkMarkColorDisabled: string;
            checkMarkColorDisabledChecked: string;
            border: string;
            borderDisabled: string;
            borderDisabledChecked: string;
            borderChecked: string;
            borderFocus: string;
            boxShadowFocus: string;
            textColor: string;
            textColorDisabled: string;
            sizeSmall: string;
            sizeMedium: string;
            sizeLarge: string;
            labelPadding: string;
            labelFontWeight: string;
        }, any>;
        Radio: import("../../_mixins").Theme<"Radio", {
            labelLineHeight: string;
            buttonHeightSmall: string;
            buttonHeightMedium: string;
            buttonHeightLarge: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            boxShadow: string;
            boxShadowActive: string;
            boxShadowFocus: string;
            boxShadowHover: string;
            boxShadowDisabled: string;
            color: string;
            colorDisabled: string;
            colorActive: string;
            textColor: string;
            textColorDisabled: string;
            dotColorActive: string;
            dotColorDisabled: string;
            buttonBorderColor: string;
            buttonBorderColorActive: string;
            buttonBorderColorHover: string;
            buttonColor: string;
            buttonColorActive: string;
            buttonTextColor: string;
            buttonTextColorActive: string;
            buttonTextColorHover: string;
            opacityDisabled: string;
            buttonBoxShadowFocus: string;
            buttonBoxShadowHover: string;
            buttonBoxShadow: string;
            buttonBorderRadius: string;
            radioSizeSmall: string;
            radioSizeMedium: string;
            radioSizeLarge: string;
            labelPadding: string;
            labelFontWeight: string;
        }, any>;
        Pagination: import("../../_mixins").Theme<"Pagination", {
            buttonColor: string;
            buttonColorHover: string;
            buttonColorPressed: string;
            buttonBorder: string;
            buttonBorderHover: string;
            buttonBorderPressed: string;
            buttonIconColor: string;
            buttonIconColorHover: string;
            buttonIconColorPressed: string;
            itemTextColor: string;
            itemTextColorHover: string;
            itemTextColorPressed: string;
            itemTextColorActive: string;
            itemTextColorDisabled: string;
            itemColor: string;
            itemColorHover: string;
            itemColorPressed: string;
            itemColorActive: string;
            itemColorActiveHover: string;
            itemColorDisabled: string;
            itemBorder: string;
            itemBorderHover: string;
            itemBorderPressed: string;
            itemBorderActive: string;
            itemBorderDisabled: string;
            itemBorderRadius: string;
            itemSizeSmall: string;
            itemSizeMedium: string;
            itemSizeLarge: string;
            itemFontSizeSmall: string;
            itemFontSizeMedium: string;
            itemFontSizeLarge: string;
            jumperFontSizeSmall: string;
            jumperFontSizeMedium: string;
            jumperFontSizeLarge: string;
            jumperTextColor: string;
            jumperTextColorDisabled: string;
            itemPaddingSmall: string;
            itemMarginSmall: string;
            itemMarginSmallRtl: string;
            itemPaddingMedium: string;
            itemMarginMedium: string;
            itemMarginMediumRtl: string;
            itemPaddingLarge: string;
            itemMarginLarge: string;
            itemMarginLargeRtl: string;
            buttonIconSizeSmall: string;
            buttonIconSizeMedium: string;
            buttonIconSizeLarge: string;
            inputWidthSmall: string;
            selectWidthSmall: string;
            inputMarginSmall: string;
            inputMarginSmallRtl: string;
            selectMarginSmall: string;
            prefixMarginSmall: string;
            suffixMarginSmall: string;
            inputWidthMedium: string;
            selectWidthMedium: string;
            inputMarginMedium: string;
            inputMarginMediumRtl: string;
            selectMarginMedium: string;
            prefixMarginMedium: string;
            suffixMarginMedium: string;
            inputWidthLarge: string;
            selectWidthLarge: string;
            inputMarginLarge: string;
            inputMarginLargeRtl: string;
            selectMarginLarge: string;
            prefixMarginLarge: string;
            suffixMarginLarge: string;
        }, {
            Select: import("../../_mixins").Theme<"Select", {
                menuBoxShadow: string;
            }, {
                InternalSelection: import("../../_mixins").Theme<"InternalSelection", {
                    fontSizeTiny: string;
                    fontSizeSmall: string;
                    fontSizeMedium: string;
                    fontSizeLarge: string;
                    heightTiny: string;
                    heightSmall: string;
                    heightMedium: string;
                    heightLarge: string;
                    borderRadius: string;
                    fontWeight: string;
                    textColor: string;
                    textColorDisabled: string;
                    placeholderColor: string;
                    placeholderColorDisabled: string;
                    color: string;
                    colorDisabled: string;
                    colorActive: string;
                    border: string;
                    borderHover: string;
                    borderActive: string;
                    borderFocus: string;
                    boxShadowHover: string;
                    boxShadowActive: string;
                    boxShadowFocus: string;
                    caretColor: string;
                    arrowColor: string;
                    arrowColorDisabled: string;
                    loadingColor: string;
                    borderWarning: string;
                    borderHoverWarning: string;
                    borderActiveWarning: string;
                    borderFocusWarning: string;
                    boxShadowHoverWarning: string;
                    boxShadowActiveWarning: string;
                    boxShadowFocusWarning: string;
                    colorActiveWarning: string;
                    caretColorWarning: string;
                    borderError: string;
                    borderHoverError: string;
                    borderActiveError: string;
                    borderFocusError: string;
                    boxShadowHoverError: string;
                    boxShadowActiveError: string;
                    boxShadowFocusError: string;
                    colorActiveError: string;
                    caretColorError: string;
                    clearColor: string;
                    clearColorHover: string;
                    clearColorPressed: string;
                    paddingSingle: string;
                    paddingMultiple: string;
                    clearSize: string;
                    arrowSize: string;
                }, {
                    Popover: import("../../_mixins").Theme<"Popover", {
                        fontSize: string;
                        borderRadius: string;
                        color: string;
                        dividerColor: string;
                        textColor: string;
                        boxShadow: string;
                        space: string;
                        spaceArrow: string;
                        arrowOffset: string;
                        arrowOffsetVertical: string;
                        arrowHeight: string;
                        padding: string;
                    }, any>;
                }>;
                InternalSelectMenu: import("../../_mixins").Theme<"InternalSelectMenu", {
                    optionFontSizeTiny: string;
                    optionFontSizeSmall: string;
                    optionFontSizeMedium: string;
                    optionFontSizeLarge: string;
                    optionFontSizeHuge: string;
                    optionHeightTiny: string;
                    optionHeightSmall: string;
                    optionHeightMedium: string;
                    optionHeightLarge: string;
                    optionHeightHuge: string;
                    borderRadius: string;
                    color: string;
                    groupHeaderTextColor: string;
                    actionDividerColor: string;
                    optionTextColor: string;
                    optionTextColorPressed: string;
                    optionTextColorDisabled: string;
                    optionTextColorActive: string;
                    optionOpacityDisabled: string;
                    optionCheckColor: string;
                    optionColorPending: string;
                    optionColorActive: string;
                    optionColorActivePending: string;
                    actionTextColor: string;
                    loadingColor: string;
                    height: string;
                    paddingTiny: string;
                    paddingSmall: string;
                    paddingMedium: string;
                    paddingLarge: string;
                    paddingHuge: string;
                    optionPaddingTiny: string;
                    optionPaddingSmall: string;
                    optionPaddingMedium: string;
                    optionPaddingLarge: string;
                    optionPaddingHuge: string;
                    loadingSize: string;
                }, {
                    Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                        height: string;
                        width: string;
                        borderRadius: string;
                        color: string;
                        colorHover: string;
                        railInsetHorizontalBottom: string;
                        railInsetHorizontalTop: string;
                        railInsetVerticalRight: string;
                        railInsetVerticalLeft: string;
                        railColor: string;
                    }, any>;
                    Empty: import("../../_mixins").Theme<"Empty", {
                        fontSizeTiny: string;
                        fontSizeSmall: string;
                        fontSizeMedium: string;
                        fontSizeLarge: string;
                        fontSizeHuge: string;
                        textColor: string;
                        iconColor: string;
                        extraTextColor: string;
                        iconSizeTiny: string;
                        iconSizeSmall: string;
                        iconSizeMedium: string;
                        iconSizeLarge: string;
                        iconSizeHuge: string;
                    }, any>;
                }>;
            }>;
            Input: import("../../_mixins").Theme<"Input", {
                fontWeight: string;
                countTextColorDisabled: string;
                countTextColor: string;
                heightTiny: string;
                heightSmall: string;
                heightMedium: string;
                heightLarge: string;
                fontSizeTiny: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                lineHeight: string;
                lineHeightTextarea: string;
                borderRadius: string;
                iconSize: string;
                groupLabelColor: string;
                groupLabelTextColor: string;
                textColor: string;
                textColorDisabled: string;
                textDecorationColor: string;
                caretColor: string;
                placeholderColor: string;
                placeholderColorDisabled: string;
                color: string;
                colorDisabled: string;
                colorFocus: string;
                groupLabelBorder: string;
                border: string;
                borderHover: string;
                borderDisabled: string;
                borderFocus: string;
                boxShadowFocus: string;
                loadingColor: string;
                loadingColorWarning: string;
                borderWarning: string;
                borderHoverWarning: string;
                colorFocusWarning: string;
                borderFocusWarning: string;
                boxShadowFocusWarning: string;
                caretColorWarning: string;
                loadingColorError: string;
                borderError: string;
                borderHoverError: string;
                colorFocusError: string;
                borderFocusError: string;
                boxShadowFocusError: string;
                caretColorError: string;
                clearColor: string;
                clearColorHover: string;
                clearColorPressed: string;
                iconColor: string;
                iconColorDisabled: string;
                iconColorHover: string;
                iconColorPressed: string;
                suffixTextColor: string;
                paddingTiny: string;
                paddingSmall: string;
                paddingMedium: string;
                paddingLarge: string;
                clearSize: string;
            }, any>;
            Popselect: import("../../_mixins").Theme<"Popselect", {
                menuBoxShadow: string;
            }, {
                Popover: import("../../_mixins").Theme<"Popover", {
                    fontSize: string;
                    borderRadius: string;
                    color: string;
                    dividerColor: string;
                    textColor: string;
                    boxShadow: string;
                    space: string;
                    spaceArrow: string;
                    arrowOffset: string;
                    arrowOffsetVertical: string;
                    arrowHeight: string;
                    padding: string;
                }, any>;
                InternalSelectMenu: import("../../_mixins").Theme<"InternalSelectMenu", {
                    optionFontSizeTiny: string;
                    optionFontSizeSmall: string;
                    optionFontSizeMedium: string;
                    optionFontSizeLarge: string;
                    optionFontSizeHuge: string;
                    optionHeightTiny: string;
                    optionHeightSmall: string;
                    optionHeightMedium: string;
                    optionHeightLarge: string;
                    optionHeightHuge: string;
                    borderRadius: string;
                    color: string;
                    groupHeaderTextColor: string;
                    actionDividerColor: string;
                    optionTextColor: string;
                    optionTextColorPressed: string;
                    optionTextColorDisabled: string;
                    optionTextColorActive: string;
                    optionOpacityDisabled: string;
                    optionCheckColor: string;
                    optionColorPending: string;
                    optionColorActive: string;
                    optionColorActivePending: string;
                    actionTextColor: string;
                    loadingColor: string;
                    height: string;
                    paddingTiny: string;
                    paddingSmall: string;
                    paddingMedium: string;
                    paddingLarge: string;
                    paddingHuge: string;
                    optionPaddingTiny: string;
                    optionPaddingSmall: string;
                    optionPaddingMedium: string;
                    optionPaddingLarge: string;
                    optionPaddingHuge: string;
                    loadingSize: string;
                }, {
                    Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                        height: string;
                        width: string;
                        borderRadius: string;
                        color: string;
                        colorHover: string;
                        railInsetHorizontalBottom: string;
                        railInsetHorizontalTop: string;
                        railInsetVerticalRight: string;
                        railInsetVerticalLeft: string;
                        railColor: string;
                    }, any>;
                    Empty: import("../../_mixins").Theme<"Empty", {
                        fontSizeTiny: string;
                        fontSizeSmall: string;
                        fontSizeMedium: string;
                        fontSizeLarge: string;
                        fontSizeHuge: string;
                        textColor: string;
                        iconColor: string;
                        extraTextColor: string;
                        iconSizeTiny: string;
                        iconSizeSmall: string;
                        iconSizeMedium: string;
                        iconSizeLarge: string;
                        iconSizeHuge: string;
                    }, any>;
                }>;
            }>;
        }>;
        Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
            height: string;
            width: string;
            borderRadius: string;
            color: string;
            colorHover: string;
            railInsetHorizontalBottom: string;
            railInsetHorizontalTop: string;
            railInsetVerticalRight: string;
            railInsetVerticalLeft: string;
            railColor: string;
        }, any>;
        Empty: import("../../_mixins").Theme<"Empty", {
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            fontSizeHuge: string;
            textColor: string;
            iconColor: string;
            extraTextColor: string;
            iconSizeTiny: string;
            iconSizeSmall: string;
            iconSizeMedium: string;
            iconSizeLarge: string;
            iconSizeHuge: string;
        }, any>;
        Popover: import("../../_mixins").Theme<"Popover", {
            fontSize: string;
            borderRadius: string;
            color: string;
            dividerColor: string;
            textColor: string;
            boxShadow: string;
            space: string;
            spaceArrow: string;
            arrowOffset: string;
            arrowOffsetVertical: string;
            arrowHeight: string;
            padding: string;
        }, any>;
        Ellipsis: import("../../_mixins").Theme<"Ellipsis", unknown, {
            Tooltip: import("../../_mixins").Theme<"Tooltip", {
                borderRadius: string;
                boxShadow: string;
                color: string;
                textColor: string;
                padding: string;
            }, {
                Popover: import("../../_mixins").Theme<"Popover", {
                    fontSize: string;
                    borderRadius: string;
                    color: string;
                    dividerColor: string;
                    textColor: string;
                    boxShadow: string;
                    space: string;
                    spaceArrow: string;
                    arrowOffset: string;
                    arrowOffsetVertical: string;
                    arrowHeight: string;
                    padding: string;
                }, any>;
            }>;
        }>;
        Dropdown: import("../../_mixins").Theme<"Dropdown", {
            optionHeightSmall: string;
            optionHeightMedium: string;
            optionHeightLarge: string;
            optionHeightHuge: string;
            borderRadius: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            fontSizeHuge: string;
            optionTextColor: string;
            optionTextColorHover: string;
            optionTextColorActive: string;
            optionTextColorChildActive: string;
            color: string;
            dividerColor: string;
            suffixColor: string;
            prefixColor: string;
            optionColorHover: string;
            optionColorActive: string;
            groupHeaderTextColor: string;
            optionTextColorInverted: string;
            optionTextColorHoverInverted: string;
            optionTextColorActiveInverted: string;
            optionTextColorChildActiveInverted: string;
            colorInverted: string;
            dividerColorInverted: string;
            suffixColorInverted: string;
            prefixColorInverted: string;
            optionColorHoverInverted: string;
            optionColorActiveInverted: string;
            groupHeaderTextColorInverted: string;
            optionOpacityDisabled: string;
            padding: string;
            optionIconSizeSmall: string;
            optionIconSizeMedium: string;
            optionIconSizeLarge: string;
            optionIconSizeHuge: string;
            optionSuffixWidthSmall: string;
            optionSuffixWidthMedium: string;
            optionSuffixWidthLarge: string;
            optionSuffixWidthHuge: string;
            optionIconSuffixWidthSmall: string;
            optionIconSuffixWidthMedium: string;
            optionIconSuffixWidthLarge: string;
            optionIconSuffixWidthHuge: string;
            optionPrefixWidthSmall: string;
            optionPrefixWidthMedium: string;
            optionPrefixWidthLarge: string;
            optionPrefixWidthHuge: string;
            optionIconPrefixWidthSmall: string;
            optionIconPrefixWidthMedium: string;
            optionIconPrefixWidthLarge: string;
            optionIconPrefixWidthHuge: string;
        }, {
            Popover: import("../../_mixins").Theme<"Popover", {
                fontSize: string;
                borderRadius: string;
                color: string;
                dividerColor: string;
                textColor: string;
                boxShadow: string;
                space: string;
                spaceArrow: string;
                arrowOffset: string;
                arrowOffsetVertical: string;
                arrowHeight: string;
                padding: string;
            }, any>;
        }>;
    }>>>;
    readonly builtinThemeOverrides: import("vue").PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"DataTable", {
        actionDividerColor: string;
        lineHeight: string;
        borderRadius: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        borderColor: string;
        tdColorHover: string;
        tdColorSorting: string;
        tdColorStriped: string;
        thColor: string;
        thColorHover: string;
        thColorSorting: string;
        tdColor: string;
        tdTextColor: string;
        thTextColor: string;
        thFontWeight: string;
        thButtonColorHover: string;
        thIconColor: string;
        thIconColorActive: string;
        borderColorModal: string;
        tdColorHoverModal: string;
        tdColorSortingModal: string;
        tdColorStripedModal: string;
        thColorModal: string;
        thColorHoverModal: string;
        thColorSortingModal: string;
        tdColorModal: string;
        borderColorPopover: string;
        tdColorHoverPopover: string;
        tdColorSortingPopover: string;
        tdColorStripedPopover: string;
        thColorPopover: string;
        thColorHoverPopover: string;
        thColorSortingPopover: string;
        tdColorPopover: string;
        boxShadowBefore: string;
        boxShadowAfter: string;
        loadingColor: string;
        loadingSize: string;
        opacityLoading: string;
        thPaddingSmall: string;
        thPaddingMedium: string;
        thPaddingLarge: string;
        tdPaddingSmall: string;
        tdPaddingMedium: string;
        tdPaddingLarge: string;
        sorterSize: string;
        resizableContainerSize: string;
        resizableSize: string;
        filterSize: string;
        paginationMargin: string;
        emptyPadding: string;
        actionPadding: string;
        actionButtonMargin: string;
    }, {
        Button: import("../../_mixins").Theme<"Button", {
            heightTiny: string;
            heightSmall: string;
            heightMedium: string;
            heightLarge: string;
            borderRadiusTiny: string;
            borderRadiusSmall: string;
            borderRadiusMedium: string;
            borderRadiusLarge: string;
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            opacityDisabled: string;
            colorOpacitySecondary: string;
            colorOpacitySecondaryHover: string;
            colorOpacitySecondaryPressed: string;
            colorSecondary: string;
            colorSecondaryHover: string;
            colorSecondaryPressed: string;
            colorTertiary: string;
            colorTertiaryHover: string;
            colorTertiaryPressed: string;
            colorQuaternary: string;
            colorQuaternaryHover: string;
            colorQuaternaryPressed: string;
            color: string;
            colorHover: string;
            colorPressed: string;
            colorFocus: string;
            colorDisabled: string;
            textColor: string;
            textColorTertiary: string;
            textColorHover: string;
            textColorPressed: string;
            textColorFocus: string;
            textColorDisabled: string;
            textColorText: string;
            textColorTextHover: string;
            textColorTextPressed: string;
            textColorTextFocus: string;
            textColorTextDisabled: string;
            textColorGhost: string;
            textColorGhostHover: string;
            textColorGhostPressed: string;
            textColorGhostFocus: string;
            textColorGhostDisabled: string;
            border: string;
            borderHover: string;
            borderPressed: string;
            borderFocus: string;
            borderDisabled: string;
            rippleColor: string;
            colorPrimary: string;
            colorHoverPrimary: string;
            colorPressedPrimary: string;
            colorFocusPrimary: string;
            colorDisabledPrimary: string;
            textColorPrimary: string;
            textColorHoverPrimary: string;
            textColorPressedPrimary: string;
            textColorFocusPrimary: string;
            textColorDisabledPrimary: string;
            textColorTextPrimary: string;
            textColorTextHoverPrimary: string;
            textColorTextPressedPrimary: string;
            textColorTextFocusPrimary: string;
            textColorTextDisabledPrimary: string;
            textColorGhostPrimary: string;
            textColorGhostHoverPrimary: string;
            textColorGhostPressedPrimary: string;
            textColorGhostFocusPrimary: string;
            textColorGhostDisabledPrimary: string;
            borderPrimary: string;
            borderHoverPrimary: string;
            borderPressedPrimary: string;
            borderFocusPrimary: string;
            borderDisabledPrimary: string;
            rippleColorPrimary: string;
            colorInfo: string;
            colorHoverInfo: string;
            colorPressedInfo: string;
            colorFocusInfo: string;
            colorDisabledInfo: string;
            textColorInfo: string;
            textColorHoverInfo: string;
            textColorPressedInfo: string;
            textColorFocusInfo: string;
            textColorDisabledInfo: string;
            textColorTextInfo: string;
            textColorTextHoverInfo: string;
            textColorTextPressedInfo: string;
            textColorTextFocusInfo: string;
            textColorTextDisabledInfo: string;
            textColorGhostInfo: string;
            textColorGhostHoverInfo: string;
            textColorGhostPressedInfo: string;
            textColorGhostFocusInfo: string;
            textColorGhostDisabledInfo: string;
            borderInfo: string;
            borderHoverInfo: string;
            borderPressedInfo: string;
            borderFocusInfo: string;
            borderDisabledInfo: string;
            rippleColorInfo: string;
            colorSuccess: string;
            colorHoverSuccess: string;
            colorPressedSuccess: string;
            colorFocusSuccess: string;
            colorDisabledSuccess: string;
            textColorSuccess: string;
            textColorHoverSuccess: string;
            textColorPressedSuccess: string;
            textColorFocusSuccess: string;
            textColorDisabledSuccess: string;
            textColorTextSuccess: string;
            textColorTextHoverSuccess: string;
            textColorTextPressedSuccess: string;
            textColorTextFocusSuccess: string;
            textColorTextDisabledSuccess: string;
            textColorGhostSuccess: string;
            textColorGhostHoverSuccess: string;
            textColorGhostPressedSuccess: string;
            textColorGhostFocusSuccess: string;
            textColorGhostDisabledSuccess: string;
            borderSuccess: string;
            borderHoverSuccess: string;
            borderPressedSuccess: string;
            borderFocusSuccess: string;
            borderDisabledSuccess: string;
            rippleColorSuccess: string;
            colorWarning: string;
            colorHoverWarning: string;
            colorPressedWarning: string;
            colorFocusWarning: string;
            colorDisabledWarning: string;
            textColorWarning: string;
            textColorHoverWarning: string;
            textColorPressedWarning: string;
            textColorFocusWarning: string;
            textColorDisabledWarning: string;
            textColorTextWarning: string;
            textColorTextHoverWarning: string;
            textColorTextPressedWarning: string;
            textColorTextFocusWarning: string;
            textColorTextDisabledWarning: string;
            textColorGhostWarning: string;
            textColorGhostHoverWarning: string;
            textColorGhostPressedWarning: string;
            textColorGhostFocusWarning: string;
            textColorGhostDisabledWarning: string;
            borderWarning: string;
            borderHoverWarning: string;
            borderPressedWarning: string;
            borderFocusWarning: string;
            borderDisabledWarning: string;
            rippleColorWarning: string;
            colorError: string;
            colorHoverError: string;
            colorPressedError: string;
            colorFocusError: string;
            colorDisabledError: string;
            textColorError: string;
            textColorHoverError: string;
            textColorPressedError: string;
            textColorFocusError: string;
            textColorDisabledError: string;
            textColorTextError: string;
            textColorTextHoverError: string;
            textColorTextPressedError: string;
            textColorTextFocusError: string;
            textColorTextDisabledError: string;
            textColorGhostError: string;
            textColorGhostHoverError: string;
            textColorGhostPressedError: string;
            textColorGhostFocusError: string;
            textColorGhostDisabledError: string;
            borderError: string;
            borderHoverError: string;
            borderPressedError: string;
            borderFocusError: string;
            borderDisabledError: string;
            rippleColorError: string;
            waveOpacity: string;
            fontWeight: string;
            fontWeightStrong: string;
            paddingTiny: string;
            paddingSmall: string;
            paddingMedium: string;
            paddingLarge: string;
            paddingRoundTiny: string;
            paddingRoundSmall: string;
            paddingRoundMedium: string;
            paddingRoundLarge: string;
            iconMarginTiny: string;
            iconMarginSmall: string;
            iconMarginMedium: string;
            iconMarginLarge: string;
            iconSizeTiny: string;
            iconSizeSmall: string;
            iconSizeMedium: string;
            iconSizeLarge: string;
            rippleDuration: string;
        }, any>;
        Checkbox: import("../../_mixins").Theme<"Checkbox", {
            labelLineHeight: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            borderRadius: string;
            color: string;
            colorChecked: string;
            colorDisabled: string;
            colorDisabledChecked: string;
            colorTableHeader: string;
            colorTableHeaderModal: string;
            colorTableHeaderPopover: string;
            checkMarkColor: string;
            checkMarkColorDisabled: string;
            checkMarkColorDisabledChecked: string;
            border: string;
            borderDisabled: string;
            borderDisabledChecked: string;
            borderChecked: string;
            borderFocus: string;
            boxShadowFocus: string;
            textColor: string;
            textColorDisabled: string;
            sizeSmall: string;
            sizeMedium: string;
            sizeLarge: string;
            labelPadding: string;
            labelFontWeight: string;
        }, any>;
        Radio: import("../../_mixins").Theme<"Radio", {
            labelLineHeight: string;
            buttonHeightSmall: string;
            buttonHeightMedium: string;
            buttonHeightLarge: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            boxShadow: string;
            boxShadowActive: string;
            boxShadowFocus: string;
            boxShadowHover: string;
            boxShadowDisabled: string;
            color: string;
            colorDisabled: string;
            colorActive: string;
            textColor: string;
            textColorDisabled: string;
            dotColorActive: string;
            dotColorDisabled: string;
            buttonBorderColor: string;
            buttonBorderColorActive: string;
            buttonBorderColorHover: string;
            buttonColor: string;
            buttonColorActive: string;
            buttonTextColor: string;
            buttonTextColorActive: string;
            buttonTextColorHover: string;
            opacityDisabled: string;
            buttonBoxShadowFocus: string;
            buttonBoxShadowHover: string;
            buttonBoxShadow: string;
            buttonBorderRadius: string;
            radioSizeSmall: string;
            radioSizeMedium: string;
            radioSizeLarge: string;
            labelPadding: string;
            labelFontWeight: string;
        }, any>;
        Pagination: import("../../_mixins").Theme<"Pagination", {
            buttonColor: string;
            buttonColorHover: string;
            buttonColorPressed: string;
            buttonBorder: string;
            buttonBorderHover: string;
            buttonBorderPressed: string;
            buttonIconColor: string;
            buttonIconColorHover: string;
            buttonIconColorPressed: string;
            itemTextColor: string;
            itemTextColorHover: string;
            itemTextColorPressed: string;
            itemTextColorActive: string;
            itemTextColorDisabled: string;
            itemColor: string;
            itemColorHover: string;
            itemColorPressed: string;
            itemColorActive: string;
            itemColorActiveHover: string;
            itemColorDisabled: string;
            itemBorder: string;
            itemBorderHover: string;
            itemBorderPressed: string;
            itemBorderActive: string;
            itemBorderDisabled: string;
            itemBorderRadius: string;
            itemSizeSmall: string;
            itemSizeMedium: string;
            itemSizeLarge: string;
            itemFontSizeSmall: string;
            itemFontSizeMedium: string;
            itemFontSizeLarge: string;
            jumperFontSizeSmall: string;
            jumperFontSizeMedium: string;
            jumperFontSizeLarge: string;
            jumperTextColor: string;
            jumperTextColorDisabled: string;
            itemPaddingSmall: string;
            itemMarginSmall: string;
            itemMarginSmallRtl: string;
            itemPaddingMedium: string;
            itemMarginMedium: string;
            itemMarginMediumRtl: string;
            itemPaddingLarge: string;
            itemMarginLarge: string;
            itemMarginLargeRtl: string;
            buttonIconSizeSmall: string;
            buttonIconSizeMedium: string;
            buttonIconSizeLarge: string;
            inputWidthSmall: string;
            selectWidthSmall: string;
            inputMarginSmall: string;
            inputMarginSmallRtl: string;
            selectMarginSmall: string;
            prefixMarginSmall: string;
            suffixMarginSmall: string;
            inputWidthMedium: string;
            selectWidthMedium: string;
            inputMarginMedium: string;
            inputMarginMediumRtl: string;
            selectMarginMedium: string;
            prefixMarginMedium: string;
            suffixMarginMedium: string;
            inputWidthLarge: string;
            selectWidthLarge: string;
            inputMarginLarge: string;
            inputMarginLargeRtl: string;
            selectMarginLarge: string;
            prefixMarginLarge: string;
            suffixMarginLarge: string;
        }, {
            Select: import("../../_mixins").Theme<"Select", {
                menuBoxShadow: string;
            }, {
                InternalSelection: import("../../_mixins").Theme<"InternalSelection", {
                    fontSizeTiny: string;
                    fontSizeSmall: string;
                    fontSizeMedium: string;
                    fontSizeLarge: string;
                    heightTiny: string;
                    heightSmall: string;
                    heightMedium: string;
                    heightLarge: string;
                    borderRadius: string;
                    fontWeight: string;
                    textColor: string;
                    textColorDisabled: string;
                    placeholderColor: string;
                    placeholderColorDisabled: string;
                    color: string;
                    colorDisabled: string;
                    colorActive: string;
                    border: string;
                    borderHover: string;
                    borderActive: string;
                    borderFocus: string;
                    boxShadowHover: string;
                    boxShadowActive: string;
                    boxShadowFocus: string;
                    caretColor: string;
                    arrowColor: string;
                    arrowColorDisabled: string;
                    loadingColor: string;
                    borderWarning: string;
                    borderHoverWarning: string;
                    borderActiveWarning: string;
                    borderFocusWarning: string;
                    boxShadowHoverWarning: string;
                    boxShadowActiveWarning: string;
                    boxShadowFocusWarning: string;
                    colorActiveWarning: string;
                    caretColorWarning: string;
                    borderError: string;
                    borderHoverError: string;
                    borderActiveError: string;
                    borderFocusError: string;
                    boxShadowHoverError: string;
                    boxShadowActiveError: string;
                    boxShadowFocusError: string;
                    colorActiveError: string;
                    caretColorError: string;
                    clearColor: string;
                    clearColorHover: string;
                    clearColorPressed: string;
                    paddingSingle: string;
                    paddingMultiple: string;
                    clearSize: string;
                    arrowSize: string;
                }, {
                    Popover: import("../../_mixins").Theme<"Popover", {
                        fontSize: string;
                        borderRadius: string;
                        color: string;
                        dividerColor: string;
                        textColor: string;
                        boxShadow: string;
                        space: string;
                        spaceArrow: string;
                        arrowOffset: string;
                        arrowOffsetVertical: string;
                        arrowHeight: string;
                        padding: string;
                    }, any>;
                }>;
                InternalSelectMenu: import("../../_mixins").Theme<"InternalSelectMenu", {
                    optionFontSizeTiny: string;
                    optionFontSizeSmall: string;
                    optionFontSizeMedium: string;
                    optionFontSizeLarge: string;
                    optionFontSizeHuge: string;
                    optionHeightTiny: string;
                    optionHeightSmall: string;
                    optionHeightMedium: string;
                    optionHeightLarge: string;
                    optionHeightHuge: string;
                    borderRadius: string;
                    color: string;
                    groupHeaderTextColor: string;
                    actionDividerColor: string;
                    optionTextColor: string;
                    optionTextColorPressed: string;
                    optionTextColorDisabled: string;
                    optionTextColorActive: string;
                    optionOpacityDisabled: string;
                    optionCheckColor: string;
                    optionColorPending: string;
                    optionColorActive: string;
                    optionColorActivePending: string;
                    actionTextColor: string;
                    loadingColor: string;
                    height: string;
                    paddingTiny: string;
                    paddingSmall: string;
                    paddingMedium: string;
                    paddingLarge: string;
                    paddingHuge: string;
                    optionPaddingTiny: string;
                    optionPaddingSmall: string;
                    optionPaddingMedium: string;
                    optionPaddingLarge: string;
                    optionPaddingHuge: string;
                    loadingSize: string;
                }, {
                    Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                        height: string;
                        width: string;
                        borderRadius: string;
                        color: string;
                        colorHover: string;
                        railInsetHorizontalBottom: string;
                        railInsetHorizontalTop: string;
                        railInsetVerticalRight: string;
                        railInsetVerticalLeft: string;
                        railColor: string;
                    }, any>;
                    Empty: import("../../_mixins").Theme<"Empty", {
                        fontSizeTiny: string;
                        fontSizeSmall: string;
                        fontSizeMedium: string;
                        fontSizeLarge: string;
                        fontSizeHuge: string;
                        textColor: string;
                        iconColor: string;
                        extraTextColor: string;
                        iconSizeTiny: string;
                        iconSizeSmall: string;
                        iconSizeMedium: string;
                        iconSizeLarge: string;
                        iconSizeHuge: string;
                    }, any>;
                }>;
            }>;
            Input: import("../../_mixins").Theme<"Input", {
                fontWeight: string;
                countTextColorDisabled: string;
                countTextColor: string;
                heightTiny: string;
                heightSmall: string;
                heightMedium: string;
                heightLarge: string;
                fontSizeTiny: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                lineHeight: string;
                lineHeightTextarea: string;
                borderRadius: string;
                iconSize: string;
                groupLabelColor: string;
                groupLabelTextColor: string;
                textColor: string;
                textColorDisabled: string;
                textDecorationColor: string;
                caretColor: string;
                placeholderColor: string;
                placeholderColorDisabled: string;
                color: string;
                colorDisabled: string;
                colorFocus: string;
                groupLabelBorder: string;
                border: string;
                borderHover: string;
                borderDisabled: string;
                borderFocus: string;
                boxShadowFocus: string;
                loadingColor: string;
                loadingColorWarning: string;
                borderWarning: string;
                borderHoverWarning: string;
                colorFocusWarning: string;
                borderFocusWarning: string;
                boxShadowFocusWarning: string;
                caretColorWarning: string;
                loadingColorError: string;
                borderError: string;
                borderHoverError: string;
                colorFocusError: string;
                borderFocusError: string;
                boxShadowFocusError: string;
                caretColorError: string;
                clearColor: string;
                clearColorHover: string;
                clearColorPressed: string;
                iconColor: string;
                iconColorDisabled: string;
                iconColorHover: string;
                iconColorPressed: string;
                suffixTextColor: string;
                paddingTiny: string;
                paddingSmall: string;
                paddingMedium: string;
                paddingLarge: string;
                clearSize: string;
            }, any>;
            Popselect: import("../../_mixins").Theme<"Popselect", {
                menuBoxShadow: string;
            }, {
                Popover: import("../../_mixins").Theme<"Popover", {
                    fontSize: string;
                    borderRadius: string;
                    color: string;
                    dividerColor: string;
                    textColor: string;
                    boxShadow: string;
                    space: string;
                    spaceArrow: string;
                    arrowOffset: string;
                    arrowOffsetVertical: string;
                    arrowHeight: string;
                    padding: string;
                }, any>;
                InternalSelectMenu: import("../../_mixins").Theme<"InternalSelectMenu", {
                    optionFontSizeTiny: string;
                    optionFontSizeSmall: string;
                    optionFontSizeMedium: string;
                    optionFontSizeLarge: string;
                    optionFontSizeHuge: string;
                    optionHeightTiny: string;
                    optionHeightSmall: string;
                    optionHeightMedium: string;
                    optionHeightLarge: string;
                    optionHeightHuge: string;
                    borderRadius: string;
                    color: string;
                    groupHeaderTextColor: string;
                    actionDividerColor: string;
                    optionTextColor: string;
                    optionTextColorPressed: string;
                    optionTextColorDisabled: string;
                    optionTextColorActive: string;
                    optionOpacityDisabled: string;
                    optionCheckColor: string;
                    optionColorPending: string;
                    optionColorActive: string;
                    optionColorActivePending: string;
                    actionTextColor: string;
                    loadingColor: string;
                    height: string;
                    paddingTiny: string;
                    paddingSmall: string;
                    paddingMedium: string;
                    paddingLarge: string;
                    paddingHuge: string;
                    optionPaddingTiny: string;
                    optionPaddingSmall: string;
                    optionPaddingMedium: string;
                    optionPaddingLarge: string;
                    optionPaddingHuge: string;
                    loadingSize: string;
                }, {
                    Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                        height: string;
                        width: string;
                        borderRadius: string;
                        color: string;
                        colorHover: string;
                        railInsetHorizontalBottom: string;
                        railInsetHorizontalTop: string;
                        railInsetVerticalRight: string;
                        railInsetVerticalLeft: string;
                        railColor: string;
                    }, any>;
                    Empty: import("../../_mixins").Theme<"Empty", {
                        fontSizeTiny: string;
                        fontSizeSmall: string;
                        fontSizeMedium: string;
                        fontSizeLarge: string;
                        fontSizeHuge: string;
                        textColor: string;
                        iconColor: string;
                        extraTextColor: string;
                        iconSizeTiny: string;
                        iconSizeSmall: string;
                        iconSizeMedium: string;
                        iconSizeLarge: string;
                        iconSizeHuge: string;
                    }, any>;
                }>;
            }>;
        }>;
        Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
            height: string;
            width: string;
            borderRadius: string;
            color: string;
            colorHover: string;
            railInsetHorizontalBottom: string;
            railInsetHorizontalTop: string;
            railInsetVerticalRight: string;
            railInsetVerticalLeft: string;
            railColor: string;
        }, any>;
        Empty: import("../../_mixins").Theme<"Empty", {
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            fontSizeHuge: string;
            textColor: string;
            iconColor: string;
            extraTextColor: string;
            iconSizeTiny: string;
            iconSizeSmall: string;
            iconSizeMedium: string;
            iconSizeLarge: string;
            iconSizeHuge: string;
        }, any>;
        Popover: import("../../_mixins").Theme<"Popover", {
            fontSize: string;
            borderRadius: string;
            color: string;
            dividerColor: string;
            textColor: string;
            boxShadow: string;
            space: string;
            spaceArrow: string;
            arrowOffset: string;
            arrowOffsetVertical: string;
            arrowHeight: string;
            padding: string;
        }, any>;
        Ellipsis: import("../../_mixins").Theme<"Ellipsis", unknown, {
            Tooltip: import("../../_mixins").Theme<"Tooltip", {
                borderRadius: string;
                boxShadow: string;
                color: string;
                textColor: string;
                padding: string;
            }, {
                Popover: import("../../_mixins").Theme<"Popover", {
                    fontSize: string;
                    borderRadius: string;
                    color: string;
                    dividerColor: string;
                    textColor: string;
                    boxShadow: string;
                    space: string;
                    spaceArrow: string;
                    arrowOffset: string;
                    arrowOffsetVertical: string;
                    arrowHeight: string;
                    padding: string;
                }, any>;
            }>;
        }>;
        Dropdown: import("../../_mixins").Theme<"Dropdown", {
            optionHeightSmall: string;
            optionHeightMedium: string;
            optionHeightLarge: string;
            optionHeightHuge: string;
            borderRadius: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            fontSizeHuge: string;
            optionTextColor: string;
            optionTextColorHover: string;
            optionTextColorActive: string;
            optionTextColorChildActive: string;
            color: string;
            dividerColor: string;
            suffixColor: string;
            prefixColor: string;
            optionColorHover: string;
            optionColorActive: string;
            groupHeaderTextColor: string;
            optionTextColorInverted: string;
            optionTextColorHoverInverted: string;
            optionTextColorActiveInverted: string;
            optionTextColorChildActiveInverted: string;
            colorInverted: string;
            dividerColorInverted: string;
            suffixColorInverted: string;
            prefixColorInverted: string;
            optionColorHoverInverted: string;
            optionColorActiveInverted: string;
            groupHeaderTextColorInverted: string;
            optionOpacityDisabled: string;
            padding: string;
            optionIconSizeSmall: string;
            optionIconSizeMedium: string;
            optionIconSizeLarge: string;
            optionIconSizeHuge: string;
            optionSuffixWidthSmall: string;
            optionSuffixWidthMedium: string;
            optionSuffixWidthLarge: string;
            optionSuffixWidthHuge: string;
            optionIconSuffixWidthSmall: string;
            optionIconSuffixWidthMedium: string;
            optionIconSuffixWidthLarge: string;
            optionIconSuffixWidthHuge: string;
            optionPrefixWidthSmall: string;
            optionPrefixWidthMedium: string;
            optionPrefixWidthLarge: string;
            optionPrefixWidthHuge: string;
            optionIconPrefixWidthSmall: string;
            optionIconPrefixWidthMedium: string;
            optionIconPrefixWidthLarge: string;
            optionIconPrefixWidthHuge: string;
        }, {
            Popover: import("../../_mixins").Theme<"Popover", {
                fontSize: string;
                borderRadius: string;
                color: string;
                dividerColor: string;
                textColor: string;
                boxShadow: string;
                space: string;
                spaceArrow: string;
                arrowOffset: string;
                arrowOffsetVertical: string;
                arrowHeight: string;
                padding: string;
            }, any>;
        }>;
    }>>>;
}>> & Readonly<{}>, {
    readonly data: import("./interface").RowData[];
    readonly size: "small" | "medium" | "large";
    readonly tableLayout: "fixed" | "auto";
    readonly columns: import("./interface").TableColumns<any>;
    readonly loading: boolean;
    readonly bordered: boolean | undefined;
    readonly virtualScroll: boolean;
    readonly remote: boolean;
    readonly pagination: false | import("../../pagination").PaginationProps;
    readonly paginateSinglePage: boolean;
    readonly bottomBordered: boolean | undefined;
    readonly striped: boolean;
    readonly defaultCheckedRowKeys: RowKey[];
    readonly singleLine: boolean;
    readonly singleColumn: boolean;
    readonly defaultExpandedRowKeys: RowKey[];
    readonly defaultExpandAll: boolean;
    readonly stickyExpandedRows: boolean;
    readonly virtualScrollX: boolean;
    readonly virtualScrollHeader: boolean;
    readonly headerHeight: number;
    readonly minRowHeight: number;
    readonly allowCheckingNotLoaded: boolean;
    readonly cascade: boolean;
    readonly childrenKey: string;
    readonly indent: number;
    readonly flexHeight: boolean;
    readonly summaryPlacement: "top" | "bottom";
    readonly paginationBehaviorOnFilter: "first" | "current";
    readonly spinProps: import("../../_internal").BaseLoadingExposedProps;
}, SlotsType<DataTableSlots>, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;

import { type PropType } from 'vue';
declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    appear: BooleanConstructor;
    group: BooleanConstructor;
    mode: PropType<"in-out" | "out-in" | "default">;
    onLeave: FunctionConstructor;
    onAfterLeave: FunctionConstructor;
    onAfterEnter: FunctionConstructor;
    width: BooleanConstructor;
    reverse: BooleanConstructor;
}>, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
    [key: string]: any;
}>, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    appear: BooleanConstructor;
    group: BooleanConstructor;
    mode: PropType<"in-out" | "out-in" | "default">;
    onLeave: FunctionConstructor;
    onAfterLeave: FunctionConstructor;
    onAfterEnter: FunctionConstructor;
    width: BooleanConstructor;
    reverse: BooleanConstructor;
}>> & Readonly<{}>, {
    reverse: boolean;
    appear: boolean;
    width: boolean;
    group: boolean;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;

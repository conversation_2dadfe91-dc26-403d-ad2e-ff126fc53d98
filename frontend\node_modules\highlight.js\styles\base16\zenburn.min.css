/*!
  Theme: <PERSON>burn
  Author: elnawe
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/pre code.hljs{display:block;overflow-x:auto;padding:1em}code.hljs{padding:3px 5px}.hljs{color:#dcdccc;background:#383838}.hljs ::selection,.hljs::selection{background-color:#606060;color:#dcdccc}.hljs-comment{color:#6f6f6f}.hljs-tag{color:grey}.hljs-operator,.hljs-punctuation,.hljs-subst{color:#dcdccc}.hljs-operator{opacity:.7}.hljs-bullet,.hljs-deletion,.hljs-name,.hljs-selector-tag,.hljs-template-variable,.hljs-variable{color:#dca3a3}.hljs-attr,.hljs-link,.hljs-literal,.hljs-number,.hljs-symbol,.hljs-variable.constant_{color:#dfaf8f}.hljs-class .hljs-title,.hljs-title,.hljs-title.class_{color:#e0cf9f}.hljs-strong{font-weight:700;color:#e0cf9f}.hljs-addition,.hljs-code,.hljs-string,.hljs-title.class_.inherited__{color:#5f7f5f}.hljs-built_in,.hljs-doctag,.hljs-keyword.hljs-atrule,.hljs-quote,.hljs-regexp{color:#93e0e3}.hljs-attribute,.hljs-function .hljs-title,.hljs-section,.hljs-title.function_,.ruby .hljs-property{color:#7cb8bb}.diff .hljs-meta,.hljs-keyword,.hljs-template-tag,.hljs-type{color:#dc8cc3}.hljs-emphasis{color:#dc8cc3;font-style:italic}.hljs-meta,.hljs-meta .hljs-keyword,.hljs-meta .hljs-string{color:#000}.hljs-meta .hljs-keyword,.hljs-meta-keyword{font-weight:700}
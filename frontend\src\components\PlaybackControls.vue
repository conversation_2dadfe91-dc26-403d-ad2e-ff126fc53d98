<template>
  <div class="playback-controls">
    <div class="controls-left">
      <!-- 播放/暂停按钮 -->
      <button
        class="control-btn play-btn"
        @click="togglePlayback"
        :disabled="!hasContent"
        :class="{ loading: isStreamLoading }"
      >
        <span v-if="isStreamLoading" class="loading-icon">⏳</span>
        <span v-else>{{ isPlaying ? '⏸️' : '▶️' }}</span>
      </button>
      
      <!-- 停止按钮 -->
      <button 
        class="control-btn"
        @click="stop"
        :disabled="!hasContent"
      >
        ⏹️
      </button>
      
      <!-- 跳转到开始 -->
      <button 
        class="control-btn"
        @click="goToStart"
        :disabled="!hasContent"
      >
        ⏮️
      </button>
      
      <!-- 跳转到结束 -->
      <button 
        class="control-btn"
        @click="goToEnd"
        :disabled="!hasContent"
      >
        ⏭️
      </button>
    </div>
    
    <div class="controls-center">
      <!-- 时间显示 -->
      <div class="time-display">
        <span class="current-time">{{ formatTime(currentTime) }}</span>
        <span class="time-separator">/</span>
        <span class="total-time">{{ formatTime(timelineDuration) }}</span>
      </div>
      
      <!-- 进度条 -->
      <div class="progress-container">
        <div 
          class="progress-bar"
          @click="handleProgressClick"
          ref="progressBar"
        >
          <div 
            class="progress-fill"
            :style="{ width: progressPercentage + '%' }"
          ></div>
          <div 
            class="progress-handle"
            :style="{ left: progressPercentage + '%' }"
            @mousedown="startDragProgress"
          ></div>
        </div>
      </div>
    </div>
    
    <div class="controls-right">
      <!-- 音量控制 -->
      <div class="volume-control">
        <button class="control-btn volume-btn" @click="toggleMute">
          {{ isMuted ? '🔇' : '🔊' }}
        </button>
        <div class="volume-slider">
          <input
            type="range"
            min="0"
            max="100"
            v-model="volume"
            class="volume-input"
          />
        </div>
      </div>
      
      <!-- 缩放控制 -->
      <div class="zoom-control">
        <span class="zoom-label">缩放:</span>
        <button class="control-btn zoom-btn" @click="zoomOut">-</button>
        <span class="zoom-value">{{ Math.round(zoom * 100) }}%</span>
        <button class="control-btn zoom-btn" @click="zoomIn">+</button>
      </div>

      <!-- 调试按钮 -->
      <button class="control-btn debug-btn" @click="printTimelineConfig" title="打印时间轴配置">
        🐛 调试
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useEditorStore } from '@/stores/editor'
import { storeToRefs } from 'pinia'

const editorStore = useEditorStore()
const {
  isPlaying,
  currentTime,
  timelineDuration,
  zoom,
  videoTrack,
  tracks,
  mediaLibrary
} = storeToRefs(editorStore)

const progressBar = ref<HTMLElement>()
const volume = ref(100)
const isMuted = ref(false)
const isDraggingProgress = ref(false)
const isStreamLoading = ref(false)

// 是否有内容可以播放
const hasContent = computed(() => {
  return videoTrack.value && videoTrack.value.clips.length > 0
})

// 进度百分比
const progressPercentage = computed(() => {
  if (timelineDuration.value === 0) return 0
  return (currentTime.value / timelineDuration.value) * 100
})

// 格式化时间显示
const formatTime = (seconds: number): string => {
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

// 切换播放/暂停
const togglePlayback = async () => {
  if (!hasContent.value) return

  // 如果要开始播放，显示加载状态
  if (!isPlaying.value) {
    isStreamLoading.value = true

    // 如果当前时间没有对应的片段，尝试跳转到最近的片段
    if (!editorStore.currentClip) {
      const firstClip = videoTrack.value?.clips[0]
      if (firstClip) {
        editorStore.setCurrentTime(firstClip.startTime)
        // 等待一帧后再播放，确保 currentClip 已更新
        setTimeout(() => {
          editorStore.setPlaying(true)
          // 2-3秒后隐藏加载状态
          setTimeout(() => {
            isStreamLoading.value = false
          }, 3000)
        }, 50)
        return
      }
    }

    editorStore.setPlaying(true)

    // 2-3秒后隐藏加载状态
    setTimeout(() => {
      isStreamLoading.value = false
    }, 3000)
  } else {
    // 暂停播放
    isStreamLoading.value = false
    editorStore.setPlaying(false)
  }
}

// 停止播放
const stop = () => {
  editorStore.setPlaying(false)
  editorStore.setCurrentTime(0)
}

// 跳转到开始
const goToStart = () => {
  editorStore.setCurrentTime(0)
}

// 跳转到结束
const goToEnd = () => {
  editorStore.setCurrentTime(timelineDuration.value)
}

// 处理进度条点击
const handleProgressClick = (event: MouseEvent) => {
  if (!progressBar.value || !hasContent.value) return
  
  const rect = progressBar.value.getBoundingClientRect()
  const x = event.clientX - rect.left
  const percentage = x / rect.width
  const newTime = percentage * timelineDuration.value
  
  editorStore.setCurrentTime(newTime)
}

// 开始拖拽进度条
const startDragProgress = (event: MouseEvent) => {
  if (!hasContent.value) return
  
  event.preventDefault()
  isDraggingProgress.value = true
  
  const handleMouseMove = (e: MouseEvent) => {
    if (!isDraggingProgress.value || !progressBar.value) return
    
    const rect = progressBar.value.getBoundingClientRect()
    const x = Math.max(0, Math.min(e.clientX - rect.left, rect.width))
    const percentage = x / rect.width
    const newTime = percentage * timelineDuration.value
    
    editorStore.setCurrentTime(newTime)
  }
  
  const handleMouseUp = () => {
    isDraggingProgress.value = false
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
  }
  
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
}

// 切换静音
const toggleMute = () => {
  isMuted.value = !isMuted.value
}

// 缩放控制
const zoomIn = () => {
  editorStore.setZoom(zoom.value * 1.2)
}

const zoomOut = () => {
  editorStore.setZoom(zoom.value / 1.2)
}

// 打印时间轴配置
const printTimelineConfig = () => {
  const config = {
    // 基本信息
    currentTime: currentTime.value,
    timelineDuration: timelineDuration.value,
    isPlaying: isPlaying.value,
    zoom: zoom.value,

    // 轨道信息
    tracks: tracks.value.map(track => ({
      id: track.id,
      type: track.type,
      clipsCount: track.clips.length,
      clips: track.clips.map(clip => ({
        id: clip.id,
        mediaId: clip.mediaId,
        startTime: clip.startTime,
        duration: clip.duration,
        trimStart: clip.trimStart,
        trimEnd: clip.trimEnd,
        // 添加媒体信息
        mediaInfo: mediaLibrary.value.find(m => m.id === clip.mediaId)
      }))
    })),

    // 素材库信息
    mediaLibrary: mediaLibrary.value.map(media => ({
      id: media.id,
      name: media.name,
      duration: media.duration,
      file_size: media.file_size,
      proxy_url: media.proxy_url,
      thumbnail_url: media.thumbnail_url
    }))
  }

  console.log('=== 时间轴配置 ===')
  console.log(JSON.stringify(config, null, 2))

  // 也在页面上显示一个简化版本
  alert(`时间轴配置已打印到控制台！

简要信息：
- 当前时间: ${config.currentTime.toFixed(2)}s
- 总时长: ${config.timelineDuration.toFixed(2)}s
- 轨道数: ${config.tracks.length}
- 片段总数: ${config.tracks.reduce((sum, track) => sum + track.clipsCount, 0)}
- 素材数: ${config.mediaLibrary.length}

详细信息请查看浏览器控制台 (F12)`)
}
</script>

<style scoped>
.playback-controls {
  height: 100%;
  display: flex;
  align-items: center;
  padding: 0 16px;
  background-color: #2a2a2a;
  border-top: 1px solid #333;
}

.controls-left,
.controls-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.controls-center {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 16px;
  margin: 0 24px;
}

.control-btn {
  background: none;
  border: 1px solid #555;
  color: #fff;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.control-btn:hover:not(:disabled) {
  background-color: #404040;
  border-color: #666;
}

.control-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.play-btn {
  background-color: #007acc;
  border-color: #007acc;
}

.play-btn:hover:not(:disabled) {
  background-color: #0066aa;
}

.play-btn.loading {
  background-color: #ffa500;
  border-color: #ffa500;
}

.play-btn.loading:hover {
  background-color: #ff8c00;
}

.loading-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.time-display {
  font-family: monospace;
  font-size: 14px;
  color: #ccc;
  min-width: 80px;
}

.time-separator {
  margin: 0 4px;
}

.progress-container {
  flex: 1;
  max-width: 400px;
}

.progress-bar {
  height: 6px;
  background-color: #333;
  border-radius: 3px;
  position: relative;
  cursor: pointer;
}

.progress-fill {
  height: 100%;
  background-color: #007acc;
  border-radius: 3px;
  transition: width 0.1s ease;
}

.progress-handle {
  position: absolute;
  top: -4px;
  width: 14px;
  height: 14px;
  background-color: #007acc;
  border-radius: 50%;
  cursor: ew-resize;
  transform: translateX(-50%);
}

.volume-control {
  display: flex;
  align-items: center;
  gap: 8px;
}

.volume-btn {
  padding: 6px 8px;
}

.volume-slider {
  width: 60px;
}

.volume-input {
  width: 100%;
  height: 4px;
  background: #333;
  outline: none;
  border-radius: 2px;
}

.volume-input::-webkit-slider-thumb {
  appearance: none;
  width: 12px;
  height: 12px;
  background: #007acc;
  border-radius: 50%;
  cursor: pointer;
}

.volume-input::-moz-range-thumb {
  width: 12px;
  height: 12px;
  background: #007acc;
  border-radius: 50%;
  cursor: pointer;
  border: none;
}

.zoom-control {
  display: flex;
  align-items: center;
  gap: 8px;
}

.zoom-label {
  font-size: 12px;
  color: #ccc;
}

.zoom-btn {
  padding: 4px 8px;
  font-size: 12px;
  min-width: 24px;
}

.zoom-value {
  font-size: 12px;
  color: #ccc;
  min-width: 40px;
  text-align: center;
}

.debug-btn {
  background-color: #ff6b35;
  border-color: #ff6b35;
  font-size: 11px;
  padding: 6px 10px;
}

.debug-btn:hover:not(:disabled) {
  background-color: #e55a2b;
  border-color: #e55a2b;
}
</style>

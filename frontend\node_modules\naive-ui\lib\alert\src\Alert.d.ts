import type { ExtractPublicPropTypes } from '../../_utils';
import { type PropType, type SlotsType, type VNode } from 'vue';
export declare const alertProps: {
    title: StringConstructor;
    showIcon: {
        type: BooleanConstructor;
        default: boolean;
    };
    type: {
        type: PropType<"info" | "warning" | "error" | "success" | "default">;
        default: string;
    };
    bordered: {
        type: BooleanConstructor;
        default: boolean;
    };
    closable: BooleanConstructor;
    onClose: FunctionConstructor;
    onAfterLeave: FunctionConstructor;
    /** @deprecated */
    onAfterHide: FunctionConstructor;
    theme: PropType<import("../../_mixins").Theme<"Alert", {
        fontSize: string;
        lineHeight: string;
        titleFontWeight: string;
        borderRadius: string;
        border: string;
        color: string;
        titleTextColor: string;
        iconColor: string;
        contentTextColor: string;
        closeBorderRadius: string;
        closeColorHover: string;
        closeColorPressed: string;
        closeIconColor: string;
        closeIconColorHover: string;
        closeIconColorPressed: string;
        borderInfo: string;
        colorInfo: string;
        titleTextColorInfo: string;
        iconColorInfo: string;
        contentTextColorInfo: string;
        closeColorHoverInfo: string;
        closeColorPressedInfo: string;
        closeIconColorInfo: string;
        closeIconColorHoverInfo: string;
        closeIconColorPressedInfo: string;
        borderSuccess: string;
        colorSuccess: string;
        titleTextColorSuccess: string;
        iconColorSuccess: string;
        contentTextColorSuccess: string;
        closeColorHoverSuccess: string;
        closeColorPressedSuccess: string;
        closeIconColorSuccess: string;
        closeIconColorHoverSuccess: string;
        closeIconColorPressedSuccess: string;
        borderWarning: string;
        colorWarning: string;
        titleTextColorWarning: string;
        iconColorWarning: string;
        contentTextColorWarning: string;
        closeColorHoverWarning: string;
        closeColorPressedWarning: string;
        closeIconColorWarning: string;
        closeIconColorHoverWarning: string;
        closeIconColorPressedWarning: string;
        borderError: string;
        colorError: string;
        titleTextColorError: string;
        iconColorError: string;
        contentTextColorError: string;
        closeColorHoverError: string;
        closeColorPressedError: string;
        closeIconColorError: string;
        closeIconColorHoverError: string;
        closeIconColorPressedError: string;
        iconMargin: string;
        iconMarginRtl: string;
        iconSize: string;
        closeIconSize: string;
        closeSize: string;
        closeMargin: string;
        closeMarginRtl: string;
        padding: string;
    }, any>>;
    themeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Alert", {
        fontSize: string;
        lineHeight: string;
        titleFontWeight: string;
        borderRadius: string;
        border: string;
        color: string;
        titleTextColor: string;
        iconColor: string;
        contentTextColor: string;
        closeBorderRadius: string;
        closeColorHover: string;
        closeColorPressed: string;
        closeIconColor: string;
        closeIconColorHover: string;
        closeIconColorPressed: string;
        borderInfo: string;
        colorInfo: string;
        titleTextColorInfo: string;
        iconColorInfo: string;
        contentTextColorInfo: string;
        closeColorHoverInfo: string;
        closeColorPressedInfo: string;
        closeIconColorInfo: string;
        closeIconColorHoverInfo: string;
        closeIconColorPressedInfo: string;
        borderSuccess: string;
        colorSuccess: string;
        titleTextColorSuccess: string;
        iconColorSuccess: string;
        contentTextColorSuccess: string;
        closeColorHoverSuccess: string;
        closeColorPressedSuccess: string;
        closeIconColorSuccess: string;
        closeIconColorHoverSuccess: string;
        closeIconColorPressedSuccess: string;
        borderWarning: string;
        colorWarning: string;
        titleTextColorWarning: string;
        iconColorWarning: string;
        contentTextColorWarning: string;
        closeColorHoverWarning: string;
        closeColorPressedWarning: string;
        closeIconColorWarning: string;
        closeIconColorHoverWarning: string;
        closeIconColorPressedWarning: string;
        borderError: string;
        colorError: string;
        titleTextColorError: string;
        iconColorError: string;
        contentTextColorError: string;
        closeColorHoverError: string;
        closeColorPressedError: string;
        closeIconColorError: string;
        closeIconColorHoverError: string;
        closeIconColorPressedError: string;
        iconMargin: string;
        iconMarginRtl: string;
        iconSize: string;
        closeIconSize: string;
        closeSize: string;
        closeMargin: string;
        closeMarginRtl: string;
        padding: string;
    }, any>>>;
    builtinThemeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Alert", {
        fontSize: string;
        lineHeight: string;
        titleFontWeight: string;
        borderRadius: string;
        border: string;
        color: string;
        titleTextColor: string;
        iconColor: string;
        contentTextColor: string;
        closeBorderRadius: string;
        closeColorHover: string;
        closeColorPressed: string;
        closeIconColor: string;
        closeIconColorHover: string;
        closeIconColorPressed: string;
        borderInfo: string;
        colorInfo: string;
        titleTextColorInfo: string;
        iconColorInfo: string;
        contentTextColorInfo: string;
        closeColorHoverInfo: string;
        closeColorPressedInfo: string;
        closeIconColorInfo: string;
        closeIconColorHoverInfo: string;
        closeIconColorPressedInfo: string;
        borderSuccess: string;
        colorSuccess: string;
        titleTextColorSuccess: string;
        iconColorSuccess: string;
        contentTextColorSuccess: string;
        closeColorHoverSuccess: string;
        closeColorPressedSuccess: string;
        closeIconColorSuccess: string;
        closeIconColorHoverSuccess: string;
        closeIconColorPressedSuccess: string;
        borderWarning: string;
        colorWarning: string;
        titleTextColorWarning: string;
        iconColorWarning: string;
        contentTextColorWarning: string;
        closeColorHoverWarning: string;
        closeColorPressedWarning: string;
        closeIconColorWarning: string;
        closeIconColorHoverWarning: string;
        closeIconColorPressedWarning: string;
        borderError: string;
        colorError: string;
        titleTextColorError: string;
        iconColorError: string;
        contentTextColorError: string;
        closeColorHoverError: string;
        closeColorPressedError: string;
        closeIconColorError: string;
        closeIconColorHoverError: string;
        closeIconColorPressedError: string;
        iconMargin: string;
        iconMarginRtl: string;
        iconSize: string;
        closeIconSize: string;
        closeSize: string;
        closeMargin: string;
        closeMarginRtl: string;
        padding: string;
    }, any>>>;
};
export type AlertProps = ExtractPublicPropTypes<typeof alertProps>;
export interface AlertSlots {
    default?: () => VNode[];
    icon?: () => VNode[];
    header?: () => VNode[];
}
declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    title: StringConstructor;
    showIcon: {
        type: BooleanConstructor;
        default: boolean;
    };
    type: {
        type: PropType<"info" | "warning" | "error" | "success" | "default">;
        default: string;
    };
    bordered: {
        type: BooleanConstructor;
        default: boolean;
    };
    closable: BooleanConstructor;
    onClose: FunctionConstructor;
    onAfterLeave: FunctionConstructor;
    /** @deprecated */
    onAfterHide: FunctionConstructor;
    theme: PropType<import("../../_mixins").Theme<"Alert", {
        fontSize: string;
        lineHeight: string;
        titleFontWeight: string;
        borderRadius: string;
        border: string;
        color: string;
        titleTextColor: string;
        iconColor: string;
        contentTextColor: string;
        closeBorderRadius: string;
        closeColorHover: string;
        closeColorPressed: string;
        closeIconColor: string;
        closeIconColorHover: string;
        closeIconColorPressed: string;
        borderInfo: string;
        colorInfo: string;
        titleTextColorInfo: string;
        iconColorInfo: string;
        contentTextColorInfo: string;
        closeColorHoverInfo: string;
        closeColorPressedInfo: string;
        closeIconColorInfo: string;
        closeIconColorHoverInfo: string;
        closeIconColorPressedInfo: string;
        borderSuccess: string;
        colorSuccess: string;
        titleTextColorSuccess: string;
        iconColorSuccess: string;
        contentTextColorSuccess: string;
        closeColorHoverSuccess: string;
        closeColorPressedSuccess: string;
        closeIconColorSuccess: string;
        closeIconColorHoverSuccess: string;
        closeIconColorPressedSuccess: string;
        borderWarning: string;
        colorWarning: string;
        titleTextColorWarning: string;
        iconColorWarning: string;
        contentTextColorWarning: string;
        closeColorHoverWarning: string;
        closeColorPressedWarning: string;
        closeIconColorWarning: string;
        closeIconColorHoverWarning: string;
        closeIconColorPressedWarning: string;
        borderError: string;
        colorError: string;
        titleTextColorError: string;
        iconColorError: string;
        contentTextColorError: string;
        closeColorHoverError: string;
        closeColorPressedError: string;
        closeIconColorError: string;
        closeIconColorHoverError: string;
        closeIconColorPressedError: string;
        iconMargin: string;
        iconMarginRtl: string;
        iconSize: string;
        closeIconSize: string;
        closeSize: string;
        closeMargin: string;
        closeMarginRtl: string;
        padding: string;
    }, any>>;
    themeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Alert", {
        fontSize: string;
        lineHeight: string;
        titleFontWeight: string;
        borderRadius: string;
        border: string;
        color: string;
        titleTextColor: string;
        iconColor: string;
        contentTextColor: string;
        closeBorderRadius: string;
        closeColorHover: string;
        closeColorPressed: string;
        closeIconColor: string;
        closeIconColorHover: string;
        closeIconColorPressed: string;
        borderInfo: string;
        colorInfo: string;
        titleTextColorInfo: string;
        iconColorInfo: string;
        contentTextColorInfo: string;
        closeColorHoverInfo: string;
        closeColorPressedInfo: string;
        closeIconColorInfo: string;
        closeIconColorHoverInfo: string;
        closeIconColorPressedInfo: string;
        borderSuccess: string;
        colorSuccess: string;
        titleTextColorSuccess: string;
        iconColorSuccess: string;
        contentTextColorSuccess: string;
        closeColorHoverSuccess: string;
        closeColorPressedSuccess: string;
        closeIconColorSuccess: string;
        closeIconColorHoverSuccess: string;
        closeIconColorPressedSuccess: string;
        borderWarning: string;
        colorWarning: string;
        titleTextColorWarning: string;
        iconColorWarning: string;
        contentTextColorWarning: string;
        closeColorHoverWarning: string;
        closeColorPressedWarning: string;
        closeIconColorWarning: string;
        closeIconColorHoverWarning: string;
        closeIconColorPressedWarning: string;
        borderError: string;
        colorError: string;
        titleTextColorError: string;
        iconColorError: string;
        contentTextColorError: string;
        closeColorHoverError: string;
        closeColorPressedError: string;
        closeIconColorError: string;
        closeIconColorHoverError: string;
        closeIconColorPressedError: string;
        iconMargin: string;
        iconMarginRtl: string;
        iconSize: string;
        closeIconSize: string;
        closeSize: string;
        closeMargin: string;
        closeMarginRtl: string;
        padding: string;
    }, any>>>;
    builtinThemeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Alert", {
        fontSize: string;
        lineHeight: string;
        titleFontWeight: string;
        borderRadius: string;
        border: string;
        color: string;
        titleTextColor: string;
        iconColor: string;
        contentTextColor: string;
        closeBorderRadius: string;
        closeColorHover: string;
        closeColorPressed: string;
        closeIconColor: string;
        closeIconColorHover: string;
        closeIconColorPressed: string;
        borderInfo: string;
        colorInfo: string;
        titleTextColorInfo: string;
        iconColorInfo: string;
        contentTextColorInfo: string;
        closeColorHoverInfo: string;
        closeColorPressedInfo: string;
        closeIconColorInfo: string;
        closeIconColorHoverInfo: string;
        closeIconColorPressedInfo: string;
        borderSuccess: string;
        colorSuccess: string;
        titleTextColorSuccess: string;
        iconColorSuccess: string;
        contentTextColorSuccess: string;
        closeColorHoverSuccess: string;
        closeColorPressedSuccess: string;
        closeIconColorSuccess: string;
        closeIconColorHoverSuccess: string;
        closeIconColorPressedSuccess: string;
        borderWarning: string;
        colorWarning: string;
        titleTextColorWarning: string;
        iconColorWarning: string;
        contentTextColorWarning: string;
        closeColorHoverWarning: string;
        closeColorPressedWarning: string;
        closeIconColorWarning: string;
        closeIconColorHoverWarning: string;
        closeIconColorPressedWarning: string;
        borderError: string;
        colorError: string;
        titleTextColorError: string;
        iconColorError: string;
        contentTextColorError: string;
        closeColorHoverError: string;
        closeColorPressedError: string;
        closeIconColorError: string;
        closeIconColorHoverError: string;
        closeIconColorPressedError: string;
        iconMargin: string;
        iconMarginRtl: string;
        iconSize: string;
        closeIconSize: string;
        closeSize: string;
        closeMargin: string;
        closeMarginRtl: string;
        padding: string;
    }, any>>>;
}>, {
    rtlEnabled: import("vue").Ref<import("../../config-provider/src/internal-interface").RtlItem | undefined, import("../../config-provider/src/internal-interface").RtlItem | undefined> | undefined;
    mergedClsPrefix: import("vue").Ref<string, string>;
    mergedBordered: import("vue").ComputedRef<boolean>;
    visible: import("vue").Ref<boolean, boolean>;
    handleCloseClick: () => void;
    handleAfterLeave: () => void;
    mergedTheme: import("vue").ComputedRef<{
        common: import("../..").ThemeCommonVars;
        self: {
            fontSize: string;
            lineHeight: string;
            titleFontWeight: string;
            borderRadius: string;
            border: string;
            color: string;
            titleTextColor: string;
            iconColor: string;
            contentTextColor: string;
            closeBorderRadius: string;
            closeColorHover: string;
            closeColorPressed: string;
            closeIconColor: string;
            closeIconColorHover: string;
            closeIconColorPressed: string;
            borderInfo: string;
            colorInfo: string;
            titleTextColorInfo: string;
            iconColorInfo: string;
            contentTextColorInfo: string;
            closeColorHoverInfo: string;
            closeColorPressedInfo: string;
            closeIconColorInfo: string;
            closeIconColorHoverInfo: string;
            closeIconColorPressedInfo: string;
            borderSuccess: string;
            colorSuccess: string;
            titleTextColorSuccess: string;
            iconColorSuccess: string;
            contentTextColorSuccess: string;
            closeColorHoverSuccess: string;
            closeColorPressedSuccess: string;
            closeIconColorSuccess: string;
            closeIconColorHoverSuccess: string;
            closeIconColorPressedSuccess: string;
            borderWarning: string;
            colorWarning: string;
            titleTextColorWarning: string;
            iconColorWarning: string;
            contentTextColorWarning: string;
            closeColorHoverWarning: string;
            closeColorPressedWarning: string;
            closeIconColorWarning: string;
            closeIconColorHoverWarning: string;
            closeIconColorPressedWarning: string;
            borderError: string;
            colorError: string;
            titleTextColorError: string;
            iconColorError: string;
            contentTextColorError: string;
            closeColorHoverError: string;
            closeColorPressedError: string;
            closeIconColorError: string;
            closeIconColorHoverError: string;
            closeIconColorPressedError: string;
            iconMargin: string;
            iconMarginRtl: string;
            iconSize: string;
            closeIconSize: string;
            closeSize: string;
            closeMargin: string;
            closeMarginRtl: string;
            padding: string;
        };
        peers: any;
        peerOverrides: {
            [x: string]: any;
        };
    }>;
    cssVars: import("vue").ComputedRef<{
        '--n-bezier': string;
        '--n-color': string;
        '--n-close-icon-size': string;
        '--n-close-border-radius': string;
        '--n-close-color-hover': string;
        '--n-close-color-pressed': string;
        '--n-close-icon-color': string;
        '--n-close-icon-color-hover': string;
        '--n-close-icon-color-pressed': string;
        '--n-icon-color': string;
        '--n-border': string;
        '--n-title-text-color': string;
        '--n-content-text-color': string;
        '--n-line-height': string;
        '--n-border-radius': string;
        '--n-font-size': string;
        '--n-title-font-weight': string;
        '--n-icon-size': string;
        '--n-icon-margin': string;
        '--n-icon-margin-rtl': string;
        '--n-close-size': string;
        '--n-close-margin': string;
        '--n-close-margin-rtl': string;
        '--n-padding': string;
        '--n-icon-margin-left': string;
        '--n-icon-margin-right': string;
    }> | undefined;
    themeClass: import("vue").Ref<string, string> | undefined;
    onRender: (() => void) | undefined;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    title: StringConstructor;
    showIcon: {
        type: BooleanConstructor;
        default: boolean;
    };
    type: {
        type: PropType<"info" | "warning" | "error" | "success" | "default">;
        default: string;
    };
    bordered: {
        type: BooleanConstructor;
        default: boolean;
    };
    closable: BooleanConstructor;
    onClose: FunctionConstructor;
    onAfterLeave: FunctionConstructor;
    /** @deprecated */
    onAfterHide: FunctionConstructor;
    theme: PropType<import("../../_mixins").Theme<"Alert", {
        fontSize: string;
        lineHeight: string;
        titleFontWeight: string;
        borderRadius: string;
        border: string;
        color: string;
        titleTextColor: string;
        iconColor: string;
        contentTextColor: string;
        closeBorderRadius: string;
        closeColorHover: string;
        closeColorPressed: string;
        closeIconColor: string;
        closeIconColorHover: string;
        closeIconColorPressed: string;
        borderInfo: string;
        colorInfo: string;
        titleTextColorInfo: string;
        iconColorInfo: string;
        contentTextColorInfo: string;
        closeColorHoverInfo: string;
        closeColorPressedInfo: string;
        closeIconColorInfo: string;
        closeIconColorHoverInfo: string;
        closeIconColorPressedInfo: string;
        borderSuccess: string;
        colorSuccess: string;
        titleTextColorSuccess: string;
        iconColorSuccess: string;
        contentTextColorSuccess: string;
        closeColorHoverSuccess: string;
        closeColorPressedSuccess: string;
        closeIconColorSuccess: string;
        closeIconColorHoverSuccess: string;
        closeIconColorPressedSuccess: string;
        borderWarning: string;
        colorWarning: string;
        titleTextColorWarning: string;
        iconColorWarning: string;
        contentTextColorWarning: string;
        closeColorHoverWarning: string;
        closeColorPressedWarning: string;
        closeIconColorWarning: string;
        closeIconColorHoverWarning: string;
        closeIconColorPressedWarning: string;
        borderError: string;
        colorError: string;
        titleTextColorError: string;
        iconColorError: string;
        contentTextColorError: string;
        closeColorHoverError: string;
        closeColorPressedError: string;
        closeIconColorError: string;
        closeIconColorHoverError: string;
        closeIconColorPressedError: string;
        iconMargin: string;
        iconMarginRtl: string;
        iconSize: string;
        closeIconSize: string;
        closeSize: string;
        closeMargin: string;
        closeMarginRtl: string;
        padding: string;
    }, any>>;
    themeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Alert", {
        fontSize: string;
        lineHeight: string;
        titleFontWeight: string;
        borderRadius: string;
        border: string;
        color: string;
        titleTextColor: string;
        iconColor: string;
        contentTextColor: string;
        closeBorderRadius: string;
        closeColorHover: string;
        closeColorPressed: string;
        closeIconColor: string;
        closeIconColorHover: string;
        closeIconColorPressed: string;
        borderInfo: string;
        colorInfo: string;
        titleTextColorInfo: string;
        iconColorInfo: string;
        contentTextColorInfo: string;
        closeColorHoverInfo: string;
        closeColorPressedInfo: string;
        closeIconColorInfo: string;
        closeIconColorHoverInfo: string;
        closeIconColorPressedInfo: string;
        borderSuccess: string;
        colorSuccess: string;
        titleTextColorSuccess: string;
        iconColorSuccess: string;
        contentTextColorSuccess: string;
        closeColorHoverSuccess: string;
        closeColorPressedSuccess: string;
        closeIconColorSuccess: string;
        closeIconColorHoverSuccess: string;
        closeIconColorPressedSuccess: string;
        borderWarning: string;
        colorWarning: string;
        titleTextColorWarning: string;
        iconColorWarning: string;
        contentTextColorWarning: string;
        closeColorHoverWarning: string;
        closeColorPressedWarning: string;
        closeIconColorWarning: string;
        closeIconColorHoverWarning: string;
        closeIconColorPressedWarning: string;
        borderError: string;
        colorError: string;
        titleTextColorError: string;
        iconColorError: string;
        contentTextColorError: string;
        closeColorHoverError: string;
        closeColorPressedError: string;
        closeIconColorError: string;
        closeIconColorHoverError: string;
        closeIconColorPressedError: string;
        iconMargin: string;
        iconMarginRtl: string;
        iconSize: string;
        closeIconSize: string;
        closeSize: string;
        closeMargin: string;
        closeMarginRtl: string;
        padding: string;
    }, any>>>;
    builtinThemeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Alert", {
        fontSize: string;
        lineHeight: string;
        titleFontWeight: string;
        borderRadius: string;
        border: string;
        color: string;
        titleTextColor: string;
        iconColor: string;
        contentTextColor: string;
        closeBorderRadius: string;
        closeColorHover: string;
        closeColorPressed: string;
        closeIconColor: string;
        closeIconColorHover: string;
        closeIconColorPressed: string;
        borderInfo: string;
        colorInfo: string;
        titleTextColorInfo: string;
        iconColorInfo: string;
        contentTextColorInfo: string;
        closeColorHoverInfo: string;
        closeColorPressedInfo: string;
        closeIconColorInfo: string;
        closeIconColorHoverInfo: string;
        closeIconColorPressedInfo: string;
        borderSuccess: string;
        colorSuccess: string;
        titleTextColorSuccess: string;
        iconColorSuccess: string;
        contentTextColorSuccess: string;
        closeColorHoverSuccess: string;
        closeColorPressedSuccess: string;
        closeIconColorSuccess: string;
        closeIconColorHoverSuccess: string;
        closeIconColorPressedSuccess: string;
        borderWarning: string;
        colorWarning: string;
        titleTextColorWarning: string;
        iconColorWarning: string;
        contentTextColorWarning: string;
        closeColorHoverWarning: string;
        closeColorPressedWarning: string;
        closeIconColorWarning: string;
        closeIconColorHoverWarning: string;
        closeIconColorPressedWarning: string;
        borderError: string;
        colorError: string;
        titleTextColorError: string;
        iconColorError: string;
        contentTextColorError: string;
        closeColorHoverError: string;
        closeColorPressedError: string;
        closeIconColorError: string;
        closeIconColorHoverError: string;
        closeIconColorPressedError: string;
        iconMargin: string;
        iconMarginRtl: string;
        iconSize: string;
        closeIconSize: string;
        closeSize: string;
        closeMargin: string;
        closeMarginRtl: string;
        padding: string;
    }, any>>>;
}>> & Readonly<{}>, {
    type: "error" | "default" | "info" | "success" | "warning";
    bordered: boolean;
    showIcon: boolean;
    closable: boolean;
}, SlotsType<AlertSlots>, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;

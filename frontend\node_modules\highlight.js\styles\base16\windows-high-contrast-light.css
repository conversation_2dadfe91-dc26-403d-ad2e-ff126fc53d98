pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Windows High Contrast Light
  Author: <PERSON> (https://github.com/C-Fergus)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme windows-high-contrast-light
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #fcfcfc  Default Background
base01  #e8e8e8  Lighter Background (Used for status bars, line number and folding marks)
base02  #d4d4d4  Selection Background
base03  #c0c0c0  Comments, Invisibles, Line Highlighting
base04  #7e7e7e  Dark Foreground (Used for status bars)
base05  #545454  Default Foreground, Caret, Delimiters, Operators
base06  #2a2a2a  Light Foreground (Not often used)
base07  #000000  Light Background (Not often used)
base08  #800000  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #fcfc54  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #808000  Classes, Markup Bold, Search Text Background
base0B  #008000  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #008080  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #000080  Functions, Methods, Attribute IDs, Headings
base0E  #800080  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #54fc54  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #545454;
  background: #fcfcfc
}
.hljs::selection,
.hljs ::selection {
  background-color: #d4d4d4;
  color: #545454
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #c0c0c0 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #c0c0c0
}
/* base04 - #7e7e7e -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #7e7e7e
}
/* base05 - #545454 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #545454
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #800000
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #fcfc54
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #808000
}
.hljs-strong {
  font-weight: bold;
  color: #808000
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #008000
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #008080
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #000080
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #800080
}
.hljs-emphasis {
  color: #800080;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #54fc54
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}
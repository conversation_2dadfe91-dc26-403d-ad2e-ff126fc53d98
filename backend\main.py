from fastapi import <PERSON><PERSON><PERSON>, File, UploadFile, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import os
import uuid
import shutil
import asyncio
import subprocess
from pathlib import Path
from PIL import Image
import json
from datetime import datetime
from typing import Dict, List, Optional
import aiofiles
import imageio_ffmpeg as ffmpeg

app = FastAPI(title="Video Editor API", version="1.0.0")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173"],  # Vite dev server
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Storage directories
STORAGE_DIR = Path("storage")
UPLOADS_DIR = STORAGE_DIR / "uploads"
PROXIES_DIR = STORAGE_DIR / "proxies"
THUMBNAILS_DIR = STORAGE_DIR / "thumbnails"
STREAMS_DIR = STORAGE_DIR / "streams"

# Create directories
for dir_path in [UPLOADS_DIR, PROXIES_DIR, THUMBNAILS_DIR, STREAMS_DIR]:
    dir_path.mkdir(parents=True, exist_ok=True)

# Mount static files
app.mount("/storage", StaticFiles(directory="storage"), name="storage")

# In-memory storage for demo (replace with database in production)
media_storage: Dict[str, dict] = {}
active_streams: Dict[str, dict] = {}  # 活跃的流媒体会话

class MediaFile:
    def __init__(self, id: str, name: str, original_path: str, proxy_path: str, 
                 thumbnail_path: str, duration: float, file_size: int):
        self.id = id
        self.name = name
        self.original_path = original_path
        self.proxy_path = proxy_path
        self.thumbnail_path = thumbnail_path
        self.duration = duration
        self.file_size = file_size
        self.created_at = datetime.now()

def get_video_duration(file_path: str) -> float:
    """Get video duration using ffprobe"""
    try:
        # 简化版本：使用文件大小估算时长（临时方案）
        # 实际项目中应该安装FFmpeg
        import os
        file_size = os.path.getsize(file_path)
        # 粗略估算：假设1MB约等于1秒（这只是临时方案）
        estimated_duration = max(10, file_size / (1024 * 1024))
        return min(estimated_duration, 300)  # 限制在5分钟内
    except Exception as e:
        print(f"Error getting duration: {e}")
        return 30.0  # 默认30秒

def create_video_proxy(input_path: str, output_path: str) -> bool:
    """Create a lower resolution proxy video"""
    try:
        # 临时方案：直接复制原文件作为代理，但确保扩展名正确
        import shutil
        from pathlib import Path

        # 确保输出文件有正确的扩展名
        output_path_obj = Path(output_path)
        if not output_path_obj.suffix:
            output_path = str(output_path_obj.with_suffix('.mp4'))

        shutil.copy2(input_path, output_path)
        print(f"Proxy created (copy): {output_path}")
        return True
    except Exception as e:
        print(f"Error creating proxy: {e}")
        return False

def create_thumbnail(input_path: str, output_path: str, time_offset: float = 1.0) -> bool:
    """Create a thumbnail from video"""
    try:
        # 临时方案：创建一个简单的占位图片
        from PIL import Image, ImageDraw

        # 创建一个简单的缩略图
        img = Image.new('RGB', (320, 180), color='#333333')
        draw = ImageDraw.Draw(img)

        # 添加文本
        try:
            # 尝试使用默认字体
            draw.text((10, 80), "Video\nThumbnail", fill='white')
        except:
            # 如果字体加载失败，只画一个简单的矩形
            draw.rectangle([50, 50, 270, 130], outline='white', width=2)

        img.save(output_path, 'PNG')
        print(f"Thumbnail created: {output_path}")
        return True
    except Exception as e:
        print(f"Error creating thumbnail: {e}")
        return False

class RealHLSStreamManager:
    """真正的HLS流媒体管理器 - 使用imageio-ffmpeg"""

    def __init__(self):
        self.active_processes = {}
        self.ffmpeg_exe = ffmpeg.get_ffmpeg_exe()
        print(f"FFmpeg路径: {self.ffmpeg_exe}")

    def build_ffmpeg_command(self, timeline_config: dict, stream_id: str) -> list:
        """构建FFmpeg命令"""
        clips = timeline_config.get('clips', [])
        if not clips:
            raise ValueError("时间轴配置中没有视频片段")

        # 创建流目录
        stream_dir = STREAMS_DIR / stream_id
        stream_dir.mkdir(exist_ok=True)

        # 验证所有视频文件是否存在
        valid_clips = []
        for clip in clips:
            media_id = clip.get('mediaId')
            if media_id and media_id in media_storage:
                proxy_path = PROXIES_DIR / f"{media_id}_proxy.mp4"
                if proxy_path.exists():
                    valid_clips.append({
                        'clip': clip,
                        'path': str(proxy_path),
                        'media': media_storage[media_id]
                    })
                    print(f"找到有效片段: {media_storage[media_id]['name']}")

        if not valid_clips:
            raise ValueError("没有找到有效的视频文件")

        # 构建FFmpeg命令
        cmd = [self.ffmpeg_exe, '-y']  # -y 覆盖输出文件

        # 添加输入文件
        for i, clip_info in enumerate(valid_clips):
            cmd.extend(['-i', clip_info['path']])

        # 如果只有一个片段，直接转换
        if len(valid_clips) == 1:
            clip = valid_clips[0]['clip']
            trim_start = clip.get('trimStart', 0)
            trim_end = clip.get('trimEnd', 30)

            cmd.extend([
                '-ss', str(trim_start),           # 开始时间
                '-t', str(trim_end - trim_start), # 持续时间
                '-c:v', 'libx264',                # 视频编码器
                '-preset', 'veryfast',            # 编码速度优先
                '-crf', '23',                     # 质量设置
                '-c:a', 'aac',                    # 音频编码器
                '-f', 'hls',                      # HLS格式
                '-hls_time', '2',                 # 每个切片2秒
                '-hls_list_size', '0',            # 保持所有切片
                '-hls_flags', 'delete_segments',  # 删除旧切片
                str(stream_dir / 'playlist.m3u8')
            ])
        else:
            # 多个片段需要拼接
            filter_complex = self.build_filter_complex(valid_clips)
            cmd.extend([
                '-filter_complex', filter_complex,
                '-map', '[outv]', '-map', '[outa]',
                '-c:v', 'libx264',
                '-preset', 'veryfast',
                '-crf', '23',
                '-c:a', 'aac',
                '-f', 'hls',
                '-hls_time', '2',
                '-hls_list_size', '0',
                '-hls_flags', 'delete_segments',
                str(stream_dir / 'playlist.m3u8')
            ])

        return cmd

    def build_filter_complex(self, valid_clips: list) -> str:
        """构建FFmpeg滤镜复合体"""
        filters = []

        # 为每个片段创建裁剪滤镜
        for i, clip_info in enumerate(valid_clips):
            clip = clip_info['clip']
            trim_start = clip.get('trimStart', 0)
            trim_end = clip.get('trimEnd', 30)

            # 视频滤镜
            video_filter = f"[{i}:v]trim=start={trim_start}:end={trim_end},setpts=PTS-STARTPTS[v{i}]"
            # 音频滤镜
            audio_filter = f"[{i}:a]atrim=start={trim_start}:end={trim_end},asetpts=PTS-STARTPTS[a{i}]"

            filters.extend([video_filter, audio_filter])

        # 拼接所有片段
        video_inputs = ''.join([f'[v{i}]' for i in range(len(valid_clips))])
        audio_inputs = ''.join([f'[a{i}]' for i in range(len(valid_clips))])

        video_concat = f"{video_inputs}concat=n={len(valid_clips)}:v=1:a=0[outv]"
        audio_concat = f"{audio_inputs}concat=n={len(valid_clips)}:v=0:a=1[outa]"

        filters.extend([video_concat, audio_concat])

        return ';'.join(filters)

    async def start_stream(self, timeline_config: dict) -> str:
        """启动HLS流"""
        stream_id = str(uuid.uuid4())

        try:
            cmd = self.build_ffmpeg_command(timeline_config, stream_id)
            print(f"启动FFmpeg命令: {' '.join(cmd)}")

            # 启动FFmpeg进程
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            self.active_processes[stream_id] = {
                'process': process,
                'timeline': timeline_config,
                'created_at': datetime.now()
            }

            # 异步监控进程输出
            asyncio.create_task(self.monitor_process(stream_id, process))

            print(f"HLS流启动成功: {stream_id}")
            return stream_id

        except Exception as e:
            print(f"启动流失败: {e}")
            raise HTTPException(status_code=500, detail=f"启动流失败: {str(e)}")

    async def monitor_process(self, stream_id: str, process):
        """监控FFmpeg进程"""
        try:
            stdout, stderr = await process.communicate()

            if process.returncode != 0:
                print(f"FFmpeg进程异常退出 (stream_id: {stream_id}): {stderr.decode()}")
            else:
                print(f"FFmpeg进程正常结束 (stream_id: {stream_id})")

        except Exception as e:
            print(f"监控进程时出错: {e}")
        finally:
            # 清理
            if stream_id in self.active_processes:
                del self.active_processes[stream_id]

    async def stop_stream(self, stream_id: str):
        """停止HLS流"""
        if stream_id in self.active_processes:
            process = self.active_processes[stream_id]['process']
            process.terminate()

            try:
                await asyncio.wait_for(process.wait(), timeout=5.0)
            except asyncio.TimeoutError:
                process.kill()
                await process.wait()

            del self.active_processes[stream_id]
            print(f"HLS流进程已停止: {stream_id}")

        # 清理流文件
        stream_dir = STREAMS_DIR / stream_id
        if stream_dir.exists():
            shutil.rmtree(stream_dir)
            print(f"HLS流文件已清理: {stream_id}")

    def get_stream_status(self, stream_id: str) -> dict:
        """获取流状态"""
        if stream_id in self.active_processes:
            # 检查playlist文件是否存在来判断流是否就绪
            playlist_path = STREAMS_DIR / stream_id / "playlist.m3u8"
            is_ready = playlist_path.exists()

            return {
                'status': 'ready' if is_ready else 'starting',
                'isReady': is_ready
            }
        return {
            'status': 'not_found',
            'isReady': False
        }

# 全局HLS管理器
hls_manager = RealHLSStreamManager()

@app.post("/api/upload")
async def upload_video(file: UploadFile = File(...)):
    """Upload and process video file"""
    if not file.content_type or not file.content_type.startswith('video/'):
        raise HTTPException(status_code=400, detail="File must be a video")
    
    # Generate unique ID
    file_id = str(uuid.uuid4())
    
    # File paths
    original_filename = file.filename or f"video_{file_id}"
    file_extension = Path(original_filename).suffix
    
    original_path = UPLOADS_DIR / f"{file_id}{file_extension}"
    proxy_path = PROXIES_DIR / f"{file_id}_proxy.mp4"
    thumbnail_path = THUMBNAILS_DIR / f"{file_id}_thumb.png"
    
    try:
        # Save original file
        with open(original_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        
        # Get file size
        file_size = original_path.stat().st_size
        
        # Get video duration
        duration = get_video_duration(str(original_path))
        
        # Create proxy video
        proxy_success = create_video_proxy(str(original_path), str(proxy_path))
        if not proxy_success:
            raise HTTPException(status_code=500, detail="Failed to create video proxy")
        
        # Create thumbnail
        thumbnail_success = create_thumbnail(str(original_path), str(thumbnail_path))
        if not thumbnail_success:
            print("Warning: Failed to create thumbnail")
        
        # Store media info
        media_info = {
            "id": file_id,
            "name": original_filename,
            "duration": duration,
            "file_size": file_size,
            "proxy_url": f"/storage/proxies/{file_id}_proxy.mp4",
            "thumbnail_url": f"/storage/thumbnails/{file_id}_thumb.png" if thumbnail_success else None,
            "created_at": datetime.now().isoformat()
        }
        
        media_storage[file_id] = media_info
        
        return JSONResponse(content=media_info)
        
    except Exception as e:
        # Cleanup on error
        for path in [original_path, proxy_path, thumbnail_path]:
            if path.exists():
                path.unlink()
        raise HTTPException(status_code=500, detail=f"Upload failed: {str(e)}")

@app.get("/api/media")
async def get_media_list():
    """Get list of all uploaded media"""
    return JSONResponse(content=list(media_storage.values()))

@app.get("/api/media/{media_id}")
async def get_media_info(media_id: str):
    """Get specific media information"""
    if media_id not in media_storage:
        raise HTTPException(status_code=404, detail="Media not found")
    
    return JSONResponse(content=media_storage[media_id])

@app.delete("/api/media/{media_id}")
async def delete_media(media_id: str):
    """Delete media file"""
    if media_id not in media_storage:
        raise HTTPException(status_code=404, detail="Media not found")
    
    # Remove files
    file_extension = Path(media_storage[media_id]["name"]).suffix
    original_path = UPLOADS_DIR / f"{media_id}{file_extension}"
    proxy_path = PROXIES_DIR / f"{media_id}_proxy.mp4"
    thumbnail_path = THUMBNAILS_DIR / f"{media_id}_thumb.png"
    
    for path in [original_path, proxy_path, thumbnail_path]:
        if path.exists():
            path.unlink()
    
    # Remove from storage
    del media_storage[media_id]
    
    return JSONResponse(content={"message": "Media deleted successfully"})

# HLS流媒体API
@app.post("/api/stream/start")
async def start_hls_stream(timeline_config: dict):
    """启动HLS预览流"""
    try:
        stream_id = await hls_manager.start_stream(timeline_config)

        # 存储流信息
        active_streams[stream_id] = {
            'id': stream_id,
            'timeline': timeline_config,
            'created_at': datetime.now().isoformat(),
            'status': 'starting'
        }

        playlist_url = f"/storage/streams/{stream_id}/playlist.m3u8"

        return JSONResponse(content={
            'streamId': stream_id,
            'playlistUrl': playlist_url,
            'status': 'starting'
        })

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/stream/update/{stream_id}")
async def update_hls_stream(stream_id: str, timeline_config: dict):
    """更新HLS流配置"""
    try:
        # 停止旧流
        await hls_manager.stop_stream(stream_id)

        # 启动新流（重用相同ID）
        await hls_manager.start_stream(timeline_config)

        # 更新流信息
        if stream_id in active_streams:
            active_streams[stream_id]['timeline'] = timeline_config
            active_streams[stream_id]['status'] = 'updated'

        return JSONResponse(content={'status': 'updated'})

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/api/stream/{stream_id}")
async def stop_hls_stream(stream_id: str):
    """停止HLS流"""
    try:
        await hls_manager.stop_stream(stream_id)

        if stream_id in active_streams:
            del active_streams[stream_id]

        return JSONResponse(content={'status': 'stopped'})

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/stream/{stream_id}/status")
async def get_stream_status(stream_id: str):
    """获取流状态"""
    try:
        status_info = hls_manager.get_stream_status(stream_id)

        if not status_info['isReady']:
            raise HTTPException(status_code=404, detail="Stream not found")

        return JSONResponse(content={
            'streamId': stream_id,
            'status': status_info['status'],
            'isReady': status_info['isReady'],
            'createdAt': datetime.now().isoformat()
        })

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/streams")
async def list_active_streams():
    """列出所有活跃的流"""
    return JSONResponse(content=list(active_streams.values()))

@app.get("/")
async def root():
    return {"message": "Video Editor API is running"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)

import { type GlobalThemeOverrides } from '../../config-provider';
declare const _default: import("vue").DefineComponent<{}, {
    locale: import("vue").Ref<{
        title: string;
        clearAllVars: string;
        clearSearch: string;
        filterCompName: string;
        filterVarName: string;
        import: string;
        export: string;
        restore: string;
    }, {
        title: string;
        clearAllVars: string;
        clearSearch: string;
        filterCompName: string;
        filterVarName: string;
        import: string;
        export: string;
        restore: string;
    }>;
    themeCommonDefault: import("vue").ComputedRef<{
        baseColor: string;
        primaryColor: string;
        primaryColorHover: string;
        primaryColorPressed: string;
        primaryColorSuppl: string;
        infoColor: string;
        infoColorHover: string;
        infoColorPressed: string;
        infoColorSuppl: string;
        successColor: string;
        successColorHover: string;
        successColorPressed: string;
        successColorSuppl: string;
        warningColor: string;
        warningColorHover: string;
        warningColorPressed: string;
        warningColorSuppl: string;
        errorColor: string;
        errorColorHover: string;
        errorColorPressed: string;
        errorColorSuppl: string;
        textColorBase: string;
        textColor1: string;
        textColor2: string;
        textColor3: string;
        textColorDisabled: string;
        placeholderColor: string;
        placeholderColorDisabled: string;
        iconColor: string;
        iconColorHover: string;
        iconColorPressed: string;
        iconColorDisabled: string;
        opacity1: string;
        opacity2: string;
        opacity3: string;
        opacity4: string;
        opacity5: string;
        dividerColor: string;
        borderColor: string;
        closeIconColor: string;
        closeIconColorHover: string;
        closeIconColorPressed: string;
        closeColorHover: string;
        closeColorPressed: string;
        clearColor: string;
        clearColorHover: string;
        clearColorPressed: string;
        scrollbarColor: string;
        scrollbarColorHover: string;
        scrollbarWidth: string;
        scrollbarHeight: string;
        scrollbarBorderRadius: string;
        progressRailColor: string;
        railColor: string;
        popoverColor: string;
        tableColor: string;
        cardColor: string;
        modalColor: string;
        bodyColor: string;
        tagColor: string;
        avatarColor: string;
        invertedColor: string;
        inputColor: string;
        codeColor: string;
        tabColor: string;
        actionColor: string;
        tableHeaderColor: string;
        hoverColor: string;
        tableColorHover: string;
        tableColorStriped: string;
        pressedColor: string;
        opacityDisabled: string;
        inputColorDisabled: string;
        buttonColor2: string;
        buttonColor2Hover: string;
        buttonColor2Pressed: string;
        boxShadow1: string;
        boxShadow2: string;
        boxShadow3: string;
        fontFamily: string;
        fontFamilyMono: string;
        fontWeight: string;
        fontWeightStrong: string;
        cubicBezierEaseInOut: string;
        cubicBezierEaseOut: string;
        cubicBezierEaseIn: string;
        borderRadius: string;
        borderRadiusSmall: string;
        fontSize: string;
        fontSizeMini: string;
        fontSizeTiny: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        fontSizeHuge: string;
        lineHeight: string;
        heightMini: string;
        heightTiny: string;
        heightSmall: string;
        heightMedium: string;
        heightLarge: string;
        heightHuge: string;
        name: "common";
    }>;
    theme: import("vue").ComputedRef<GlobalThemeOverrides>;
    showPanel: import("vue").Ref<boolean, boolean>;
    tempOverrides: import("vue").Ref<any, any>;
    overrides: import("vue").Ref<any, any>;
    compNamePattern: import("vue").Ref<string, string>;
    tempCompNamePattern: import("vue").Ref<string, string>;
    varNamePattern: import("vue").Ref<string, string>;
    tempVarNamePattern: import("vue").Ref<string, string>;
    fileInputRef: import("vue").Ref<HTMLInputElement | null, HTMLInputElement | null>;
    applyTempOverrides: () => void;
    setTempOverrides: (compName: string, varName: string, value: string) => void;
    handleClearAllClick: () => void;
    handleExportClick: () => void;
    handleImportClick: () => void;
    handleInputFileChange: () => void;
    toggleMaximized: () => void;
    isMaximized: import("vue").Ref<boolean, boolean>;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<{}> & Readonly<{}>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;

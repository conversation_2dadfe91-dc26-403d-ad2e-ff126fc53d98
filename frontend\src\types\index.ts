// 媒体文件类型
export interface MediaItem {
  id: string
  name: string
  duration: number
  file_size: number
  proxy_url: string
  thumbnail_url?: string
  created_at: string
}

// 时间轴片段类型
export interface TimelineClip {
  id: string
  mediaId: string
  startTime: number    // 在时间轴上的开始位置（秒）
  duration: number     // 片段长度（秒）
  trimStart: number    // 素材内部裁剪开始点（秒）
  trimEnd: number      // 素材内部裁剪结束点（秒）
}

// 时间轴轨道类型
export interface TimelineTrack {
  id: string
  type: 'video' | 'audio'
  clips: TimelineClip[]
}

// 编辑器状态类型
export interface EditorState {
  // 素材库
  mediaLibrary: MediaItem[]
  
  // 时间轴
  timeline: {
    tracks: TimelineTrack[]
    duration: number
    currentTime: number
    zoom: number
  }
  
  // 播放状态
  playback: {
    isPlaying: boolean
    currentTime: number
  }
  
  // UI状态
  ui: {
    selectedClipId?: string
    isDragging: boolean
  }
}

// 拖拽数据类型
export interface DragData {
  type: 'media' | 'clip'
  mediaId?: string
  clipId?: string
  data: any
}

// API响应类型
export interface ApiResponse<T> {
  data?: T
  error?: string
  message?: string
}

// 上传进度类型
export interface UploadProgress {
  loaded: number
  total: number
  percentage: number
}

import type { FormValidationStatus } from '../../../form/src/interface';
import type { SelectBaseOption } from '../../../select/src/interface';
import type { TagRef } from '../../../tag/src/Tag';
import type { <PERSON>derLabel, RenderLabelImpl } from '../../select-menu/src/interface';
import type { RenderTag } from './interface';
import { type InputHTMLAttributes, type PropType } from 'vue';
import { type VOverflowInst } from 'vueuc';
import { type PopoverProps } from '../../../popover';
export interface InternalSelectionInst {
    isComposing: boolean;
    focus: () => void;
    focusInput: () => void;
    blur: () => void;
    blurInput: () => void;
    $el: HTMLElement;
}
declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    clsPrefix: {
        type: StringConstructor;
        required: true;
    };
    bordered: {
        type: PropType<boolean | undefined>;
        default: undefined;
    };
    active: BooleanConstructor;
    pattern: {
        type: StringConstructor;
        default: string;
    };
    placeholder: StringConstructor;
    selectedOption: {
        type: PropType<SelectBaseOption | null>;
        default: null;
    };
    selectedOptions: {
        type: PropType<SelectBaseOption[] | null>;
        default: null;
    };
    labelField: {
        type: StringConstructor;
        default: string;
    };
    valueField: {
        type: StringConstructor;
        default: string;
    };
    multiple: BooleanConstructor;
    filterable: BooleanConstructor;
    clearable: BooleanConstructor;
    disabled: BooleanConstructor;
    size: {
        type: PropType<"tiny" | "small" | "medium" | "large">;
        default: string;
    };
    loading: BooleanConstructor;
    autofocus: BooleanConstructor;
    showArrow: {
        type: BooleanConstructor;
        default: boolean;
    };
    inputProps: PropType<InputHTMLAttributes>;
    focused: BooleanConstructor;
    renderTag: PropType<RenderTag>;
    onKeydown: PropType<(e: KeyboardEvent) => void>;
    onClick: PropType<(e: MouseEvent) => void>;
    onBlur: PropType<(e: FocusEvent) => void>;
    onFocus: PropType<(e: FocusEvent) => void>;
    onDeleteOption: PropType<(option: SelectBaseOption) => void>;
    maxTagCount: PropType<number | "responsive">;
    ellipsisTagPopoverProps: PropType<PopoverProps>;
    onClear: PropType<(e: MouseEvent) => void>;
    onPatternInput: PropType<(e: InputEvent) => void>;
    onPatternFocus: PropType<(e: FocusEvent) => void>;
    onPatternBlur: PropType<(e: FocusEvent) => void>;
    renderLabel: PropType<RenderLabel>;
    status: PropType<FormValidationStatus>;
    inlineThemeDisabled: BooleanConstructor;
    ignoreComposition: {
        type: BooleanConstructor;
        default: boolean;
    };
    onResize: PropType<() => void>;
    theme: PropType<import("../../../_mixins").Theme<"InternalSelection", {
        fontSizeTiny: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        heightTiny: string;
        heightSmall: string;
        heightMedium: string;
        heightLarge: string;
        borderRadius: string;
        fontWeight: string;
        textColor: string;
        textColorDisabled: string;
        placeholderColor: string;
        placeholderColorDisabled: string;
        color: string;
        colorDisabled: string;
        colorActive: string;
        border: string;
        borderHover: string;
        borderActive: string;
        borderFocus: string;
        boxShadowHover: string;
        boxShadowActive: string;
        boxShadowFocus: string;
        caretColor: string;
        arrowColor: string;
        arrowColorDisabled: string;
        loadingColor: string;
        borderWarning: string;
        borderHoverWarning: string;
        borderActiveWarning: string;
        borderFocusWarning: string;
        boxShadowHoverWarning: string;
        boxShadowActiveWarning: string;
        boxShadowFocusWarning: string;
        colorActiveWarning: string;
        caretColorWarning: string;
        borderError: string;
        borderHoverError: string;
        borderActiveError: string;
        borderFocusError: string;
        boxShadowHoverError: string;
        boxShadowActiveError: string;
        boxShadowFocusError: string;
        colorActiveError: string;
        caretColorError: string;
        clearColor: string;
        clearColorHover: string;
        clearColorPressed: string;
        paddingSingle: string;
        paddingMultiple: string;
        clearSize: string;
        arrowSize: string;
    }, {
        Popover: import("../../../_mixins").Theme<"Popover", {
            fontSize: string;
            borderRadius: string;
            color: string;
            dividerColor: string;
            textColor: string;
            boxShadow: string;
            space: string;
            spaceArrow: string;
            arrowOffset: string;
            arrowOffsetVertical: string;
            arrowHeight: string;
            padding: string;
        }, any>;
    }>>;
    themeOverrides: PropType<import("../../../_mixins/use-theme").ExtractThemeOverrides<import("../../../_mixins").Theme<"InternalSelection", {
        fontSizeTiny: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        heightTiny: string;
        heightSmall: string;
        heightMedium: string;
        heightLarge: string;
        borderRadius: string;
        fontWeight: string;
        textColor: string;
        textColorDisabled: string;
        placeholderColor: string;
        placeholderColorDisabled: string;
        color: string;
        colorDisabled: string;
        colorActive: string;
        border: string;
        borderHover: string;
        borderActive: string;
        borderFocus: string;
        boxShadowHover: string;
        boxShadowActive: string;
        boxShadowFocus: string;
        caretColor: string;
        arrowColor: string;
        arrowColorDisabled: string;
        loadingColor: string;
        borderWarning: string;
        borderHoverWarning: string;
        borderActiveWarning: string;
        borderFocusWarning: string;
        boxShadowHoverWarning: string;
        boxShadowActiveWarning: string;
        boxShadowFocusWarning: string;
        colorActiveWarning: string;
        caretColorWarning: string;
        borderError: string;
        borderHoverError: string;
        borderActiveError: string;
        borderFocusError: string;
        boxShadowHoverError: string;
        boxShadowActiveError: string;
        boxShadowFocusError: string;
        colorActiveError: string;
        caretColorError: string;
        clearColor: string;
        clearColorHover: string;
        clearColorPressed: string;
        paddingSingle: string;
        paddingMultiple: string;
        clearSize: string;
        arrowSize: string;
    }, {
        Popover: import("../../../_mixins").Theme<"Popover", {
            fontSize: string;
            borderRadius: string;
            color: string;
            dividerColor: string;
            textColor: string;
            boxShadow: string;
            space: string;
            spaceArrow: string;
            arrowOffset: string;
            arrowOffsetVertical: string;
            arrowHeight: string;
            padding: string;
        }, any>;
    }>>>;
    builtinThemeOverrides: PropType<import("../../../_mixins/use-theme").ExtractThemeOverrides<import("../../../_mixins").Theme<"InternalSelection", {
        fontSizeTiny: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        heightTiny: string;
        heightSmall: string;
        heightMedium: string;
        heightLarge: string;
        borderRadius: string;
        fontWeight: string;
        textColor: string;
        textColorDisabled: string;
        placeholderColor: string;
        placeholderColorDisabled: string;
        color: string;
        colorDisabled: string;
        colorActive: string;
        border: string;
        borderHover: string;
        borderActive: string;
        borderFocus: string;
        boxShadowHover: string;
        boxShadowActive: string;
        boxShadowFocus: string;
        caretColor: string;
        arrowColor: string;
        arrowColorDisabled: string;
        loadingColor: string;
        borderWarning: string;
        borderHoverWarning: string;
        borderActiveWarning: string;
        borderFocusWarning: string;
        boxShadowHoverWarning: string;
        boxShadowActiveWarning: string;
        boxShadowFocusWarning: string;
        colorActiveWarning: string;
        caretColorWarning: string;
        borderError: string;
        borderHoverError: string;
        borderActiveError: string;
        borderFocusError: string;
        boxShadowHoverError: string;
        boxShadowActiveError: string;
        boxShadowFocusError: string;
        colorActiveError: string;
        caretColorError: string;
        clearColor: string;
        clearColorHover: string;
        clearColorPressed: string;
        paddingSingle: string;
        paddingMultiple: string;
        clearSize: string;
        arrowSize: string;
    }, {
        Popover: import("../../../_mixins").Theme<"Popover", {
            fontSize: string;
            borderRadius: string;
            color: string;
            dividerColor: string;
            textColor: string;
            boxShadow: string;
            space: string;
            spaceArrow: string;
            arrowOffset: string;
            arrowOffsetVertical: string;
            arrowHeight: string;
            padding: string;
        }, any>;
    }>>>;
}>, {
    mergedTheme: import("vue").ComputedRef<{
        common: import("../../..").ThemeCommonVars;
        self: {
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            heightTiny: string;
            heightSmall: string;
            heightMedium: string;
            heightLarge: string;
            borderRadius: string;
            fontWeight: string;
            textColor: string;
            textColorDisabled: string;
            placeholderColor: string;
            placeholderColorDisabled: string;
            color: string;
            colorDisabled: string;
            colorActive: string;
            border: string;
            borderHover: string;
            borderActive: string;
            borderFocus: string;
            boxShadowHover: string;
            boxShadowActive: string;
            boxShadowFocus: string;
            caretColor: string;
            arrowColor: string;
            arrowColorDisabled: string;
            loadingColor: string;
            borderWarning: string;
            borderHoverWarning: string;
            borderActiveWarning: string;
            borderFocusWarning: string;
            boxShadowHoverWarning: string;
            boxShadowActiveWarning: string;
            boxShadowFocusWarning: string;
            colorActiveWarning: string;
            caretColorWarning: string;
            borderError: string;
            borderHoverError: string;
            borderActiveError: string;
            borderFocusError: string;
            boxShadowHoverError: string;
            boxShadowActiveError: string;
            boxShadowFocusError: string;
            colorActiveError: string;
            caretColorError: string;
            clearColor: string;
            clearColorHover: string;
            clearColorPressed: string;
            paddingSingle: string;
            paddingMultiple: string;
            clearSize: string;
            arrowSize: string;
        };
        peers: {
            Popover: import("../../../_mixins").Theme<"Popover", {
                fontSize: string;
                borderRadius: string;
                color: string;
                dividerColor: string;
                textColor: string;
                boxShadow: string;
                space: string;
                spaceArrow: string;
                arrowOffset: string;
                arrowOffsetVertical: string;
                arrowHeight: string;
                padding: string;
            }, any>;
        };
        peerOverrides: {
            Popover?: {
                peers?: {
                    [x: string]: any;
                } | undefined;
            } | undefined;
        };
    }>;
    mergedClearable: import("vue").ComputedRef<boolean>;
    mergedClsPrefix: import("vue").Ref<string, string>;
    rtlEnabled: import("vue").Ref<import("../../../config-provider/src/internal-interface").RtlItem | undefined, import("../../../config-provider/src/internal-interface").RtlItem | undefined> | undefined;
    patternInputFocused: import("vue").Ref<boolean, boolean>;
    filterablePlaceholder: import("vue").ComputedRef<import("vue").VNodeChild>;
    label: import("vue").ComputedRef<unknown>;
    selected: import("vue").ComputedRef<boolean>;
    showTagsPanel: import("vue").Ref<boolean, boolean>;
    isComposing: import("vue").Ref<boolean, boolean>;
    counterRef: import("vue").Ref<{
        $el: HTMLElement;
        setTextContent: (textContent: string) => void;
    } | null, TagRef | {
        $el: HTMLElement;
        setTextContent: (textContent: string) => void;
    } | null>;
    counterWrapperRef: import("vue").Ref<HTMLElement | null, HTMLElement | null>;
    patternInputMirrorRef: import("vue").Ref<HTMLElement | null, HTMLElement | null>;
    patternInputRef: import("vue").Ref<HTMLElement | null, HTMLElement | null>;
    selfRef: import("vue").Ref<HTMLElement | null, HTMLElement | null>;
    multipleElRef: import("vue").Ref<HTMLElement | null, HTMLElement | null>;
    singleElRef: import("vue").Ref<HTMLElement | null, HTMLElement | null>;
    patternInputWrapperRef: import("vue").Ref<HTMLElement | null, HTMLElement | null>;
    overflowRef: import("vue").Ref<{
        sync: (options: {
            showAllItemsBeforeCalculate: boolean;
        }) => void;
    } | null, VOverflowInst | {
        sync: (options: {
            showAllItemsBeforeCalculate: boolean;
        }) => void;
    } | null>;
    inputTagElRef: import("vue").Ref<HTMLElement | null, HTMLElement | null>;
    handleMouseDown: (e: MouseEvent) => void;
    handleFocusin: (e: FocusEvent) => void;
    handleClear: (e: MouseEvent) => void;
    handleMouseEnter: () => void;
    handleMouseLeave: () => void;
    handleDeleteOption: (option: SelectBaseOption) => void;
    handlePatternKeyDown: (e: KeyboardEvent) => void;
    handlePatternInputInput: (e: InputEvent) => void;
    handlePatternInputBlur: (e: FocusEvent) => void;
    handlePatternInputFocus: (e: FocusEvent) => void;
    handleMouseEnterCounter: () => void;
    handleMouseLeaveCounter: () => void;
    handleFocusout: (e: FocusEvent) => void;
    handleCompositionEnd: () => void;
    handleCompositionStart: () => void;
    onPopoverUpdateShow: (show: boolean) => void;
    focus: () => void;
    focusInput: () => void;
    blur: () => void;
    blurInput: () => void;
    updateCounter: (count: number) => void;
    getCounter: () => HTMLElement | null;
    getTail: () => HTMLElement | null;
    renderLabel: RenderLabelImpl;
    cssVars: import("vue").ComputedRef<{
        '--n-bezier': string;
        '--n-border': string;
        '--n-border-active': string;
        '--n-border-focus': string;
        '--n-border-hover': string;
        '--n-border-radius': string;
        '--n-box-shadow-active': string;
        '--n-box-shadow-focus': string;
        '--n-box-shadow-hover': string;
        '--n-caret-color': string;
        '--n-color': string;
        '--n-color-active': string;
        '--n-color-disabled': string;
        '--n-font-size': string;
        '--n-height': string;
        '--n-padding-single-top': string;
        '--n-padding-multiple-top': string;
        '--n-padding-single-right': string;
        '--n-padding-multiple-right': string;
        '--n-padding-single-left': string;
        '--n-padding-multiple-left': string;
        '--n-padding-single-bottom': string;
        '--n-padding-multiple-bottom': string;
        '--n-placeholder-color': string;
        '--n-placeholder-color-disabled': string;
        '--n-text-color': string;
        '--n-text-color-disabled': string;
        '--n-arrow-color': string;
        '--n-arrow-color-disabled': string;
        '--n-loading-color': string;
        '--n-color-active-warning': string;
        '--n-box-shadow-focus-warning': string;
        '--n-box-shadow-active-warning': string;
        '--n-box-shadow-hover-warning': string;
        '--n-border-warning': string;
        '--n-border-focus-warning': string;
        '--n-border-hover-warning': string;
        '--n-border-active-warning': string;
        '--n-color-active-error': string;
        '--n-box-shadow-focus-error': string;
        '--n-box-shadow-active-error': string;
        '--n-box-shadow-hover-error': string;
        '--n-border-error': string;
        '--n-border-focus-error': string;
        '--n-border-hover-error': string;
        '--n-border-active-error': string;
        '--n-clear-size': string;
        '--n-clear-color': string;
        '--n-clear-color-hover': string;
        '--n-clear-color-pressed': string;
        '--n-arrow-size': string;
        '--n-font-weight': string;
    }> | undefined;
    themeClass: import("vue").Ref<string, string> | undefined;
    onRender: (() => void) | undefined;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    clsPrefix: {
        type: StringConstructor;
        required: true;
    };
    bordered: {
        type: PropType<boolean | undefined>;
        default: undefined;
    };
    active: BooleanConstructor;
    pattern: {
        type: StringConstructor;
        default: string;
    };
    placeholder: StringConstructor;
    selectedOption: {
        type: PropType<SelectBaseOption | null>;
        default: null;
    };
    selectedOptions: {
        type: PropType<SelectBaseOption[] | null>;
        default: null;
    };
    labelField: {
        type: StringConstructor;
        default: string;
    };
    valueField: {
        type: StringConstructor;
        default: string;
    };
    multiple: BooleanConstructor;
    filterable: BooleanConstructor;
    clearable: BooleanConstructor;
    disabled: BooleanConstructor;
    size: {
        type: PropType<"tiny" | "small" | "medium" | "large">;
        default: string;
    };
    loading: BooleanConstructor;
    autofocus: BooleanConstructor;
    showArrow: {
        type: BooleanConstructor;
        default: boolean;
    };
    inputProps: PropType<InputHTMLAttributes>;
    focused: BooleanConstructor;
    renderTag: PropType<RenderTag>;
    onKeydown: PropType<(e: KeyboardEvent) => void>;
    onClick: PropType<(e: MouseEvent) => void>;
    onBlur: PropType<(e: FocusEvent) => void>;
    onFocus: PropType<(e: FocusEvent) => void>;
    onDeleteOption: PropType<(option: SelectBaseOption) => void>;
    maxTagCount: PropType<number | "responsive">;
    ellipsisTagPopoverProps: PropType<PopoverProps>;
    onClear: PropType<(e: MouseEvent) => void>;
    onPatternInput: PropType<(e: InputEvent) => void>;
    onPatternFocus: PropType<(e: FocusEvent) => void>;
    onPatternBlur: PropType<(e: FocusEvent) => void>;
    renderLabel: PropType<RenderLabel>;
    status: PropType<FormValidationStatus>;
    inlineThemeDisabled: BooleanConstructor;
    ignoreComposition: {
        type: BooleanConstructor;
        default: boolean;
    };
    onResize: PropType<() => void>;
    theme: PropType<import("../../../_mixins").Theme<"InternalSelection", {
        fontSizeTiny: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        heightTiny: string;
        heightSmall: string;
        heightMedium: string;
        heightLarge: string;
        borderRadius: string;
        fontWeight: string;
        textColor: string;
        textColorDisabled: string;
        placeholderColor: string;
        placeholderColorDisabled: string;
        color: string;
        colorDisabled: string;
        colorActive: string;
        border: string;
        borderHover: string;
        borderActive: string;
        borderFocus: string;
        boxShadowHover: string;
        boxShadowActive: string;
        boxShadowFocus: string;
        caretColor: string;
        arrowColor: string;
        arrowColorDisabled: string;
        loadingColor: string;
        borderWarning: string;
        borderHoverWarning: string;
        borderActiveWarning: string;
        borderFocusWarning: string;
        boxShadowHoverWarning: string;
        boxShadowActiveWarning: string;
        boxShadowFocusWarning: string;
        colorActiveWarning: string;
        caretColorWarning: string;
        borderError: string;
        borderHoverError: string;
        borderActiveError: string;
        borderFocusError: string;
        boxShadowHoverError: string;
        boxShadowActiveError: string;
        boxShadowFocusError: string;
        colorActiveError: string;
        caretColorError: string;
        clearColor: string;
        clearColorHover: string;
        clearColorPressed: string;
        paddingSingle: string;
        paddingMultiple: string;
        clearSize: string;
        arrowSize: string;
    }, {
        Popover: import("../../../_mixins").Theme<"Popover", {
            fontSize: string;
            borderRadius: string;
            color: string;
            dividerColor: string;
            textColor: string;
            boxShadow: string;
            space: string;
            spaceArrow: string;
            arrowOffset: string;
            arrowOffsetVertical: string;
            arrowHeight: string;
            padding: string;
        }, any>;
    }>>;
    themeOverrides: PropType<import("../../../_mixins/use-theme").ExtractThemeOverrides<import("../../../_mixins").Theme<"InternalSelection", {
        fontSizeTiny: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        heightTiny: string;
        heightSmall: string;
        heightMedium: string;
        heightLarge: string;
        borderRadius: string;
        fontWeight: string;
        textColor: string;
        textColorDisabled: string;
        placeholderColor: string;
        placeholderColorDisabled: string;
        color: string;
        colorDisabled: string;
        colorActive: string;
        border: string;
        borderHover: string;
        borderActive: string;
        borderFocus: string;
        boxShadowHover: string;
        boxShadowActive: string;
        boxShadowFocus: string;
        caretColor: string;
        arrowColor: string;
        arrowColorDisabled: string;
        loadingColor: string;
        borderWarning: string;
        borderHoverWarning: string;
        borderActiveWarning: string;
        borderFocusWarning: string;
        boxShadowHoverWarning: string;
        boxShadowActiveWarning: string;
        boxShadowFocusWarning: string;
        colorActiveWarning: string;
        caretColorWarning: string;
        borderError: string;
        borderHoverError: string;
        borderActiveError: string;
        borderFocusError: string;
        boxShadowHoverError: string;
        boxShadowActiveError: string;
        boxShadowFocusError: string;
        colorActiveError: string;
        caretColorError: string;
        clearColor: string;
        clearColorHover: string;
        clearColorPressed: string;
        paddingSingle: string;
        paddingMultiple: string;
        clearSize: string;
        arrowSize: string;
    }, {
        Popover: import("../../../_mixins").Theme<"Popover", {
            fontSize: string;
            borderRadius: string;
            color: string;
            dividerColor: string;
            textColor: string;
            boxShadow: string;
            space: string;
            spaceArrow: string;
            arrowOffset: string;
            arrowOffsetVertical: string;
            arrowHeight: string;
            padding: string;
        }, any>;
    }>>>;
    builtinThemeOverrides: PropType<import("../../../_mixins/use-theme").ExtractThemeOverrides<import("../../../_mixins").Theme<"InternalSelection", {
        fontSizeTiny: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        heightTiny: string;
        heightSmall: string;
        heightMedium: string;
        heightLarge: string;
        borderRadius: string;
        fontWeight: string;
        textColor: string;
        textColorDisabled: string;
        placeholderColor: string;
        placeholderColorDisabled: string;
        color: string;
        colorDisabled: string;
        colorActive: string;
        border: string;
        borderHover: string;
        borderActive: string;
        borderFocus: string;
        boxShadowHover: string;
        boxShadowActive: string;
        boxShadowFocus: string;
        caretColor: string;
        arrowColor: string;
        arrowColorDisabled: string;
        loadingColor: string;
        borderWarning: string;
        borderHoverWarning: string;
        borderActiveWarning: string;
        borderFocusWarning: string;
        boxShadowHoverWarning: string;
        boxShadowActiveWarning: string;
        boxShadowFocusWarning: string;
        colorActiveWarning: string;
        caretColorWarning: string;
        borderError: string;
        borderHoverError: string;
        borderActiveError: string;
        borderFocusError: string;
        boxShadowHoverError: string;
        boxShadowActiveError: string;
        boxShadowFocusError: string;
        colorActiveError: string;
        caretColorError: string;
        clearColor: string;
        clearColorHover: string;
        clearColorPressed: string;
        paddingSingle: string;
        paddingMultiple: string;
        clearSize: string;
        arrowSize: string;
    }, {
        Popover: import("../../../_mixins").Theme<"Popover", {
            fontSize: string;
            borderRadius: string;
            color: string;
            dividerColor: string;
            textColor: string;
            boxShadow: string;
            space: string;
            spaceArrow: string;
            arrowOffset: string;
            arrowOffsetVertical: string;
            arrowHeight: string;
            padding: string;
        }, any>;
    }>>>;
}>> & Readonly<{}>, {
    pattern: string;
    size: "small" | "medium" | "large" | "tiny";
    disabled: boolean;
    multiple: boolean;
    active: boolean;
    autofocus: boolean;
    loading: boolean;
    bordered: boolean | undefined;
    inlineThemeDisabled: boolean;
    labelField: string;
    valueField: string;
    showArrow: boolean;
    filterable: boolean;
    clearable: boolean;
    focused: boolean;
    selectedOption: SelectBaseOption<string | number, string | ((option: SelectBaseOption<string | number, string | any>, selected: boolean) => import("vue").VNodeChild)> | null;
    selectedOptions: SelectBaseOption<string | number, string | ((option: SelectBaseOption<string | number, string | any>, selected: boolean) => import("vue").VNodeChild)>[] | null;
    ignoreComposition: boolean;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;

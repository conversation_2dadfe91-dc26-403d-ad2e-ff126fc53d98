import type { ThemeCommonVars } from '../../_styles/common';
export declare function createPartialInvertedVars(color: string, activeItemColor: string, activeTextColor: string, groupTextColor: string): {
    itemColorHoverInverted: string;
    itemColorActiveInverted: string;
    itemColorActiveHoverInverted: string;
    itemColorActiveCollapsedInverted: string;
    itemTextColorInverted: string;
    itemTextColorHoverInverted: string;
    itemTextColorChildActiveInverted: string;
    itemTextColorChildActiveHoverInverted: string;
    itemTextColorActiveInverted: string;
    itemTextColorActiveHoverInverted: string;
    itemTextColorHorizontalInverted: string;
    itemTextColorHoverHorizontalInverted: string;
    itemTextColorChildActiveHorizontalInverted: string;
    itemTextColorChildActiveHoverHorizontalInverted: string;
    itemTextColorActiveHorizontalInverted: string;
    itemTextColorActiveHoverHorizontalInverted: string;
    itemIconColorInverted: string;
    itemIconColorHoverInverted: string;
    itemIconColorActiveInverted: string;
    itemIconColorActiveHoverInverted: string;
    itemIconColorChildActiveInverted: string;
    itemIconColorChildActiveHoverInverted: string;
    itemIconColorCollapsedInverted: string;
    itemIconColorHorizontalInverted: string;
    itemIconColorHoverHorizontalInverted: string;
    itemIconColorActiveHorizontalInverted: string;
    itemIconColorActiveHoverHorizontalInverted: string;
    itemIconColorChildActiveHorizontalInverted: string;
    itemIconColorChildActiveHoverHorizontalInverted: string;
    arrowColorInverted: string;
    arrowColorHoverInverted: string;
    arrowColorActiveInverted: string;
    arrowColorActiveHoverInverted: string;
    arrowColorChildActiveInverted: string;
    arrowColorChildActiveHoverInverted: string;
    groupTextColorInverted: string;
};
export declare function self(vars: ThemeCommonVars): {
    itemColorHoverInverted: string;
    itemColorActiveInverted: string;
    itemColorActiveHoverInverted: string;
    itemColorActiveCollapsedInverted: string;
    itemTextColorInverted: string;
    itemTextColorHoverInverted: string;
    itemTextColorChildActiveInverted: string;
    itemTextColorChildActiveHoverInverted: string;
    itemTextColorActiveInverted: string;
    itemTextColorActiveHoverInverted: string;
    itemTextColorHorizontalInverted: string;
    itemTextColorHoverHorizontalInverted: string;
    itemTextColorChildActiveHorizontalInverted: string;
    itemTextColorChildActiveHoverHorizontalInverted: string;
    itemTextColorActiveHorizontalInverted: string;
    itemTextColorActiveHoverHorizontalInverted: string;
    itemIconColorInverted: string;
    itemIconColorHoverInverted: string;
    itemIconColorActiveInverted: string;
    itemIconColorActiveHoverInverted: string;
    itemIconColorChildActiveInverted: string;
    itemIconColorChildActiveHoverInverted: string;
    itemIconColorCollapsedInverted: string;
    itemIconColorHorizontalInverted: string;
    itemIconColorHoverHorizontalInverted: string;
    itemIconColorActiveHorizontalInverted: string;
    itemIconColorActiveHoverHorizontalInverted: string;
    itemIconColorChildActiveHorizontalInverted: string;
    itemIconColorChildActiveHoverHorizontalInverted: string;
    arrowColorInverted: string;
    arrowColorHoverInverted: string;
    arrowColorActiveInverted: string;
    arrowColorActiveHoverInverted: string;
    arrowColorChildActiveInverted: string;
    arrowColorChildActiveHoverInverted: string;
    groupTextColorInverted: string;
    borderRadius: string;
    color: string;
    groupTextColor: string;
    itemColorHover: string;
    itemColorActive: string;
    itemColorActiveHover: string;
    itemColorActiveCollapsed: string;
    itemTextColor: string;
    itemTextColorHover: string;
    itemTextColorActive: string;
    itemTextColorActiveHover: string;
    itemTextColorChildActive: string;
    itemTextColorChildActiveHover: string;
    itemTextColorHorizontal: string;
    itemTextColorHoverHorizontal: string;
    itemTextColorActiveHorizontal: string;
    itemTextColorActiveHoverHorizontal: string;
    itemTextColorChildActiveHorizontal: string;
    itemTextColorChildActiveHoverHorizontal: string;
    itemIconColor: string;
    itemIconColorHover: string;
    itemIconColorActive: string;
    itemIconColorActiveHover: string;
    itemIconColorChildActive: string;
    itemIconColorChildActiveHover: string;
    itemIconColorCollapsed: string;
    itemIconColorHorizontal: string;
    itemIconColorHoverHorizontal: string;
    itemIconColorActiveHorizontal: string;
    itemIconColorActiveHoverHorizontal: string;
    itemIconColorChildActiveHorizontal: string;
    itemIconColorChildActiveHoverHorizontal: string;
    itemHeight: string;
    arrowColor: string;
    arrowColorHover: string;
    arrowColorActive: string;
    arrowColorActiveHover: string;
    arrowColorChildActive: string;
    arrowColorChildActiveHover: string;
    colorInverted: string;
    borderColorHorizontal: string;
    fontSize: string;
    dividerColor: string;
};
export type MenuThemeVars = ReturnType<typeof self>;
declare const menuLight: import("../../_mixins/use-theme").Theme<"Menu", {
    itemColorHoverInverted: string;
    itemColorActiveInverted: string;
    itemColorActiveHoverInverted: string;
    itemColorActiveCollapsedInverted: string;
    itemTextColorInverted: string;
    itemTextColorHoverInverted: string;
    itemTextColorChildActiveInverted: string;
    itemTextColorChildActiveHoverInverted: string;
    itemTextColorActiveInverted: string;
    itemTextColorActiveHoverInverted: string;
    itemTextColorHorizontalInverted: string;
    itemTextColorHoverHorizontalInverted: string;
    itemTextColorChildActiveHorizontalInverted: string;
    itemTextColorChildActiveHoverHorizontalInverted: string;
    itemTextColorActiveHorizontalInverted: string;
    itemTextColorActiveHoverHorizontalInverted: string;
    itemIconColorInverted: string;
    itemIconColorHoverInverted: string;
    itemIconColorActiveInverted: string;
    itemIconColorActiveHoverInverted: string;
    itemIconColorChildActiveInverted: string;
    itemIconColorChildActiveHoverInverted: string;
    itemIconColorCollapsedInverted: string;
    itemIconColorHorizontalInverted: string;
    itemIconColorHoverHorizontalInverted: string;
    itemIconColorActiveHorizontalInverted: string;
    itemIconColorActiveHoverHorizontalInverted: string;
    itemIconColorChildActiveHorizontalInverted: string;
    itemIconColorChildActiveHoverHorizontalInverted: string;
    arrowColorInverted: string;
    arrowColorHoverInverted: string;
    arrowColorActiveInverted: string;
    arrowColorActiveHoverInverted: string;
    arrowColorChildActiveInverted: string;
    arrowColorChildActiveHoverInverted: string;
    groupTextColorInverted: string;
    borderRadius: string;
    color: string;
    groupTextColor: string;
    itemColorHover: string;
    itemColorActive: string;
    itemColorActiveHover: string;
    itemColorActiveCollapsed: string;
    itemTextColor: string;
    itemTextColorHover: string;
    itemTextColorActive: string;
    itemTextColorActiveHover: string;
    itemTextColorChildActive: string;
    itemTextColorChildActiveHover: string;
    itemTextColorHorizontal: string;
    itemTextColorHoverHorizontal: string;
    itemTextColorActiveHorizontal: string;
    itemTextColorActiveHoverHorizontal: string;
    itemTextColorChildActiveHorizontal: string;
    itemTextColorChildActiveHoverHorizontal: string;
    itemIconColor: string;
    itemIconColorHover: string;
    itemIconColorActive: string;
    itemIconColorActiveHover: string;
    itemIconColorChildActive: string;
    itemIconColorChildActiveHover: string;
    itemIconColorCollapsed: string;
    itemIconColorHorizontal: string;
    itemIconColorHoverHorizontal: string;
    itemIconColorActiveHorizontal: string;
    itemIconColorActiveHoverHorizontal: string;
    itemIconColorChildActiveHorizontal: string;
    itemIconColorChildActiveHoverHorizontal: string;
    itemHeight: string;
    arrowColor: string;
    arrowColorHover: string;
    arrowColorActive: string;
    arrowColorActiveHover: string;
    arrowColorChildActive: string;
    arrowColorChildActiveHover: string;
    colorInverted: string;
    borderColorHorizontal: string;
    fontSize: string;
    dividerColor: string;
}, {
    Tooltip: import("../../_mixins/use-theme").Theme<"Tooltip", {
        borderRadius: string;
        boxShadow: string;
        color: string;
        textColor: string;
        padding: string;
    }, {
        Popover: import("../../_mixins/use-theme").Theme<"Popover", {
            fontSize: string;
            borderRadius: string;
            color: string;
            dividerColor: string;
            textColor: string;
            boxShadow: string;
            space: string;
            spaceArrow: string;
            arrowOffset: string;
            arrowOffsetVertical: string;
            arrowHeight: string;
            padding: string;
        }, any>;
    }>;
    Dropdown: import("../../_mixins/use-theme").Theme<"Dropdown", {
        optionHeightSmall: string;
        optionHeightMedium: string;
        optionHeightLarge: string;
        optionHeightHuge: string;
        borderRadius: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        fontSizeHuge: string;
        optionTextColor: string;
        optionTextColorHover: string;
        optionTextColorActive: string;
        optionTextColorChildActive: string;
        color: string;
        dividerColor: string;
        suffixColor: string;
        prefixColor: string;
        optionColorHover: string;
        optionColorActive: string;
        groupHeaderTextColor: string;
        optionTextColorInverted: string;
        optionTextColorHoverInverted: string;
        optionTextColorActiveInverted: string;
        optionTextColorChildActiveInverted: string;
        colorInverted: string;
        dividerColorInverted: string;
        suffixColorInverted: string;
        prefixColorInverted: string;
        optionColorHoverInverted: string;
        optionColorActiveInverted: string;
        groupHeaderTextColorInverted: string;
        optionOpacityDisabled: string;
        padding: string;
        optionIconSizeSmall: string;
        optionIconSizeMedium: string;
        optionIconSizeLarge: string;
        optionIconSizeHuge: string;
        optionSuffixWidthSmall: string;
        optionSuffixWidthMedium: string;
        optionSuffixWidthLarge: string;
        optionSuffixWidthHuge: string;
        optionIconSuffixWidthSmall: string;
        optionIconSuffixWidthMedium: string;
        optionIconSuffixWidthLarge: string;
        optionIconSuffixWidthHuge: string;
        optionPrefixWidthSmall: string;
        optionPrefixWidthMedium: string;
        optionPrefixWidthLarge: string;
        optionPrefixWidthHuge: string;
        optionIconPrefixWidthSmall: string;
        optionIconPrefixWidthMedium: string;
        optionIconPrefixWidthLarge: string;
        optionIconPrefixWidthHuge: string;
    }, {
        Popover: import("../../_mixins/use-theme").Theme<"Popover", {
            fontSize: string;
            borderRadius: string;
            color: string;
            dividerColor: string;
            textColor: string;
            boxShadow: string;
            space: string;
            spaceArrow: string;
            arrowOffset: string;
            arrowOffsetVertical: string;
            arrowHeight: string;
            padding: string;
        }, any>;
    }>;
}>;
export default menuLight;
export type MenuTheme = typeof menuLight;
